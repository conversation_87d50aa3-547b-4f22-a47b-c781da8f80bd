const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function checkMigration() {
  try {
    // Try to select the new columns to see if they exist
    const { data, error } = await supabase
      .from('prescriptions')
      .select('ai_extracted_medicines, ai_extraction_success, extracted_text, filename')
      .limit(1);

    if (error) {
      console.log('❌ Migration needed. New columns do not exist:', error.message);
      console.log('\n📋 To run the migration:');
      console.log('1. Connect to your Supabase database');
      console.log('2. Execute the SQL in: backend/migrations/add_ai_medicine_extraction.sql');
      return false;
    } else {
      console.log('✅ Migration already applied. New columns exist.');
      console.log('📊 Sample data structure:', data);
      return true;
    }
  } catch (err) {
    console.error('❌ Error checking migration:', err.message);
    return false;
  }
}

checkMigration();
