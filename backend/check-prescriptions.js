const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function checkPrescriptions() {
  try {
    console.log('🔍 Checking prescriptions in database...\n');
    
    // Get all prescriptions with the new AI fields
    const { data, error } = await supabase
      .from('prescriptions')
      .select(`
        id,
        filename,
        status,
        extracted_text,
        ai_extracted_medicines,
        ai_extraction_success,
        created_at,
        updated_at
      `)
      .order('created_at', { ascending: false })
      .limit(5);

    if (error) {
      console.error('❌ Error fetching prescriptions:', error);
      return;
    }

    if (!data || data.length === 0) {
      console.log('📭 No prescriptions found in database');
      return;
    }

    console.log(`📊 Found ${data.length} prescriptions:\n`);
    
    data.forEach((prescription, index) => {
      console.log(`--- Prescription ${index + 1} ---`);
      console.log(`ID: ${prescription.id}`);
      console.log(`Filename: ${prescription.filename || 'NULL'}`);
      console.log(`Status: ${prescription.status}`);
      console.log(`AI Extraction Success: ${prescription.ai_extraction_success}`);
      console.log(`AI Extracted Medicines: ${prescription.ai_extracted_medicines ? JSON.stringify(prescription.ai_extracted_medicines) : 'NULL'}`);
      console.log(`Extracted Text Length: ${prescription.extracted_text ? prescription.extracted_text.length : 0} characters`);
      console.log(`Created: ${prescription.created_at}`);
      console.log(`Updated: ${prescription.updated_at}`);
      console.log('');
    });

  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

checkPrescriptions();
