const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.SUPABASE_ANON_KEY
);

async function checkTableSchema() {
  try {
    console.log('🔍 Checking prescriptions table schema...\n');

    // Query the information schema to get column details
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: `
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'prescriptions' 
        ORDER BY ordinal_position;
      `
    });

    if (error) {
      console.log('❌ Schema query failed, trying alternative approach...');
      
      // Alternative: Try to insert a test record to see what columns are expected
      const { data: testData, error: testError } = await supabase
        .from('prescriptions')
        .insert({
          patient_id: 'test',
          filename: 'test.pdf',
          file_url: 'test-url',
          status: 'processing'
        })
        .select();

      if (testError) {
        console.log('📋 Insert test error (shows expected columns):', testError.message);
        
        // Try to get existing data to see structure
        const { data: existingData, error: selectError } = await supabase
          .from('prescriptions')
          .select('*')
          .limit(1);
          
        if (selectError) {
          console.log('❌ Select error:', selectError.message);
        } else {
          console.log('✅ Table exists, sample structure:', existingData);
        }
      } else {
        console.log('✅ Test insert successful:', testData);
        // Clean up test record
        await supabase.from('prescriptions').delete().eq('patient_id', 'test');
      }
    } else {
      console.log('✅ Schema query successful:');
      console.table(data);
    }

  } catch (err) {
    console.error('❌ Error:', err.message);
  }
}

checkTableSchema();
