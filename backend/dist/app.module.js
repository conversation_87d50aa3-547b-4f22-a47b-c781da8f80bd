"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const schedule_1 = require("@nestjs/schedule");
const throttler_1 = require("@nestjs/throttler");
const serve_static_1 = require("@nestjs/serve-static");
const path_1 = require("path");
const configuration_1 = require("./config/configuration");
const supabase_service_1 = require("./config/supabase.service");
const auth_module_1 = require("./modules/auth/auth.module");
const users_module_1 = require("./modules/users/users.module");
const medicines_module_1 = require("./modules/medicines/medicines.module");
const prescriptions_module_1 = require("./modules/prescriptions/prescriptions.module");
const reminders_module_1 = require("./modules/reminders/reminders.module");
const adherence_module_1 = require("./modules/adherence/adherence.module");
const gamification_module_1 = require("./modules/gamification/gamification.module");
const achievements_module_1 = require("./modules/achievements/achievements.module");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                load: [configuration_1.default],
                isGlobal: true,
                envFilePath: '.env',
            }),
            schedule_1.ScheduleModule.forRoot(),
            throttler_1.ThrottlerModule.forRootAsync({
                useFactory: () => ({
                    throttlers: [
                        {
                            ttl: parseInt(process.env.THROTTLE_TTL || '60', 10),
                            limit: parseInt(process.env.THROTTLE_LIMIT || '100', 10),
                        },
                    ],
                }),
            }),
            serve_static_1.ServeStaticModule.forRoot({
                rootPath: (0, path_1.join)(__dirname, '..', 'uploads'),
                serveRoot: '/uploads',
            }),
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            medicines_module_1.MedicinesModule,
            prescriptions_module_1.PrescriptionsModule,
            reminders_module_1.RemindersModule,
            adherence_module_1.AdherenceModule,
            gamification_module_1.GamificationModule,
            achievements_module_1.AchievementsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService, supabase_service_1.SupabaseService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map