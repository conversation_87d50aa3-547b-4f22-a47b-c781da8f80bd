export declare class ErrorResponseDto {
    message: string;
    error: string;
    statusCode: number;
    errorCode?: string;
    details?: any;
    timestamp: string;
    requestId?: string;
    path?: string;
}
export declare class ValidationErrorResponseDto extends ErrorResponseDto {
    validationErrors: ValidationErrorDetail[];
}
export interface ValidationErrorDetail {
    field: string;
    value: any;
    constraints: Record<string, string>;
    children?: ValidationErrorDetail[];
}
export declare class ExternalServiceErrorResponseDto extends ErrorResponseDto {
    service: string;
    retryAfter?: number;
}
export declare class DatabaseErrorResponseDto extends ErrorResponseDto {
    table?: string;
    constraint?: string;
    operation?: string;
}
export declare class RateLimitErrorResponseDto extends ErrorResponseDto {
    limit: number;
    windowMs: number;
    retryAfter: number;
    currentRequests: number;
}
