"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitErrorResponseDto = exports.DatabaseErrorResponseDto = exports.ExternalServiceErrorResponseDto = exports.ValidationErrorResponseDto = exports.ErrorResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class ErrorResponseDto {
    message;
    error;
    statusCode;
    errorCode;
    details;
    timestamp;
    requestId;
    path;
}
exports.ErrorResponseDto = ErrorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Error message',
        example: 'Resource not found',
    }),
    __metadata("design:type", String)
], ErrorResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Error type',
        example: 'Not Found',
    }),
    __metadata("design:type", String)
], ErrorResponseDto.prototype, "error", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'HTTP status code',
        example: 404,
    }),
    __metadata("design:type", Number)
], ErrorResponseDto.prototype, "statusCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Specific error code for client handling',
        example: 'RESOURCE_NOT_FOUND',
    }),
    __metadata("design:type", String)
], ErrorResponseDto.prototype, "errorCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional error details',
        example: { resource: 'User', identifier: '123' },
    }),
    __metadata("design:type", Object)
], ErrorResponseDto.prototype, "details", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp when the error occurred',
        example: '2024-01-15T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], ErrorResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Request ID for tracking',
        example: 'req_123456789',
    }),
    __metadata("design:type", String)
], ErrorResponseDto.prototype, "requestId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'API path where the error occurred',
        example: '/api/v1/users/123',
    }),
    __metadata("design:type", String)
], ErrorResponseDto.prototype, "path", void 0);
class ValidationErrorResponseDto extends ErrorResponseDto {
    validationErrors;
}
exports.ValidationErrorResponseDto = ValidationErrorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Detailed validation errors',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                field: {
                    type: 'string',
                    description: 'Field name that failed validation',
                    example: 'email',
                },
                value: {
                    description: 'Value that failed validation',
                    example: 'invalid-email',
                },
                constraints: {
                    type: 'object',
                    description: 'Validation constraints that were violated',
                    example: {
                        isEmail: 'email must be a valid email',
                        isNotEmpty: 'email should not be empty',
                    },
                },
                children: {
                    type: 'array',
                    description: 'Nested validation errors',
                    items: { $ref: '#/components/schemas/ValidationErrorDetail' },
                },
            },
        },
    }),
    __metadata("design:type", Array)
], ValidationErrorResponseDto.prototype, "validationErrors", void 0);
class ExternalServiceErrorResponseDto extends ErrorResponseDto {
    service;
    retryAfter;
}
exports.ExternalServiceErrorResponseDto = ExternalServiceErrorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'External service name',
        example: 'Twilio',
    }),
    __metadata("design:type", String)
], ExternalServiceErrorResponseDto.prototype, "service", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Retry after seconds (for rate limiting)',
        example: 60,
    }),
    __metadata("design:type", Number)
], ExternalServiceErrorResponseDto.prototype, "retryAfter", void 0);
class DatabaseErrorResponseDto extends ErrorResponseDto {
    table;
    constraint;
    operation;
}
exports.DatabaseErrorResponseDto = DatabaseErrorResponseDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Database table involved in the error',
        example: 'users',
    }),
    __metadata("design:type", String)
], DatabaseErrorResponseDto.prototype, "table", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Database constraint that was violated',
        example: 'unique_email',
    }),
    __metadata("design:type", String)
], DatabaseErrorResponseDto.prototype, "constraint", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Database operation that failed',
        example: 'INSERT',
    }),
    __metadata("design:type", String)
], DatabaseErrorResponseDto.prototype, "operation", void 0);
class RateLimitErrorResponseDto extends ErrorResponseDto {
    limit;
    windowMs;
    retryAfter;
    currentRequests;
}
exports.RateLimitErrorResponseDto = RateLimitErrorResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Rate limit that was exceeded',
        example: 100,
    }),
    __metadata("design:type", Number)
], RateLimitErrorResponseDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Time window in milliseconds',
        example: 60000,
    }),
    __metadata("design:type", Number)
], RateLimitErrorResponseDto.prototype, "windowMs", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Seconds until rate limit resets',
        example: 45,
    }),
    __metadata("design:type", Number)
], RateLimitErrorResponseDto.prototype, "retryAfter", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current request count in the window',
        example: 101,
    }),
    __metadata("design:type", Number)
], RateLimitErrorResponseDto.prototype, "currentRequests", void 0);
//# sourceMappingURL=error-response.dto.js.map