export declare class SuccessResponseDto<T = any> {
    message: string;
    data: T;
    statusCode: number;
    timestamp: string;
    requestId?: string;
}
export declare class PaginatedResponseDto<T = any> {
    items: T[];
    meta: {
        total: number;
        page: number;
        limit: number;
        totalPages: number;
        hasNext: boolean;
        hasPrevious: boolean;
    };
}
export declare class ListResponseDto<T = any> {
    items: T[];
    count: number;
}
export declare class StatusResponseDto {
    status: 'healthy' | 'degraded' | 'unhealthy';
    timestamp: string;
    details?: Record<string, any>;
}
export declare class BulkOperationResponseDto {
    successful: number;
    failed: number;
    total: number;
    errors?: Array<{
        item: any;
        error: string;
    }>;
}
export declare class FileUploadResponseDto {
    filename: string;
    url: string;
    size: number;
    mimeType: string;
    uploadedAt: string;
}
