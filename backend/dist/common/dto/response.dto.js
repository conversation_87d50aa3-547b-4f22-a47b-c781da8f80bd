"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileUploadResponseDto = exports.BulkOperationResponseDto = exports.StatusResponseDto = exports.ListResponseDto = exports.PaginatedResponseDto = exports.SuccessResponseDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class SuccessResponseDto {
    message;
    data;
    statusCode;
    timestamp;
    requestId;
}
exports.SuccessResponseDto = SuccessResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Success message',
        example: 'Operation completed successfully',
    }),
    __metadata("design:type", String)
], SuccessResponseDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Response data',
    }),
    __metadata("design:type", Object)
], SuccessResponseDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'HTTP status code',
        example: 200,
    }),
    __metadata("design:type", Number)
], SuccessResponseDto.prototype, "statusCode", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the response',
        example: '2024-01-15T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], SuccessResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Request ID for tracking',
        example: 'req_123456789',
    }),
    __metadata("design:type", String)
], SuccessResponseDto.prototype, "requestId", void 0);
class PaginatedResponseDto {
    items;
    meta;
}
exports.PaginatedResponseDto = PaginatedResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of items',
        type: 'array',
    }),
    __metadata("design:type", Array)
], PaginatedResponseDto.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Pagination metadata',
        type: 'object',
        properties: {
            total: {
                type: 'number',
                description: 'Total number of items',
                example: 100,
            },
            page: {
                type: 'number',
                description: 'Current page number',
                example: 1,
            },
            limit: {
                type: 'number',
                description: 'Items per page',
                example: 10,
            },
            totalPages: {
                type: 'number',
                description: 'Total number of pages',
                example: 10,
            },
            hasNext: {
                type: 'boolean',
                description: 'Whether there is a next page',
                example: true,
            },
            hasPrevious: {
                type: 'boolean',
                description: 'Whether there is a previous page',
                example: false,
            },
        },
    }),
    __metadata("design:type", Object)
], PaginatedResponseDto.prototype, "meta", void 0);
class ListResponseDto {
    items;
    count;
}
exports.ListResponseDto = ListResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of items',
        type: 'array',
    }),
    __metadata("design:type", Array)
], ListResponseDto.prototype, "items", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total count of items',
        example: 25,
    }),
    __metadata("design:type", Number)
], ListResponseDto.prototype, "count", void 0);
class StatusResponseDto {
    status;
    timestamp;
    details;
}
exports.StatusResponseDto = StatusResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Service status',
        example: 'healthy',
        enum: ['healthy', 'degraded', 'unhealthy'],
    }),
    __metadata("design:type", String)
], StatusResponseDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Timestamp of the status check',
        example: '2024-01-15T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], StatusResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional status details',
        example: { database: 'connected', redis: 'connected' },
    }),
    __metadata("design:type", Object)
], StatusResponseDto.prototype, "details", void 0);
class BulkOperationResponseDto {
    successful;
    failed;
    total;
    errors;
}
exports.BulkOperationResponseDto = BulkOperationResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items processed successfully',
        example: 8,
    }),
    __metadata("design:type", Number)
], BulkOperationResponseDto.prototype, "successful", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of items that failed processing',
        example: 2,
    }),
    __metadata("design:type", Number)
], BulkOperationResponseDto.prototype, "failed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total number of items processed',
        example: 10,
    }),
    __metadata("design:type", Number)
], BulkOperationResponseDto.prototype, "total", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Details of failed items',
        type: 'array',
        items: {
            type: 'object',
            properties: {
                item: { description: 'Failed item identifier' },
                error: { description: 'Error message' },
            },
        },
    }),
    __metadata("design:type", Array)
], BulkOperationResponseDto.prototype, "errors", void 0);
class FileUploadResponseDto {
    filename;
    url;
    size;
    mimeType;
    uploadedAt;
}
exports.FileUploadResponseDto = FileUploadResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Uploaded file name',
        example: 'prescription_123.pdf',
    }),
    __metadata("design:type", String)
], FileUploadResponseDto.prototype, "filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File URL',
        example: 'https://storage.example.com/files/prescription_123.pdf',
    }),
    __metadata("design:type", String)
], FileUploadResponseDto.prototype, "url", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File size in bytes',
        example: 1024000,
    }),
    __metadata("design:type", Number)
], FileUploadResponseDto.prototype, "size", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'File MIME type',
        example: 'application/pdf',
    }),
    __metadata("design:type", String)
], FileUploadResponseDto.prototype, "mimeType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Upload timestamp',
        example: '2024-01-15T10:30:00.000Z',
    }),
    __metadata("design:type", String)
], FileUploadResponseDto.prototype, "uploadedAt", void 0);
//# sourceMappingURL=response.dto.js.map