{"version": 3, "file": "business.exception.js", "sourceRoot": "", "sources": ["../../../src/common/exceptions/business.exception.ts"], "names": [], "mappings": ";;;AAAA,2CAA2D;AAK3D,MAAa,iBAAkB,SAAQ,sBAAa;IAClD,YACE,OAAe,EACf,aAAyB,mBAAU,CAAC,WAAW,EAC/C,SAAkB,EAClB,OAAa;QAEb,MAAM,aAAa,GAAG;YACpB,OAAO;YACP,KAAK,EAAE,sBAAsB;YAC7B,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;CACF;AAlBD,8CAkBC;AAKD,MAAa,2BAA4B,SAAQ,iBAAiB;IAChE,YAAY,QAAgB,EAAE,MAAc;QAC1C,KAAK,CACH,+BAA+B,MAAM,IAAI,QAAQ,EAAE,EACnD,mBAAU,CAAC,SAAS,EACpB,qBAAqB,EACrB,EAAE,QAAQ,EAAE,MAAM,EAAE,CACrB,CAAC;IACJ,CAAC;CACF;AATD,kEASC;AAKD,MAAa,yBAA0B,SAAQ,iBAAiB;IAC9D,YAAY,QAAgB,EAAE,UAAkB;QAC9C,KAAK,CACH,GAAG,QAAQ,qBAAqB,UAAU,aAAa,EACvD,mBAAU,CAAC,SAAS,EACpB,oBAAoB,EACpB,EAAE,QAAQ,EAAE,UAAU,EAAE,CACzB,CAAC;IACJ,CAAC;CACF;AATD,8DASC;AAKD,MAAa,0BAA2B,SAAQ,iBAAiB;IAC/D,YAAY,QAAgB,EAAE,KAAa,EAAE,KAAa;QACxD,KAAK,CACH,GAAG,QAAQ,SAAS,KAAK,KAAK,KAAK,kBAAkB,EACrD,mBAAU,CAAC,QAAQ,EACnB,oBAAoB,EACpB,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,CAC3B,CAAC;IACJ,CAAC;CACF;AATD,gEASC;AAKD,MAAa,yBAA0B,SAAQ,iBAAiB;IAC9D,YAAY,SAAiB,EAAE,MAAc;QAC3C,KAAK,CACH,sBAAsB,SAAS,aAAa,MAAM,EAAE,EACpD,mBAAU,CAAC,WAAW,EACtB,mBAAmB,EACnB,EAAE,SAAS,EAAE,MAAM,EAAE,CACtB,CAAC;IACJ,CAAC;CACF;AATD,8DASC;AAKD,MAAa,gCAAiC,SAAQ,iBAAiB;IACrE,YAAY,YAAoB,EAAE,WAAmB;QACnD,KAAK,CACH,4CAA4C,YAAY,oBAAoB,WAAW,GAAG,EAC1F,mBAAU,CAAC,SAAS,EACpB,0BAA0B,EAC1B,EAAE,YAAY,EAAE,WAAW,EAAE,CAC9B,CAAC;IACJ,CAAC;CACF;AATD,4EASC;AAKD,MAAa,0BAA2B,SAAQ,iBAAiB;IAC/D,YAAY,KAAa,EAAE,QAAgB;QACzC,KAAK,CACH,wBAAwB,KAAK,iBAAiB,QAAQ,IAAI,EAC1D,mBAAU,CAAC,iBAAiB,EAC5B,qBAAqB,EACrB,EAAE,KAAK,EAAE,QAAQ,EAAE,CACpB,CAAC;IACJ,CAAC;CACF;AATD,gEASC"}