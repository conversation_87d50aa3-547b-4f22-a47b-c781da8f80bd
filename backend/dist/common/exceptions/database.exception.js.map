{"version": 3, "file": "database.exception.js", "sourceRoot": "", "sources": ["../../../src/common/exceptions/database.exception.ts"], "names": [], "mappings": ";;;AAAA,2CAA2D;AAK3D,MAAa,iBAAkB,SAAQ,sBAAa;IAClD,YACE,OAAe,EACf,aAAyB,mBAAU,CAAC,qBAAqB,EACzD,SAAkB,EAClB,OAAa;QAEb,MAAM,aAAa,GAAG;YACpB,OAAO,EAAE,mBAAmB,OAAO,EAAE;YACrC,KAAK,EAAE,gBAAgB;YACvB,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;CACF;AAlBD,8CAkBC;AAKD,MAAa,2BAA4B,SAAQ,iBAAiB;IAChE,YAAY,UAAkB,+BAA+B;QAC3D,KAAK,CAAC,OAAO,EAAE,mBAAU,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,CAAC;IACxE,CAAC;CACF;AAJD,kEAIC;AAKD,MAAa,2BAA4B,SAAQ,iBAAiB;IAChE,YAAY,UAAkB,EAAE,KAAa,EAAE,OAAa;QAC1D,KAAK,CACH,yBAAyB,UAAU,aAAa,KAAK,EAAE,EACvD,mBAAU,CAAC,QAAQ,EACnB,yBAAyB,EACzB,EAAE,UAAU,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,CAClC,CAAC;IACJ,CAAC;CACF;AATD,kEASC;AAKD,MAAa,6BAA8B,SAAQ,2BAA2B;IAC5E,YAAY,KAAa,EAAE,MAAc,EAAE,eAAuB;QAChE,KAAK,CACH,eAAe,MAAM,EAAE,EACvB,KAAK,EACL;YACE,MAAM;YACN,eAAe;YACf,IAAI,EAAE,aAAa;SACpB,CACF,CAAC;IACJ,CAAC;CACF;AAZD,sEAYC;AAKD,MAAa,yBAA0B,SAAQ,2BAA2B;IACxE,YAAY,KAAa,EAAE,MAAc,EAAE,KAAU;QACnD,KAAK,CACH,UAAU,MAAM,EAAE,EAClB,KAAK,EACL;YACE,MAAM;YACN,KAAK;YACL,IAAI,EAAE,QAAQ;SACf,CACF,CAAC;IACJ,CAAC;CACF;AAZD,8DAYC;AAKD,MAAa,wBAAyB,SAAQ,iBAAiB;IAC7D,YAAY,KAAa,EAAE,OAAe;QACxC,KAAK,CACH,uBAAuB,OAAO,IAAI,EAClC,mBAAU,CAAC,eAAe,EAC1B,kBAAkB,EAClB,EAAE,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE,OAAO,EAAE,CAC5C,CAAC;IACJ,CAAC;CACF;AATD,4DASC;AAKD,MAAa,4BAA6B,SAAQ,iBAAiB;IACjE,YAAY,OAAe,EAAE,SAAiB;QAC5C,KAAK,CACH,4BAA4B,SAAS,KAAK,OAAO,EAAE,EACnD,mBAAU,CAAC,qBAAqB,EAChC,sBAAsB,EACtB,EAAE,SAAS,EAAE,CACd,CAAC;IACJ,CAAC;CACF;AATD,oEASC;AAKD,MAAa,0BAA2B,SAAQ,iBAAiB;IAC/D,YAAY,SAAiB,EAAE,OAAe;QAC5C,KAAK,CACH,sBAAsB,SAAS,KAAK,OAAO,EAAE,EAC7C,mBAAU,CAAC,qBAAqB,EAChC,oBAAoB,EACpB,EAAE,SAAS,EAAE,CACd,CAAC;IACJ,CAAC;CACF;AATD,gEASC"}