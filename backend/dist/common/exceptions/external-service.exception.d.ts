import { HttpException, HttpStatus } from '@nestjs/common';
export declare class ExternalServiceException extends HttpException {
    constructor(service: string, message: string, statusCode?: HttpStatus, errorCode?: string, details?: any);
}
export declare class TwilioServiceException extends ExternalServiceException {
    constructor(message: string, errorCode?: string, details?: any);
}
export declare class ElevenLabsServiceException extends ExternalServiceException {
    constructor(message: string, errorCode?: string, details?: any);
}
export declare class AWSServiceException extends ExternalServiceException {
    constructor(service: string, message: string, errorCode?: string, details?: any);
}
export declare class SupabaseServiceException extends ExternalServiceException {
    constructor(message: string, errorCode?: string, details?: any);
}
export declare class ExternalServiceTimeoutException extends ExternalServiceException {
    constructor(service: string, timeout: number);
}
export declare class ExternalServiceRateLimitException extends ExternalServiceException {
    constructor(service: string, retryAfter?: number);
}
export declare class ExternalServiceAuthException extends ExternalServiceException {
    constructor(service: string, message?: string);
}
