"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ExternalServiceAuthException = exports.ExternalServiceRateLimitException = exports.ExternalServiceTimeoutException = exports.SupabaseServiceException = exports.AWSServiceException = exports.ElevenLabsServiceException = exports.TwilioServiceException = exports.ExternalServiceException = void 0;
const common_1 = require("@nestjs/common");
class ExternalServiceException extends common_1.HttpException {
    constructor(service, message, statusCode = common_1.HttpStatus.SERVICE_UNAVAILABLE, errorCode, details) {
        const errorResponse = {
            message: `External service error (${service}): ${message}`,
            error: 'External Service Error',
            statusCode,
            errorCode,
            service,
            details,
            timestamp: new Date().toISOString(),
        };
        super(errorResponse, statusCode);
    }
}
exports.ExternalServiceException = ExternalServiceException;
class TwilioServiceException extends ExternalServiceException {
    constructor(message, errorCode, details) {
        super('Twilio', message, common_1.HttpStatus.SERVICE_UNAVAILABLE, errorCode, details);
    }
}
exports.TwilioServiceException = TwilioServiceException;
class ElevenLabsServiceException extends ExternalServiceException {
    constructor(message, errorCode, details) {
        super('ElevenLabs', message, common_1.HttpStatus.SERVICE_UNAVAILABLE, errorCode, details);
    }
}
exports.ElevenLabsServiceException = ElevenLabsServiceException;
class AWSServiceException extends ExternalServiceException {
    constructor(service, message, errorCode, details) {
        super(`AWS ${service}`, message, common_1.HttpStatus.SERVICE_UNAVAILABLE, errorCode, details);
    }
}
exports.AWSServiceException = AWSServiceException;
class SupabaseServiceException extends ExternalServiceException {
    constructor(message, errorCode, details) {
        super('Supabase', message, common_1.HttpStatus.SERVICE_UNAVAILABLE, errorCode, details);
    }
}
exports.SupabaseServiceException = SupabaseServiceException;
class ExternalServiceTimeoutException extends ExternalServiceException {
    constructor(service, timeout) {
        super(service, `Request timeout after ${timeout}ms`, common_1.HttpStatus.REQUEST_TIMEOUT, 'SERVICE_TIMEOUT', { timeout });
    }
}
exports.ExternalServiceTimeoutException = ExternalServiceTimeoutException;
class ExternalServiceRateLimitException extends ExternalServiceException {
    constructor(service, retryAfter) {
        super(service, 'Rate limit exceeded', common_1.HttpStatus.TOO_MANY_REQUESTS, 'EXTERNAL_RATE_LIMIT', { retryAfter });
    }
}
exports.ExternalServiceRateLimitException = ExternalServiceRateLimitException;
class ExternalServiceAuthException extends ExternalServiceException {
    constructor(service, message = 'Authentication failed') {
        super(service, message, common_1.HttpStatus.UNAUTHORIZED, 'EXTERNAL_AUTH_ERROR');
    }
}
exports.ExternalServiceAuthException = ExternalServiceAuthException;
//# sourceMappingURL=external-service.exception.js.map