{"version": 3, "file": "external-service.exception.js", "sourceRoot": "", "sources": ["../../../src/common/exceptions/external-service.exception.ts"], "names": [], "mappings": ";;;AAAA,2CAA2D;AAK3D,MAAa,wBAAyB,SAAQ,sBAAa;IACzD,YACE,OAAe,EACf,OAAe,EACf,aAAyB,mBAAU,CAAC,mBAAmB,EACvD,SAAkB,EAClB,OAAa;QAEb,MAAM,aAAa,GAAG;YACpB,OAAO,EAAE,2BAA2B,OAAO,MAAM,OAAO,EAAE;YAC1D,KAAK,EAAE,wBAAwB;YAC/B,UAAU;YACV,SAAS;YACT,OAAO;YACP,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC;QAEF,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,CAAC;IACnC,CAAC;CACF;AApBD,4DAoBC;AAKD,MAAa,sBAAuB,SAAQ,wBAAwB;IAClE,YAAY,OAAe,EAAE,SAAkB,EAAE,OAAa;QAC5D,KAAK,CAAC,QAAQ,EAAE,OAAO,EAAE,mBAAU,CAAC,mBAAmB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IAC/E,CAAC;CACF;AAJD,wDAIC;AAKD,MAAa,0BAA2B,SAAQ,wBAAwB;IACtE,YAAY,OAAe,EAAE,SAAkB,EAAE,OAAa;QAC5D,KAAK,CAAC,YAAY,EAAE,OAAO,EAAE,mBAAU,CAAC,mBAAmB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACnF,CAAC;CACF;AAJD,gEAIC;AAKD,MAAa,mBAAoB,SAAQ,wBAAwB;IAC/D,YAAY,OAAe,EAAE,OAAe,EAAE,SAAkB,EAAE,OAAa;QAC7E,KAAK,CAAC,OAAO,OAAO,EAAE,EAAE,OAAO,EAAE,mBAAU,CAAC,mBAAmB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACvF,CAAC;CACF;AAJD,kDAIC;AAKD,MAAa,wBAAyB,SAAQ,wBAAwB;IACpE,YAAY,OAAe,EAAE,SAAkB,EAAE,OAAa;QAC5D,KAAK,CAAC,UAAU,EAAE,OAAO,EAAE,mBAAU,CAAC,mBAAmB,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;IACjF,CAAC;CACF;AAJD,4DAIC;AAKD,MAAa,+BAAgC,SAAQ,wBAAwB;IAC3E,YAAY,OAAe,EAAE,OAAe;QAC1C,KAAK,CACH,OAAO,EACP,yBAAyB,OAAO,IAAI,EACpC,mBAAU,CAAC,eAAe,EAC1B,iBAAiB,EACjB,EAAE,OAAO,EAAE,CACZ,CAAC;IACJ,CAAC;CACF;AAVD,0EAUC;AAKD,MAAa,iCAAkC,SAAQ,wBAAwB;IAC7E,YAAY,OAAe,EAAE,UAAmB;QAC9C,KAAK,CACH,OAAO,EACP,qBAAqB,EACrB,mBAAU,CAAC,iBAAiB,EAC5B,qBAAqB,EACrB,EAAE,UAAU,EAAE,CACf,CAAC;IACJ,CAAC;CACF;AAVD,8EAUC;AAKD,MAAa,4BAA6B,SAAQ,wBAAwB;IACxE,YAAY,OAAe,EAAE,UAAkB,uBAAuB;QACpE,KAAK,CACH,OAAO,EACP,OAAO,EACP,mBAAU,CAAC,YAAY,EACvB,qBAAqB,CACtB,CAAC;IACJ,CAAC;CACF;AATD,oEASC"}