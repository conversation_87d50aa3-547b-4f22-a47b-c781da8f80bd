"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var GlobalExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.GlobalExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const uuid_1 = require("uuid");
const exceptions_1 = require("../exceptions");
let GlobalExceptionFilter = GlobalExceptionFilter_1 = class GlobalExceptionFilter {
    logger = new common_1.Logger(GlobalExceptionFilter_1.name);
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const requestId = (0, uuid_1.v4)();
        request.requestId = requestId;
        let status;
        let errorResponse;
        if (exception instanceof common_1.HttpException) {
            status = exception.getStatus();
            const exceptionResponse = exception.getResponse();
            if (typeof exceptionResponse === 'object') {
                errorResponse = {
                    ...exceptionResponse,
                    requestId,
                    path: request.url,
                };
            }
            else {
                errorResponse = {
                    message: exceptionResponse,
                    error: exception.name,
                    statusCode: status,
                    requestId,
                    path: request.url,
                    timestamp: new Date().toISOString(),
                };
            }
        }
        else {
            status = common_1.HttpStatus.INTERNAL_SERVER_ERROR;
            errorResponse = {
                message: 'Internal server error',
                error: 'Internal Server Error',
                statusCode: status,
                requestId,
                path: request.url,
                timestamp: new Date().toISOString(),
            };
        }
        this.logError(exception, request, requestId, status);
        response.status(status).json(errorResponse);
    }
    logError(exception, request, requestId, status) {
        const errorContext = {
            requestId,
            method: request.method,
            url: request.url,
            userAgent: request.get('User-Agent'),
            ip: request.ip,
            userId: request.user?.id,
            userRole: request.user?.role,
            statusCode: status,
        };
        if (exception instanceof exceptions_1.BusinessException) {
            this.logger.warn(`Business Exception: ${exception.message}`, {
                ...errorContext,
                exception: exception.getResponse(),
            });
        }
        else if (exception instanceof exceptions_1.DatabaseException) {
            this.logger.error(`Database Exception: ${exception.message}`, {
                ...errorContext,
                exception: exception.getResponse(),
                stack: exception.stack,
            });
        }
        else if (exception instanceof exceptions_1.ExternalServiceException) {
            this.logger.error(`External Service Exception: ${exception.message}`, {
                ...errorContext,
                exception: exception.getResponse(),
            });
        }
        else if (exception instanceof exceptions_1.CustomValidationException) {
            this.logger.warn(`Validation Exception: ${exception.message}`, {
                ...errorContext,
                exception: exception.getResponse(),
            });
        }
        else if (exception instanceof common_1.HttpException) {
            if (status >= 500) {
                this.logger.error(`HTTP Exception: ${exception.message}`, {
                    ...errorContext,
                    exception: exception.getResponse(),
                    stack: exception.stack,
                });
            }
            else {
                this.logger.warn(`HTTP Exception: ${exception.message}`, {
                    ...errorContext,
                    exception: exception.getResponse(),
                });
            }
        }
        else {
            this.logger.error(`Unexpected Error: ${exception}`, {
                ...errorContext,
                stack: exception instanceof Error ? exception.stack : 'No stack trace available',
            });
        }
    }
};
exports.GlobalExceptionFilter = GlobalExceptionFilter;
exports.GlobalExceptionFilter = GlobalExceptionFilter = GlobalExceptionFilter_1 = __decorate([
    (0, common_1.Catch)()
], GlobalExceptionFilter);
//# sourceMappingURL=global-exception.filter.js.map