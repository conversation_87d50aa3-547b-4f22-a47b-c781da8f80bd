import { ConfigService } from '@nestjs/config';
export declare class AwsService {
    private readonly configService;
    private readonly logger;
    private readonly textractClient;
    private readonly s3Client;
    private readonly bucketName;
    constructor(configService: ConfigService);
    uploadFileToS3(file: Buffer, fileName: string, contentType: string): Promise<string>;
    extractTextFromPrescription(fileBuffer: Buffer): Promise<{
        extractedText: string;
        confidence: number;
        medicineInfo: any[];
    }>;
    private extractMedicineInformation;
    private getTextFromBlock;
    private categorizeField;
    private extractTableData;
}
