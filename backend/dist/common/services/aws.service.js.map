{"version": 3, "file": "aws.service.js", "sourceRoot": "", "sources": ["../../../src/common/services/aws.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,8DAA6G;AAC7G,kDAAkF;AAG3E,IAAM,UAAU,kBAAhB,MAAM,UAAU;IAMQ;IALZ,MAAM,GAAG,IAAI,eAAM,CAAC,YAAU,CAAC,IAAI,CAAC,CAAC;IACrC,cAAc,CAAiB;IAC/B,QAAQ,CAAW;IACnB,UAAU,CAAS;IAEpC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,EAAE,WAAW,CAAC,CAAC;QACzE,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,mBAAmB,CAAC,CAAC;QACxE,MAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,uBAAuB,CAAC,CAAC;QAEhF,IAAI,CAAC,WAAW,IAAI,CAAC,eAAe,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CAAC,6GAA6G,CAAC,CAAC;QACjI,CAAC;QAED,IAAI,CAAC,cAAc,GAAG,IAAI,gCAAc,CAAC;YACvC,MAAM;YACN,WAAW,EAAE;gBACX,WAAW;gBACX,eAAe;aAChB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,GAAG,IAAI,oBAAQ,CAAC;YAC3B,MAAM;YACN,WAAW,EAAE;gBACX,WAAW;gBACX,eAAe;aAChB;SACF,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oBAAoB,EAAE,uBAAuB,CAAC,CAAC;IAClG,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,IAAY,EAAE,QAAgB,EAAE,WAAmB;QACtE,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,iBAAiB,IAAI,CAAC,GAAG,EAAE,IAAI,QAAQ,EAAE,CAAC;YAEtD,MAAM,OAAO,GAAG,IAAI,4BAAgB,CAAC;gBACnC,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,GAAG,EAAE,GAAG;gBACR,IAAI,EAAE,IAAI;gBACV,WAAW,EAAE,WAAW;aACzB,CAAC,CAAC;YAEH,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAGlC,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,EAAE,WAAW,CAAC,CAAC;YACzE,OAAO,WAAW,IAAI,CAAC,UAAU,OAAO,MAAM,kBAAkB,GAAG,EAAE,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,UAAkB;QAKlD,IAAI,CAAC;YAEH,MAAM,OAAO,GAAG,IAAI,wCAAsB,CAAC;gBACzC,QAAQ,EAAE;oBACR,KAAK,EAAE,UAAU;iBAClB;gBACD,YAAY,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC;aAClC,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAEzD,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACrB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;YACtD,CAAC;YAGD,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC;YAC/E,MAAM,aAAa,GAAG,UAAU;iBAC7B,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;iBACxB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;iBACnC,IAAI,CAAC,IAAI,CAAC,CAAC;YAGd,MAAM,gBAAgB,GAAG,UAAU;iBAChC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,CAAC;iBACnC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;YAExC,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC;gBACnD,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM;gBACjF,CAAC,CAAC,CAAC,CAAC;YAGN,MAAM,YAAY,GAAG,IAAI,CAAC,0BAA0B,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAEtE,OAAO;gBACL,aAAa;gBACb,UAAU,EAAE,iBAAiB;gBAC7B,YAAY;aACb,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YACtE,MAAM,IAAI,KAAK,CAAC,0CAA0C,CAAC,CAAC;QAC9D,CAAC;IACH,CAAC;IAEO,0BAA0B,CAAC,MAAa;QAC9C,MAAM,SAAS,GAAU,EAAE,CAAC;QAE5B,IAAI,CAAC;YAEH,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,eAAe,CAAC,CAAC;YACnF,MAAM,SAAS,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;YACrF,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;YAGzF,MAAM,WAAW,GAAG,IAAI,GAAG,EAAE,CAAC;YAE9B,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC3B,IAAI,QAAQ,CAAC,aAAa,EAAE,CAAC;oBAC3B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;oBACnF,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,GAAG,EAAE,CAAC;wBAC/C,MAAM,UAAU,GAAG,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,iBAAiB,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;wBACjF,IAAI,UAAU,EAAE,CAAC;4BACf,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;4BACxD,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;4BAC5D,WAAW,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC,CAAC;wBACpD,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC,CAAC,CAAC;YAGH,MAAM,gBAAgB,GAAG;gBACvB,UAAU,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO;gBAC9D,QAAQ,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc;aAC1D,CAAC;YAEF,WAAW,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;gBACjC,IAAI,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;oBAC5D,SAAS,CAAC,IAAI,CAAC;wBACb,KAAK,EAAE,GAAG;wBACV,KAAK,EAAE,KAAK;wBACZ,IAAI,EAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;qBAChC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;YAGH,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,KAAK,OAAO,CAAC,CAAC;YACxE,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBAC1B,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBACvD,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzB,SAAS,CAAC,IAAI,CAAC;wBACb,IAAI,EAAE,OAAO;wBACb,IAAI,EAAE,SAAS;qBAChB,CAAC,CAAC;gBACL,CAAC;YACH,CAAC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE,KAAK,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,gBAAgB,CAAC,KAAU,EAAE,SAAgB;QACnD,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC;YACf,OAAO,KAAK,CAAC,IAAI,CAAC;QACpB,CAAC;QAED,IAAI,KAAK,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,iBAAiB,GAAG,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;YAChF,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,GAAG,EAAE,CAAC;gBAC/C,MAAM,UAAU,GAAG,iBAAiB,CAAC,GAAG;qBACrC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;qBAC3C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC;qBACxB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;gBACpB,OAAO,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;QAED,OAAO,EAAE,CAAC;IACZ,CAAC;IAEO,eAAe,CAAC,SAAiB;QACvC,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;QAE3C,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACxG,OAAO,eAAe,CAAC;QACzB,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACjE,OAAO,QAAQ,CAAC;QAClB,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACrC,OAAO,WAAW,CAAC;QACrB,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;YACpC,OAAO,UAAU,CAAC;QACpB,CAAC;QACD,IAAI,UAAU,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YACvC,OAAO,cAAc,CAAC;QACxB,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,gBAAgB,CAAC,UAAe,EAAE,SAAgB;QACxD,MAAM,SAAS,GAAU,EAAE,CAAC;QAE5B,IAAI,CAAC;YACH,IAAI,UAAU,CAAC,aAAa,EAAE,CAAC;gBAC7B,MAAM,gBAAgB,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC;gBACpF,IAAI,gBAAgB,IAAI,gBAAgB,CAAC,GAAG,EAAE,CAAC;oBAC7C,MAAM,KAAK,GAAG,gBAAgB,CAAC,GAAG;yBAC/B,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;yBAC3C,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,CAAC;oBAG5C,MAAM,MAAM,GAAG,IAAI,GAAG,EAAE,CAAC;oBACzB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;wBACpC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC;4BAC1B,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;wBAC3B,CAAC;wBACD,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC;4BACxB,MAAM,EAAE,IAAI,CAAC,WAAW,IAAI,CAAC;4BAC7B,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC;yBAC7C,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;oBAGH,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;wBACjC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC;wBAC9D,SAAS,CAAC,IAAI,CAAC;4BACb,GAAG,EAAE,QAAQ;4BACb,KAAK,EAAE,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;yBAC1C,CAAC,CAAC;oBACL,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF,CAAA;AAxPY,gCAAU;qBAAV,UAAU;IADtB,IAAA,mBAAU,GAAE;qCAOiC,sBAAa;GAN9C,UAAU,CAwPtB"}