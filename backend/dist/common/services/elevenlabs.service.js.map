{"version": 3, "file": "elevenlabs.service.js", "sourceRoot": "", "sources": ["../../../src/common/services/elevenlabs.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,6DAA6D;AA6BtD,IAAM,iBAAiB,yBAAvB,MAAM,iBAAiB;IAQC;IAPZ,MAAM,GAAG,IAAI,eAAM,CAAC,mBAAiB,CAAC,IAAI,CAAC,CAAC;IAC5C,gBAAgB,CAAmB;IACnC,SAAS,CAAU;IACnB,cAAc,CAAS;IACvB,OAAO,CAAS;IAChB,kBAAkB,CAAS;IAE5C,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QACvD,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oBAAoB,CAAC,CAAC;QACpE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,qBAAqB,EAAE,sBAAsB,CAAC,CAAC;QACpG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,qBAAqB,EAAE,EAAE,CAAC,CAAC;QACzE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kCAAkC,EAAE,EAAE,CAAC,CAAC;QAGjG,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;QAE5B,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,gCAAgB,CAAC;gBAC3C,MAAM,EAAE,MAAM;aACf,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QACjE,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0EAA0E,CAAC,CAAC;QAC/F,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,OAAmC;QAC9D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,0DAA0D,CAAC,CAAC;YAC7E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;QAChE,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gEAAgE,CAAC,CAAC;YACnF,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,iCAAiC,EAAE,CAAC;QACtE,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC;gBAChF,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,kBAAkB,EAAE,IAAI,CAAC,kBAAkB;gBAC3C,QAAQ,EAAE,OAAO,CAAC,WAAW;aAK9B,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iDAAiD,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;YACxF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,OAAO,EAAE,CAAC;QACrD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yCAAyC,OAAO,CAAC,WAAW,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1F,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,OAA4B;QAC/C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uDAAuD,CAAC,CAAC;YAC1E,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,2BAA2B,EAAE,CAAC;QAChE,CAAC;QAED,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,OAAO,CAC5D,OAAO,CAAC,OAAO,IAAI,IAAI,CAAC,cAAc,EACtC;gBACE,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,uBAAuB;gBACnD,aAAa,EAAE;oBACb,SAAS,EAAE,GAAG;oBACd,eAAe,EAAE,IAAI;iBACtB;aACF,CACF,CAAC;YAGF,MAAM,MAAM,GAAa,EAAE,CAAC;YAC5B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,KAAK,EAAE,CAAC;gBAChC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAClC,CAAC;YACD,MAAM,WAAW,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAE1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4CAA4C,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC;YACjG,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,gCAAgC,CACpC,WAAmB,EACnB,YAAoB,EACpB,MAAc,EACd,OAAgB;QAEhB,MAAM,OAAO,GAAG,IAAI,CAAC,iCAAiC,CAAC,WAAW,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;QAE1F,OAAO,IAAI,CAAC,cAAc,CAAC;YACzB,IAAI,EAAE,OAAO;YACb,OAAO,EAAE,OAAO,IAAI,IAAI,CAAC,cAAc;SACxC,CAAC,CAAC;IACL,CAAC;IAEO,iCAAiC,CACvC,WAAmB,EACnB,YAAoB,EACpB,MAAc;QAEd,OAAO,SAAS,WAAW,oFAAoF,YAAY,KAAK,MAAM,oTAAoT,CAAC;IAC7b,CAAC;IAGD,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,EAAE,CAAC;QACZ,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YAC3D,OAAO,MAAM,CAAC,MAAM,IAAI,EAAE,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC9D,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAGD,gBAAgB;QAMd,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,UAAU,EAAE,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,oBAAoB,CAAC;YAC1D,eAAe,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC;YAC5D,OAAO,EAAE,IAAI,CAAC,cAAc;SAC7B,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,WAAW,CAAC,IAAY,EAAE,MAAc;QAC5C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC/C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM,CAAC;gBACvE,IAAI,EAAE,IAAI;gBACV,kBAAkB,EAAE;oBAClB,KAAK,EAAE;wBACL,MAAM,EAAE;4BACN,MAAM,EAAE,MAAM,IAAI,+DAA+D;yBAClF;qBACF;iBACF;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,+BAA+B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YAChE,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YACpD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAGD,KAAK,CAAC,kBAAkB,CACtB,QAAsE;QAEtE,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBACxB,QAAQ,EAAE,CAAC,CAAC,QAAQ;gBACpB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,2BAA2B;aACnC,CAAC,CAAC,CAAC;QACN,CAAC;QAED,MAAM,OAAO,GAAmE,EAAE,CAAC;QAEnF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC;gBACvC,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,OAAO,EAAE,OAAO,CAAC,OAAO;aACzB,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC;gBACX,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,GAAG,MAAM;aACV,CAAC,CAAC;YAGH,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAA;AAzMY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCASiC,sBAAa;GAR9C,iBAAiB,CAyM7B"}