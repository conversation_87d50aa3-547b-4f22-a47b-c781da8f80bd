"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var TwilioService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TwilioService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const twilio_1 = require("twilio");
let TwilioService = TwilioService_1 = class TwilioService {
    configService;
    logger = new common_1.Logger(TwilioService_1.name);
    twilioClient;
    fromPhoneNumber;
    isEnabled;
    constructor(configService) {
        this.configService = configService;
        const accountSid = this.configService.get('TWILIO_ACCOUNT_SID');
        const authToken = this.configService.get('TWILIO_AUTH_TOKEN');
        this.fromPhoneNumber = this.configService.get('TWILIO_PHONE_NUMBER', '');
        this.isEnabled = !!(accountSid && authToken && this.fromPhoneNumber);
        if (this.isEnabled) {
            this.twilioClient = new twilio_1.Twilio(accountSid, authToken);
            this.logger.log('Twilio service initialized successfully');
        }
        else {
            this.logger.warn('Twilio service disabled - missing configuration (TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_PHONE_NUMBER)');
        }
    }
    async sendSMS(options) {
        if (!this.isEnabled) {
            this.logger.warn('Twilio not configured - SMS not sent');
            return { success: false, error: 'Twilio not configured' };
        }
        try {
            const message = await this.twilioClient.messages.create({
                body: options.message,
                from: options.from || this.fromPhoneNumber,
                to: options.to,
            });
            this.logger.log(`SMS sent successfully to ${options.to}, SID: ${message.sid}`);
            return { success: true, messageSid: message.sid };
        }
        catch (error) {
            this.logger.error(`Failed to send SMS to ${options.to}:`, error);
            return { success: false, error: error.message };
        }
    }
    async makeCall(options) {
        if (!this.isEnabled) {
            this.logger.warn('Twilio not configured - call not made');
            return { success: false, error: 'Twilio not configured' };
        }
        try {
            const twiml = this.generateTwiML(options.message, options.voice);
            const call = await this.twilioClient.calls.create({
                twiml: twiml,
                from: options.from || this.fromPhoneNumber,
                to: options.to,
            });
            this.logger.log(`Call initiated successfully to ${options.to}, SID: ${call.sid}`);
            return { success: true, callSid: call.sid };
        }
        catch (error) {
            this.logger.error(`Failed to make call to ${options.to}:`, error);
            return { success: false, error: error.message };
        }
    }
    async sendMedicationReminder(phoneNumber, patientName, medicineName, dosage, reminderType = 'sms') {
        const message = this.generateMedicationReminderMessage(patientName, medicineName, dosage);
        if (reminderType === 'sms') {
            return this.sendSMS({
                to: phoneNumber,
                message: message,
            });
        }
        else {
            return this.makeCall({
                to: phoneNumber,
                message: message,
                voice: 'alice',
            });
        }
    }
    generateMedicationReminderMessage(patientName, medicineName, dosage) {
        return `Hi ${patientName}, this is a friendly reminder to take your medication: ${medicineName} (${dosage}). Please take it as prescribed by your doctor. Stay healthy! - MedCare Team`;
    }
    generateTwiML(message, voice = 'alice') {
        return `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Say voice="${voice}">${this.escapeXml(message)}</Say>
  <Pause length="1"/>
  <Say voice="${voice}">Thank you for using MedCare. Have a great day!</Say>
</Response>`;
    }
    escapeXml(text) {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&apos;');
    }
    isValidPhoneNumber(phoneNumber) {
        const phoneRegex = /^\+[1-9]\d{1,14}$/;
        return phoneRegex.test(phoneNumber);
    }
    getServiceStatus() {
        return {
            enabled: this.isEnabled,
            configured: !!(this.configService.get('TWILIO_ACCOUNT_SID') &&
                this.configService.get('TWILIO_AUTH_TOKEN') &&
                this.configService.get('TWILIO_PHONE_NUMBER')),
        };
    }
    async sendBulkSMS(recipients) {
        if (!this.isEnabled) {
            return recipients.map(r => ({
                phoneNumber: r.phoneNumber,
                success: false,
                error: 'Twilio not configured'
            }));
        }
        const results = [];
        for (const recipient of recipients) {
            const result = await this.sendSMS({
                to: recipient.phoneNumber,
                message: recipient.message,
            });
            results.push({
                phoneNumber: recipient.phoneNumber,
                ...result,
            });
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        return results;
    }
};
exports.TwilioService = TwilioService;
exports.TwilioService = TwilioService = TwilioService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], TwilioService);
//# sourceMappingURL=twilio.service.js.map