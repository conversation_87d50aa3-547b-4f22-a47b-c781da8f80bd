export declare enum AchievementCategory {
    STREAK = "streak",
    CONSISTENCY = "consistency",
    COMPLETION = "completion",
    MILESTONE = "milestone"
}
export declare class CreateAchievementDto {
    name: string;
    description: string;
    icon: string;
    category: AchievementCategory;
    requirement: number;
    points: number;
}
export declare class UpdateAchievementDto {
    name?: string;
    description?: string;
    icon?: string;
    category?: AchievementCategory;
    requirement?: number;
    points?: number;
}
export declare class AchievementQueryDto {
    category?: AchievementCategory;
    limit?: number;
    offset?: number;
}
export declare class UserAchievementQueryDto {
    user_id: string;
    category?: AchievementCategory;
    unlocked_only?: boolean;
}
export interface AchievementProgress {
    achievement_id: string;
    achievement_name: string;
    achievement_description: string;
    achievement_icon: string;
    category: AchievementCategory;
    requirement: number;
    points: number;
    current_progress: number;
    progress_percentage: number;
    is_unlocked: boolean;
    unlocked_at?: Date;
    estimated_completion?: string;
}
export interface AchievementSummary {
    user_id: string;
    total_achievements: number;
    unlocked_achievements: number;
    total_points_from_achievements: number;
    completion_percentage: number;
    recent_achievements: Array<{
        achievement_id: string;
        achievement_name: string;
        points: number;
        unlocked_at: Date;
    }>;
    next_achievements: Array<{
        achievement_id: string;
        achievement_name: string;
        progress_percentage: number;
        estimated_completion: string;
    }>;
    category_breakdown: Array<{
        category: AchievementCategory;
        total: number;
        unlocked: number;
        percentage: number;
    }>;
}
export declare const DEFAULT_ACHIEVEMENTS: {
    name: string;
    description: string;
    icon: string;
    category: AchievementCategory;
    requirement: number;
    points: number;
}[];
