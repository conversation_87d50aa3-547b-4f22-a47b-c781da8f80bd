"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_ACHIEVEMENTS = exports.UserAchievementQueryDto = exports.AchievementQueryDto = exports.UpdateAchievementDto = exports.CreateAchievementDto = exports.AchievementCategory = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
var AchievementCategory;
(function (AchievementCategory) {
    AchievementCategory["STREAK"] = "streak";
    AchievementCategory["CONSISTENCY"] = "consistency";
    AchievementCategory["COMPLETION"] = "completion";
    AchievementCategory["MILESTONE"] = "milestone";
})(AchievementCategory || (exports.AchievementCategory = AchievementCategory = {}));
class CreateAchievementDto {
    name;
    description;
    icon;
    category;
    requirement;
    points;
}
exports.CreateAchievementDto = CreateAchievementDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Achievement name',
        example: 'Week Warrior',
        maxLength: 255,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateAchievementDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Achievement description',
        example: 'Maintain a 7-day medication streak',
        maxLength: 1000,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(1000),
    __metadata("design:type", String)
], CreateAchievementDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Achievement icon identifier',
        example: 'trophy-star',
        maxLength: 255,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], CreateAchievementDto.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Achievement category',
        enum: AchievementCategory,
        example: AchievementCategory.STREAK,
    }),
    (0, class_validator_1.IsEnum)(AchievementCategory),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAchievementDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Requirement value to unlock this achievement',
        example: 7,
        minimum: 1,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateAchievementDto.prototype, "requirement", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Points awarded for this achievement',
        example: 100,
        minimum: 0,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], CreateAchievementDto.prototype, "points", void 0);
class UpdateAchievementDto {
    name;
    description;
    icon;
    category;
    requirement;
    points;
}
exports.UpdateAchievementDto = UpdateAchievementDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Achievement name',
        example: 'Week Warrior',
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateAchievementDto.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Achievement description',
        example: 'Maintain a 7-day medication streak',
        maxLength: 1000,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(1000),
    __metadata("design:type", String)
], UpdateAchievementDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Achievement icon identifier',
        example: 'trophy-star',
        maxLength: 255,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(255),
    __metadata("design:type", String)
], UpdateAchievementDto.prototype, "icon", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Achievement category',
        enum: AchievementCategory,
        example: AchievementCategory.STREAK,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(AchievementCategory),
    __metadata("design:type", String)
], UpdateAchievementDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Requirement value to unlock this achievement',
        example: 7,
        minimum: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], UpdateAchievementDto.prototype, "requirement", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Points awarded for this achievement',
        example: 100,
        minimum: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateAchievementDto.prototype, "points", void 0);
class AchievementQueryDto {
    category;
    limit = 20;
    offset = 0;
}
exports.AchievementQueryDto = AchievementQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by category',
        enum: AchievementCategory,
        example: AchievementCategory.STREAK,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(AchievementCategory),
    __metadata("design:type", String)
], AchievementQueryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of achievements to return',
        example: 20,
        default: 20,
        minimum: 1,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], AchievementQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of achievements to skip',
        example: 0,
        default: 0,
        minimum: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], AchievementQueryDto.prototype, "offset", void 0);
class UserAchievementQueryDto {
    user_id;
    category;
    unlocked_only = false;
}
exports.UserAchievementQueryDto = UserAchievementQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID to get achievements for',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], UserAchievementQueryDto.prototype, "user_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by category',
        enum: AchievementCategory,
        example: AchievementCategory.STREAK,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(AchievementCategory),
    __metadata("design:type", String)
], UserAchievementQueryDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include only unlocked achievements',
        example: true,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true'),
    __metadata("design:type", Boolean)
], UserAchievementQueryDto.prototype, "unlocked_only", void 0);
exports.DEFAULT_ACHIEVEMENTS = [
    {
        name: 'First Steps',
        description: 'Take your first medication on time',
        icon: 'baby-steps',
        category: AchievementCategory.STREAK,
        requirement: 1,
        points: 10,
    },
    {
        name: 'Week Warrior',
        description: 'Maintain a 7-day medication streak',
        icon: 'trophy-week',
        category: AchievementCategory.STREAK,
        requirement: 7,
        points: 100,
    },
    {
        name: 'Two Week Champion',
        description: 'Maintain a 14-day medication streak',
        icon: 'trophy-two-weeks',
        category: AchievementCategory.STREAK,
        requirement: 14,
        points: 200,
    },
    {
        name: 'Monthly Master',
        description: 'Maintain a 30-day medication streak',
        icon: 'trophy-month',
        category: AchievementCategory.STREAK,
        requirement: 30,
        points: 500,
    },
    {
        name: 'Quarterly Hero',
        description: 'Maintain a 90-day medication streak',
        icon: 'trophy-quarter',
        category: AchievementCategory.STREAK,
        requirement: 90,
        points: 1000,
    },
    {
        name: 'Half Year Legend',
        description: 'Maintain a 180-day medication streak',
        icon: 'trophy-half-year',
        category: AchievementCategory.STREAK,
        requirement: 180,
        points: 2000,
    },
    {
        name: 'Year Long Champion',
        description: 'Maintain a 365-day medication streak',
        icon: 'trophy-year',
        category: AchievementCategory.STREAK,
        requirement: 365,
        points: 5000,
    },
    {
        name: 'Reliable Rookie',
        description: 'Achieve 80% adherence rate for a month',
        icon: 'consistency-80',
        category: AchievementCategory.CONSISTENCY,
        requirement: 80,
        points: 150,
    },
    {
        name: 'Dependable Pro',
        description: 'Achieve 90% adherence rate for a month',
        icon: 'consistency-90',
        category: AchievementCategory.CONSISTENCY,
        requirement: 90,
        points: 300,
    },
    {
        name: 'Perfect Patient',
        description: 'Achieve 95% adherence rate for a month',
        icon: 'consistency-95',
        category: AchievementCategory.CONSISTENCY,
        requirement: 95,
        points: 500,
    },
    {
        name: 'Flawless Performer',
        description: 'Achieve 100% adherence rate for a month',
        icon: 'consistency-100',
        category: AchievementCategory.CONSISTENCY,
        requirement: 100,
        points: 1000,
    },
    {
        name: 'Century Club',
        description: 'Take 100 medications',
        icon: 'milestone-100',
        category: AchievementCategory.MILESTONE,
        requirement: 100,
        points: 200,
    },
    {
        name: 'Half Thousand Hero',
        description: 'Take 500 medications',
        icon: 'milestone-500',
        category: AchievementCategory.MILESTONE,
        requirement: 500,
        points: 750,
    },
    {
        name: 'Thousand Strong',
        description: 'Take 1000 medications',
        icon: 'milestone-1000',
        category: AchievementCategory.MILESTONE,
        requirement: 1000,
        points: 1500,
    },
    {
        name: 'Course Completer',
        description: 'Complete your first prescription course',
        icon: 'completion-first',
        category: AchievementCategory.COMPLETION,
        requirement: 1,
        points: 100,
    },
    {
        name: 'Treatment Veteran',
        description: 'Complete 5 prescription courses',
        icon: 'completion-five',
        category: AchievementCategory.COMPLETION,
        requirement: 5,
        points: 500,
    },
    {
        name: 'Wellness Warrior',
        description: 'Complete 10 prescription courses',
        icon: 'completion-ten',
        category: AchievementCategory.COMPLETION,
        requirement: 10,
        points: 1000,
    },
];
//# sourceMappingURL=index.js.map