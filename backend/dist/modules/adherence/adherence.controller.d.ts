import { AdherenceService } from './adherence.service';
import { CreateAdherenceRecordDto, UpdateAdherenceRecordDto, AdherenceQueryDto, AdherenceAnalytics } from './dto';
import { AdherenceRecord } from '../../common/types';
export declare class AdherenceController {
    private readonly adherenceService;
    constructor(adherenceService: AdherenceService);
    create(createAdherenceDto: CreateAdherenceRecordDto, req: any): Promise<AdherenceRecord>;
    findAll(query: AdherenceQueryDto, req: any): Promise<AdherenceRecord[]>;
    getAnalytics(patientId: string, req: any, days?: number): Promise<AdherenceAnalytics>;
    findOne(id: string, req: any): Promise<AdherenceRecord>;
    update(id: string, updateAdherenceDto: UpdateAdherenceRecordDto, req: any): Promise<AdherenceRecord>;
    remove(id: string, req: any): Promise<void>;
}
