"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdherenceModule = void 0;
const common_1 = require("@nestjs/common");
const adherence_service_1 = require("./adherence.service");
const adherence_controller_1 = require("./adherence.controller");
const supabase_service_1 = require("../../config/supabase.service");
const gamification_module_1 = require("../gamification/gamification.module");
const achievements_module_1 = require("../achievements/achievements.module");
let AdherenceModule = class AdherenceModule {
};
exports.AdherenceModule = AdherenceModule;
exports.AdherenceModule = AdherenceModule = __decorate([
    (0, common_1.Module)({
        imports: [
            (0, common_1.forwardRef)(() => gamification_module_1.GamificationModule),
            (0, common_1.forwardRef)(() => achievements_module_1.AchievementsModule),
        ],
        controllers: [adherence_controller_1.AdherenceController],
        providers: [adherence_service_1.AdherenceService, supabase_service_1.SupabaseService],
        exports: [adherence_service_1.AdherenceService],
    })
], AdherenceModule);
//# sourceMappingURL=adherence.module.js.map