import { SupabaseService } from '../../config/supabase.service';
import { User, AdherenceRecord } from '../../common/types';
import { CreateAdherenceRecordDto, UpdateAdherenceRecordDto, AdherenceQueryDto, AdherenceAnalyticsDto, AdherenceAnalytics } from './dto';
export declare class AdherenceService {
    private readonly supabaseService;
    constructor(supabaseService: SupabaseService);
    create(createAdherenceDto: CreateAdherenceRecordDto, currentUser: User): Promise<AdherenceRecord>;
    private updateGamificationAfterAdherence;
    findAll(query: AdherenceQueryDto, currentUser: User): Promise<AdherenceRecord[]>;
    findOne(id: string, currentUser: User): Promise<AdherenceRecord>;
    update(id: string, updateAdherenceDto: UpdateAdherenceRecordDto, currentUser: User): Promise<AdherenceRecord>;
    remove(id: string, currentUser: User): Promise<void>;
    getAnalytics(analyticsDto: AdherenceAnalyticsDto, currentUser: User): Promise<AdherenceAnalytics>;
    private calculateStreaks;
    private calculateDailyBreakdown;
    private calculateMedicineBreakdown;
}
