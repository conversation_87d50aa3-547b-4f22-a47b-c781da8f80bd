"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdherenceService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../config/supabase.service");
let AdherenceService = class AdherenceService {
    supabaseService;
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async create(createAdherenceDto, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== createAdherenceDto.patient_id) {
            throw new common_1.ForbiddenException('Patients can only record their own adherence');
        }
        const supabase = this.supabaseService.getClient();
        const { data: patient, error: patientError } = await supabase
            .from('patients')
            .select('id')
            .eq('id', createAdherenceDto.patient_id)
            .single();
        if (patientError || !patient) {
            throw new common_1.NotFoundException(`Patient with ID ${createAdherenceDto.patient_id} not found`);
        }
        const { data: medicine, error: medicineError } = await supabase
            .from('medicines')
            .select('id, name')
            .eq('id', createAdherenceDto.medicine_id)
            .single();
        if (medicineError || !medicine) {
            throw new common_1.NotFoundException(`Medicine with ID ${createAdherenceDto.medicine_id} not found`);
        }
        const { data: existing, error: existingError } = await supabase
            .from('adherence_records')
            .select('id')
            .eq('patient_id', createAdherenceDto.patient_id)
            .eq('medicine_id', createAdherenceDto.medicine_id)
            .eq('scheduled_time', createAdherenceDto.scheduled_time)
            .single();
        if (existing) {
            throw new common_1.BadRequestException('Adherence record already exists for this medication and time');
        }
        const { data, error } = await supabase
            .from('adherence_records')
            .insert({
            patient_id: createAdherenceDto.patient_id,
            medicine_id: createAdherenceDto.medicine_id,
            scheduled_time: createAdherenceDto.scheduled_time,
            taken_time: createAdherenceDto.taken_time || null,
            status: createAdherenceDto.status,
            notes: createAdherenceDto.notes || null,
        })
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency)
      `)
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to create adherence record: ${error.message}`);
        }
        await this.updateGamificationAfterAdherence(data);
        return data;
    }
    async updateGamificationAfterAdherence(adherenceRecord) {
        try {
            const { GamificationService } = await Promise.resolve().then(() => require('../gamification/gamification.service'));
            const { AchievementsService } = await Promise.resolve().then(() => require('../achievements/achievements.service'));
            const gamificationService = new GamificationService(this.supabaseService);
            const achievementsService = new AchievementsService(this.supabaseService);
            await gamificationService.updateStatsAfterAdherence(adherenceRecord.patient_id, adherenceRecord.status, new Date(adherenceRecord.scheduled_time), adherenceRecord.taken_time ? new Date(adherenceRecord.taken_time) : undefined);
            await achievementsService.checkAndAwardAchievements(adherenceRecord.patient_id);
        }
        catch (error) {
            console.error('Failed to update gamification after adherence:', error);
        }
    }
    async findAll(query, currentUser) {
        const supabase = this.supabaseService.getClient();
        let dbQuery = supabase
            .from('adherence_records')
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency, instructions)
      `);
        if (currentUser.role === 'patient') {
            dbQuery = dbQuery.eq('patient_id', currentUser.id);
        }
        else if (currentUser.role === 'doctor') {
            const { data: assignedPatients } = await supabase
                .from('patients')
                .select('id')
                .eq('assigned_doctor_id', currentUser.id);
            if (assignedPatients && assignedPatients.length > 0) {
                const patientIds = assignedPatients.map(p => p.id);
                dbQuery = dbQuery.in('patient_id', patientIds);
            }
            else {
                return [];
            }
        }
        if (query.patient_id) {
            dbQuery = dbQuery.eq('patient_id', query.patient_id);
        }
        if (query.medicine_id) {
            dbQuery = dbQuery.eq('medicine_id', query.medicine_id);
        }
        if (query.status) {
            dbQuery = dbQuery.eq('status', query.status);
        }
        if (query.start_date) {
            dbQuery = dbQuery.gte('scheduled_time', query.start_date);
        }
        if (query.end_date) {
            dbQuery = dbQuery.lte('scheduled_time', query.end_date);
        }
        dbQuery = dbQuery
            .order('scheduled_time', { ascending: false })
            .range(query.offset || 0, (query.offset || 0) + (query.limit || 50) - 1);
        const { data, error } = await dbQuery;
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch adherence records: ${error.message}`);
        }
        return data || [];
    }
    async findOne(id, currentUser) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('adherence_records')
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency, instructions, side_effects)
      `)
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Adherence record with ID ${id} not found`);
        }
        if (currentUser.role === 'patient' && currentUser.id !== data.patient_id) {
            throw new common_1.ForbiddenException('Patients can only view their own adherence records');
        }
        if (currentUser.role === 'doctor') {
            const { data: patient } = await supabase
                .from('patients')
                .select('assigned_doctor_id')
                .eq('id', data.patient_id)
                .single();
            if (!patient || patient.assigned_doctor_id !== currentUser.id) {
                throw new common_1.ForbiddenException('You can only view adherence records for your assigned patients');
            }
        }
        return data;
    }
    async update(id, updateAdherenceDto, currentUser) {
        const existingRecord = await this.findOne(id, currentUser);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('adherence_records')
            .update({
            taken_time: updateAdherenceDto.taken_time || existingRecord.taken_time,
            status: updateAdherenceDto.status || existingRecord.status,
            notes: updateAdherenceDto.notes !== undefined ? updateAdherenceDto.notes : existingRecord.notes,
            updated_at: new Date().toISOString(),
        })
            .eq('id', id)
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        medicine:medicines(id, name, dosage, frequency)
      `)
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to update adherence record: ${error.message}`);
        }
        return data;
    }
    async remove(id, currentUser) {
        await this.findOne(id, currentUser);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase
            .from('adherence_records')
            .delete()
            .eq('id', id);
        if (error) {
            throw new common_1.BadRequestException(`Failed to delete adherence record: ${error.message}`);
        }
    }
    async getAnalytics(analyticsDto, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== analyticsDto.patient_id) {
            throw new common_1.ForbiddenException('Patients can only view their own analytics');
        }
        if (currentUser.role === 'doctor') {
            const supabase = this.supabaseService.getClient();
            const { data: patient } = await supabase
                .from('patients')
                .select('assigned_doctor_id')
                .eq('id', analyticsDto.patient_id)
                .single();
            if (!patient || patient.assigned_doctor_id !== currentUser.id) {
                throw new common_1.ForbiddenException('You can only view analytics for your assigned patients');
            }
        }
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - (analyticsDto.days || 30));
        const supabase = this.supabaseService.getClient();
        const { data: records, error } = await supabase
            .from('adherence_records')
            .select(`
        *,
        medicine:medicines(id, name)
      `)
            .eq('patient_id', analyticsDto.patient_id)
            .gte('scheduled_time', startDate.toISOString())
            .lte('scheduled_time', endDate.toISOString())
            .order('scheduled_time', { ascending: true });
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch analytics data: ${error.message}`);
        }
        const adherenceRecords = records || [];
        const totalScheduled = adherenceRecords.length;
        const totalTaken = adherenceRecords.filter(r => r.status === 'taken').length;
        const totalMissed = adherenceRecords.filter(r => r.status === 'missed').length;
        const totalSkipped = adherenceRecords.filter(r => r.status === 'skipped').length;
        const adherenceRate = totalScheduled > 0 ? (totalTaken / totalScheduled) * 100 : 0;
        const onTimeRecords = adherenceRecords.filter(r => {
            if (r.status !== 'taken' || !r.taken_time)
                return false;
            const scheduled = new Date(r.scheduled_time);
            const taken = new Date(r.taken_time);
            const diffHours = (taken.getTime() - scheduled.getTime()) / (1000 * 60 * 60);
            return diffHours <= 1;
        });
        const lateRecords = adherenceRecords.filter(r => {
            if (r.status !== 'taken' || !r.taken_time)
                return false;
            const scheduled = new Date(r.scheduled_time);
            const taken = new Date(r.taken_time);
            const diffHours = (taken.getTime() - scheduled.getTime()) / (1000 * 60 * 60);
            return diffHours > 1;
        });
        const onTimeRate = totalTaken > 0 ? (onTimeRecords.length / totalTaken) * 100 : 0;
        const lateRate = totalTaken > 0 ? (lateRecords.length / totalTaken) * 100 : 0;
        const { currentStreak, longestStreak } = this.calculateStreaks(adherenceRecords);
        const dailyBreakdown = this.calculateDailyBreakdown(adherenceRecords, startDate, endDate);
        const medicineBreakdown = this.calculateMedicineBreakdown(adherenceRecords);
        return {
            patient_id: analyticsDto.patient_id,
            total_scheduled: totalScheduled,
            total_taken: totalTaken,
            total_missed: totalMissed,
            total_skipped: totalSkipped,
            adherence_rate: Math.round(adherenceRate * 100) / 100,
            on_time_rate: Math.round(onTimeRate * 100) / 100,
            late_rate: Math.round(lateRate * 100) / 100,
            current_streak: currentStreak,
            longest_streak: longestStreak,
            daily_breakdown: dailyBreakdown,
            medicine_breakdown: medicineBreakdown,
        };
    }
    calculateStreaks(records) {
        if (records.length === 0)
            return { currentStreak: 0, longestStreak: 0 };
        const dailyRecords = new Map();
        records.forEach(record => {
            const date = new Date(record.scheduled_time).toDateString();
            if (!dailyRecords.has(date)) {
                dailyRecords.set(date, []);
            }
            dailyRecords.get(date).push(record);
        });
        const dailyRates = [];
        for (const [date, dayRecords] of dailyRecords) {
            const taken = dayRecords.filter(r => r.status === 'taken').length;
            const total = dayRecords.length;
            const rate = total > 0 ? (taken / total) * 100 : 0;
            dailyRates.push({ date, rate });
        }
        dailyRates.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        let currentStreak = 0;
        let longestStreak = 0;
        let tempStreak = 0;
        for (let i = dailyRates.length - 1; i >= 0; i--) {
            if (dailyRates[i].rate >= 80) {
                tempStreak++;
                if (i === dailyRates.length - 1) {
                    currentStreak = tempStreak;
                }
            }
            else {
                if (i === dailyRates.length - 1) {
                    currentStreak = 0;
                }
                longestStreak = Math.max(longestStreak, tempStreak);
                tempStreak = 0;
            }
        }
        longestStreak = Math.max(longestStreak, tempStreak);
        return { currentStreak, longestStreak };
    }
    calculateDailyBreakdown(records, startDate, endDate) {
        const dailyMap = new Map();
        const currentDate = new Date(startDate);
        while (currentDate <= endDate) {
            const dateStr = currentDate.toISOString().split('T')[0];
            dailyMap.set(dateStr, { scheduled: 0, taken: 0, missed: 0, skipped: 0 });
            currentDate.setDate(currentDate.getDate() + 1);
        }
        records.forEach(record => {
            const dateStr = new Date(record.scheduled_time).toISOString().split('T')[0];
            const dayData = dailyMap.get(dateStr);
            if (dayData) {
                dayData.scheduled++;
                if (record.status === 'taken')
                    dayData.taken++;
                else if (record.status === 'missed')
                    dayData.missed++;
                else if (record.status === 'skipped')
                    dayData.skipped++;
            }
        });
        return Array.from(dailyMap.entries()).map(([date, data]) => ({
            date,
            ...data,
            rate: data.scheduled > 0 ? Math.round((data.taken / data.scheduled) * 10000) / 100 : 0,
        }));
    }
    calculateMedicineBreakdown(records) {
        const medicineMap = new Map();
        records.forEach(record => {
            const medicineId = record.medicine_id;
            const medicineName = record.medicine?.name || 'Unknown Medicine';
            if (!medicineMap.has(medicineId)) {
                medicineMap.set(medicineId, {
                    medicine_id: medicineId,
                    medicine_name: medicineName,
                    scheduled: 0,
                    taken: 0,
                    missed: 0,
                    skipped: 0,
                });
            }
            const medicineData = medicineMap.get(medicineId);
            medicineData.scheduled++;
            if (record.status === 'taken')
                medicineData.taken++;
            else if (record.status === 'missed')
                medicineData.missed++;
            else if (record.status === 'skipped')
                medicineData.skipped++;
        });
        return Array.from(medicineMap.values()).map(data => ({
            ...data,
            rate: data.scheduled > 0 ? Math.round((data.taken / data.scheduled) * 10000) / 100 : 0,
        }));
    }
};
exports.AdherenceService = AdherenceService;
exports.AdherenceService = AdherenceService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], AdherenceService);
//# sourceMappingURL=adherence.service.js.map