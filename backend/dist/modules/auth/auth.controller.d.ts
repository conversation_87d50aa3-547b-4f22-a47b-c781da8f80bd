import { AuthService } from './auth.service';
import { LoginDto, RegisterDto, AuthResponseDto, UserResponseDto } from './dto';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<AuthResponseDto>;
    login(loginDto: LoginDto): Promise<AuthResponseDto>;
    getProfile(req: any): Promise<UserResponseDto>;
    logout(req: any): Promise<{
        message: string;
    }>;
}
