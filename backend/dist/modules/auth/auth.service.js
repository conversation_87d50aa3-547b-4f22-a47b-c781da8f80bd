"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const config_1 = require("@nestjs/config");
const bcrypt = require("bcrypt");
const supabase_service_1 = require("../../config/supabase.service");
let AuthService = class AuthService {
    jwtService;
    supabaseService;
    configService;
    constructor(jwtService, supabaseService, configService) {
        this.jwtService = jwtService;
        this.supabaseService = supabaseService;
        this.configService = configService;
    }
    async register(registerDto) {
        const { email, password, name, role, ...roleSpecificData } = registerDto;
        const { data: existingUser } = await this.supabaseService
            .getAdminClient()
            .from('users')
            .select('id')
            .eq('email', email)
            .single();
        if (existingUser) {
            throw new common_1.ConflictException('User with this email already exists');
        }
        const hashedPassword = await bcrypt.hash(password, 12);
        const { data: authUser, error: authError } = await this.supabaseService
            .getAdminClient()
            .auth.admin.createUser({
            email,
            password,
            email_confirm: true,
            user_metadata: {
                name,
                role,
            },
        });
        if (authError) {
            throw new common_1.BadRequestException(authError.message);
        }
        const { data: user, error: userError } = await this.supabaseService
            .getAdminClient()
            .from('users')
            .insert({
            id: authUser.user.id,
            email,
            name,
            role,
        })
            .select()
            .single();
        if (userError) {
            await this.supabaseService.getAdminClient().auth.admin.deleteUser(authUser.user.id);
            throw new common_1.BadRequestException(userError.message);
        }
        await this.createRoleSpecificRecord(user.id, role, roleSpecificData);
        const payload = { sub: user.id, email: user.email, role: user.role };
        const accessToken = this.jwtService.sign(payload);
        return {
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                role: user.role,
                avatar: user.avatar,
            },
            accessToken,
        };
    }
    async login(loginDto) {
        const { email, password } = loginDto;
        const { data: authData, error: authError } = await this.supabaseService
            .getClient()
            .auth.signInWithPassword({
            email,
            password,
        });
        if (authError) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const { data: user, error: userError } = await this.supabaseService
            .getAdminClient()
            .from('users')
            .select('*')
            .eq('id', authData.user.id)
            .single();
        if (userError || !user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const payload = { sub: user.id, email: user.email, role: user.role };
        const accessToken = this.jwtService.sign(payload);
        return {
            user: {
                id: user.id,
                email: user.email,
                name: user.name,
                role: user.role,
                avatar: user.avatar,
            },
            accessToken,
        };
    }
    async validateUser(userId) {
        const { data: user, error } = await this.supabaseService
            .getAdminClient()
            .from('users')
            .select('*')
            .eq('id', userId)
            .single();
        if (error || !user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        return user;
    }
    async logout(userId) {
        await this.supabaseService.getClient().auth.signOut();
    }
    async createRoleSpecificRecord(userId, role, data) {
        const supabase = this.supabaseService.getAdminClient();
        switch (role) {
            case 'patient':
                await supabase.from('patients').insert({
                    id: userId,
                    date_of_birth: data.dateOfBirth,
                    emergency_contact: data.emergencyContact,
                    assigned_doctor_id: data.assignedDoctorId,
                    insurance_id: data.insuranceId,
                });
                await supabase.from('gamification_stats').insert({
                    patient_id: userId,
                });
                break;
            case 'doctor':
                await supabase.from('doctors').insert({
                    id: userId,
                    specialization: data.specialization,
                    license_number: data.licenseNumber,
                    hospital_id: data.hospitalId,
                });
                break;
            case 'hospital':
                await supabase.from('hospitals').insert({
                    id: userId,
                    address: data.address,
                    phone: data.phone,
                    website: data.website,
                });
                break;
            case 'insurance':
                await supabase.from('insurance_providers').insert({
                    id: userId,
                    company_name: data.companyName,
                    address: data.address,
                    phone: data.phone,
                    website: data.website,
                    policy_types: data.policyTypes || [],
                    coverage_areas: data.coverageAreas || [],
                });
                break;
        }
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        supabase_service_1.SupabaseService,
        config_1.ConfigService])
], AuthService);
//# sourceMappingURL=auth.service.js.map