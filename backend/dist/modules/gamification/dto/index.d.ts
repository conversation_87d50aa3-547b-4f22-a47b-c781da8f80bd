export declare class GamificationStatsDto {
    patient_id: string;
}
export declare class UpdateGamificationStatsDto {
    current_streak?: number;
    longest_streak?: number;
    total_points?: number;
    completion_rate?: number;
    weekly_progress?: number[];
    monthly_progress?: number[];
}
export declare class LeaderboardQueryDto {
    limit?: number;
    type?: 'points' | 'streak' | 'completion_rate';
}
export interface GamificationLevel {
    level: number;
    name: string;
    min_points: number;
    max_points: number;
    benefits: string[];
}
export interface PointsCalculation {
    base_points: number;
    bonus_points: number;
    total_points: number;
    reason: string;
    multiplier?: number;
}
export interface LeaderboardEntry {
    patient_id: string;
    patient_name: string;
    rank: number;
    points: number;
    current_streak: number;
    completion_rate: number;
    level: GamificationLevel;
}
export interface GamificationDashboard {
    patient_id: string;
    current_stats: {
        total_points: number;
        current_streak: number;
        longest_streak: number;
        completion_rate: number;
        level: GamificationLevel;
        points_to_next_level: number;
    };
    recent_achievements: Array<{
        achievement_id: string;
        achievement_name: string;
        points_earned: number;
        unlocked_at: Date;
    }>;
    weekly_progress: {
        current_week: number[];
        previous_week: number[];
        improvement: number;
    };
    monthly_progress: {
        current_month: number[];
        previous_month: number[];
        improvement: number;
    };
    next_milestones: Array<{
        type: 'streak' | 'points' | 'completion';
        target: number;
        current: number;
        progress_percentage: number;
        estimated_days: number;
    }>;
}
export declare const GAMIFICATION_LEVELS: GamificationLevel[];
export declare const POINTS_CONFIG: {
    MEDICATION_ON_TIME: number;
    MEDICATION_LATE: number;
    PERFECT_DAY_BONUS: number;
    WEEKLY_STREAK_BONUS: number;
    MONTHLY_CONSISTENCY_BONUS: number;
    ACHIEVEMENT_MULTIPLIER: number;
    STREAK_MULTIPLIER_THRESHOLD: number;
    STREAK_MULTIPLIER: number;
};
