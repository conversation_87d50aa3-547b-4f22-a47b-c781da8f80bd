import { GamificationService } from './gamification.service';
import { UpdateGamificationStatsDto, LeaderboardQueryDto, LeaderboardEntry, GamificationDashboard } from './dto';
import { GamificationStats } from '../../common/types';
export declare class GamificationController {
    private readonly gamificationService;
    constructor(gamificationService: GamificationService);
    getStats(patientId: string, req: any): Promise<GamificationStats>;
    getDashboard(patientId: string, req: any): Promise<GamificationDashboard>;
    getLeaderboard(query: LeaderboardQueryDto, req: any): Promise<LeaderboardEntry[]>;
    updateStats(patientId: string, updateDto: UpdateGamificationStatsDto, req: any): Promise<GamificationStats>;
    calculatePoints(patientId: string, body: {
        adherence_status: string;
        scheduled_time: string;
        taken_time?: string;
    }, req: any): Promise<import("./dto").PointsCalculation>;
    getLevels(): Promise<import("./dto").GamificationLevel[]>;
    getPointsConfig(): Promise<{
        MEDICATION_ON_TIME: number;
        MEDICATION_LATE: number;
        PERFECT_DAY_BONUS: number;
        WEEKLY_STREAK_BONUS: number;
        MONTHLY_CONSISTENCY_BONUS: number;
        ACHIEVEMENT_MULTIPLIER: number;
        STREAK_MULTIPLIER_THRESHOLD: number;
        STREAK_MULTIPLIER: number;
    }>;
}
