export declare class HospitalPatientsQueryDto {
    page?: number;
    limit?: number;
    search?: string;
    doctor_id?: string;
    specialization?: string;
}
export declare class HospitalAdherenceReportQueryDto {
    days?: number;
    include_medications?: boolean;
    include_gamification?: boolean;
}
export declare class HospitalBulkAdherenceQueryDto {
    patient_ids: string[];
    days?: number;
    include_details?: boolean;
}
export declare class HospitalDepartmentAnalysisQueryDto {
    days?: number;
    department?: string;
    min_adherence_rate?: number;
    risk_level?: 'low' | 'medium' | 'high' | 'critical';
}
export interface HospitalPatient {
    patient_id: string;
    patient_name: string;
    patient_email: string;
    date_of_birth?: Date;
    emergency_contact?: string;
    assigned_doctor: {
        id: string;
        name: string;
        specialization: string;
    };
    admission_date?: Date;
    current_medications_count: number;
    last_adherence_check: Date;
}
export interface HospitalAdherenceReport {
    patient_id: string;
    patient_name: string;
    hospital_id: string;
    report_date: Date;
    analysis_period: {
        start_date: Date;
        end_date: Date;
        days_analyzed: number;
    };
    overall_adherence: {
        adherence_rate: number;
        total_doses: number;
        taken_doses: number;
        missed_doses: number;
        current_streak: number;
        longest_streak: number;
    };
    medication_details?: {
        medicine_id: string;
        medicine_name: string;
        dosage: string;
        frequency: string;
        adherence_rate: number;
        missed_doses: number;
        last_taken?: Date;
    }[];
    weekly_trends: {
        week_start: Date;
        adherence_rate: number;
        improvement: number;
    }[];
    assigned_doctor: {
        id: string;
        name: string;
        specialization: string;
    };
    gamification_data?: {
        total_points: number;
        current_streak: number;
        longest_streak: number;
        completion_rate: number;
        level: string;
    };
    recommendations: string[];
    risk_assessment: {
        risk_level: 'low' | 'medium' | 'high' | 'critical';
        risk_factors: string[];
        intervention_needed: boolean;
    };
}
export interface HospitalBulkAdherenceReport {
    hospital_id: string;
    report_date: Date;
    analysis_period: {
        start_date: Date;
        end_date: Date;
        days_analyzed: number;
    };
    summary: {
        total_patients: number;
        average_adherence_rate: number;
        patients_above_threshold: number;
        patients_needing_intervention: number;
    };
    patient_summaries: {
        patient_id: string;
        patient_name: string;
        adherence_rate: number;
        risk_level: 'low' | 'medium' | 'high' | 'critical';
        assigned_doctor: string;
        department: string;
    }[];
    detailed_reports?: HospitalAdherenceReport[];
    department_breakdown: {
        department: string;
        patient_count: number;
        average_adherence: number;
        high_risk_patients: number;
    }[];
}
export interface HospitalDepartmentAnalysis {
    hospital_id: string;
    analysis_date: Date;
    criteria: {
        analysis_period_days: number;
        department_filter?: string;
        min_adherence_rate: number;
        risk_level_filter?: string;
    };
    department_performance: {
        department: string;
        doctor_count: number;
        patient_count: number;
        average_adherence_rate: number;
        high_risk_patients: number;
        top_performing_doctor: {
            id: string;
            name: string;
            patient_count: number;
            average_adherence: number;
        };
        improvement_opportunities: string[];
    }[];
    overall_hospital_metrics: {
        total_departments: number;
        total_doctors: number;
        total_patients: number;
        hospital_average_adherence: number;
        best_performing_department: string;
        departments_needing_attention: string[];
    };
    recommendations: {
        department: string;
        priority: 'high' | 'medium' | 'low';
        action_items: string[];
    }[];
}
export interface HospitalDashboardSummary {
    hospital_id: string;
    hospital_name: string;
    summary_date: Date;
    overview: {
        total_patients: number;
        total_doctors: number;
        total_departments: number;
        active_prescriptions: number;
    };
    adherence_metrics: {
        overall_adherence_rate: number;
        patients_above_80_percent: number;
        patients_needing_intervention: number;
        trend_direction: 'improving' | 'declining' | 'stable';
    };
    department_highlights: {
        best_performing: {
            department: string;
            adherence_rate: number;
        };
        needs_attention: {
            department: string;
            adherence_rate: number;
            patient_count: number;
        };
    };
    recent_alerts: {
        critical_patients: number;
        missed_appointments: number;
        medication_adherence_alerts: number;
    };
    quick_actions: {
        patients_to_contact: number;
        reports_pending_review: number;
        doctors_to_notify: number;
    };
}
