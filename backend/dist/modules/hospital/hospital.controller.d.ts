import { HospitalService } from './hospital.service';
import { HospitalPatientsQueryDto, HospitalAdherenceReportQueryDto, HospitalBulkAdherenceQueryDto, HospitalDepartmentAnalysisQueryDto, HospitalPatient, HospitalAdherenceReport, HospitalBulkAdherenceReport, HospitalDepartmentAnalysis, HospitalDashboardSummary } from './dto';
export declare class HospitalController {
    private readonly hospitalService;
    constructor(hospitalService: HospitalService);
    getHospitalPatients(query: HospitalPatientsQueryDto, req: any): Promise<{
        data: HospitalPatient[];
        total: number;
        page: number;
        limit: number;
    }>;
    getPatientAdherenceReport(patientId: string, query: HospitalAdherenceReportQueryDto, req: any): Promise<HospitalAdherenceReport>;
    getBulkAdherenceReport(query: HospitalBulkAdherenceQueryDto, req: any): Promise<HospitalBulkAdherenceReport>;
    getDepartmentAnalysis(query: HospitalDepartmentAnalysisQueryDto, req: any): Promise<HospitalDepartmentAnalysis>;
    getDashboardSummary(req: any): Promise<HospitalDashboardSummary>;
    getPatientSummary(patientId: string, req: any): Promise<any>;
}
