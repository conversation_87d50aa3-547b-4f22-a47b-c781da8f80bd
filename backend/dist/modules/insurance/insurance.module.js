"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsuranceModule = void 0;
const common_1 = require("@nestjs/common");
const insurance_controller_1 = require("./insurance.controller");
const insurance_service_1 = require("./insurance.service");
const supabase_service_1 = require("../../config/supabase.service");
const adherence_service_1 = require("../adherence/adherence.service");
const gamification_service_1 = require("../gamification/gamification.service");
let InsuranceModule = class InsuranceModule {
};
exports.InsuranceModule = InsuranceModule;
exports.InsuranceModule = InsuranceModule = __decorate([
    (0, common_1.Module)({
        controllers: [insurance_controller_1.InsuranceController],
        providers: [
            insurance_service_1.InsuranceService,
            supabase_service_1.SupabaseService,
            adherence_service_1.AdherenceService,
            gamification_service_1.GamificationService,
        ],
        exports: [insurance_service_1.InsuranceService],
    })
], InsuranceModule);
//# sourceMappingURL=insurance.module.js.map