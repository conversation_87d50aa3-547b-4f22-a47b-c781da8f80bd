"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsuranceService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../config/supabase.service");
const adherence_service_1 = require("../adherence/adherence.service");
const gamification_service_1 = require("../gamification/gamification.service");
let InsuranceService = class InsuranceService {
    supabaseService;
    adherenceService;
    gamificationService;
    constructor(supabaseService, adherenceService, gamificationService) {
        this.supabaseService = supabaseService;
        this.adherenceService = adherenceService;
        this.gamificationService = gamificationService;
    }
    validateInsuranceProvider(currentUser) {
        if (currentUser.role !== 'insurance' && currentUser.role !== 'admin') {
            throw new common_1.ForbiddenException('Only insurance providers can access this data');
        }
    }
    async getPolicyHolders(query, currentUser) {
        this.validateInsuranceProvider(currentUser);
        const supabase = this.supabaseService.getClient();
        const page = query.page || 1;
        const limit = query.limit || 20;
        const offset = (page - 1) * limit;
        let dbQuery = supabase
            .from('patients')
            .select(`
        id,
        users!patients_id_fkey(name, email, created_at),
        date_of_birth,
        emergency_contact,
        assigned_doctor_id,
        doctors:assigned_doctor_id(
          id,
          users!doctors_id_fkey(name),
          specialization
        ),
        created_at
      `)
            .eq('insurance_id', currentUser.id);
        if (query.search) {
            dbQuery = dbQuery.or(`users.name.ilike.%${query.search}%,users.email.ilike.%${query.search}%`);
        }
        const { count } = await supabase
            .from('patients')
            .select('*', { count: 'exact', head: true })
            .eq('insurance_id', currentUser.id);
        const { data: patients, error } = await dbQuery
            .range(offset, offset + limit - 1)
            .order('created_at', { ascending: false });
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch policy holders: ${error.message}`);
        }
        const policyHolders = await Promise.all((patients || []).map(async (patient) => {
            const { count: medicationsCount } = await supabase
                .from('medicines')
                .select('*', { count: 'exact', head: true })
                .eq('patient_id', patient.id)
                .gte('end_date', new Date().toISOString().split('T')[0]);
            const { data: lastAdherence } = await supabase
                .from('adherence_records')
                .select('created_at')
                .eq('patient_id', patient.id)
                .order('created_at', { ascending: false })
                .limit(1)
                .single();
            return {
                patient_id: patient.id,
                patient_name: patient.users?.name || 'Unknown',
                patient_email: patient.users?.email || 'Unknown',
                date_of_birth: patient.date_of_birth ? new Date(patient.date_of_birth) : undefined,
                emergency_contact: patient.emergency_contact,
                assigned_doctor: patient.doctors ? {
                    id: patient.doctors.id,
                    name: patient.doctors.users?.name || 'Unknown',
                    specialization: patient.doctors.specialization,
                } : undefined,
                enrollment_date: new Date(patient.created_at),
                policy_type: 'Standard',
                coverage_area: 'General',
                active_medications_count: medicationsCount || 0,
                last_adherence_update: lastAdherence ? new Date(lastAdherence.created_at) : new Date(),
            };
        }));
        return {
            data: policyHolders,
            total: count || 0,
            page,
            limit,
        };
    }
    async getPatientAdherenceReport(patientId, query, currentUser) {
        this.validateInsuranceProvider(currentUser);
        await this.validatePatientAccess(patientId, currentUser);
        const supabase = this.supabaseService.getClient();
        const { data: patient, error: patientError } = await supabase
            .from('patients')
            .select(`
        id,
        users!patients_id_fkey(name, email)
      `)
            .eq('id', patientId)
            .single();
        if (patientError || !patient) {
            throw new common_1.NotFoundException(`Patient with ID ${patientId} not found`);
        }
        const endDate = query.end_date ? new Date(query.end_date) : new Date();
        const days = query.days || 30;
        const startDate = query.start_date
            ? new Date(query.start_date)
            : new Date(endDate.getTime() - (days * 24 * 60 * 60 * 1000));
        const adherenceAnalytics = await this.adherenceService.getAnalytics({ patient_id: patientId, days: query.days }, { role: 'admin' });
        let medicationDetails = undefined;
        if (query.include_medicine_details) {
            const { data: medicines } = await supabase
                .from('medicines')
                .select(`
          id,
          name,
          dosage,
          frequency,
          adherence_records(status, taken_time)
        `)
                .eq('patient_id', patientId)
                .gte('end_date', startDate.toISOString().split('T')[0]);
            medicationDetails = (medicines || []).map(medicine => {
                const records = medicine.adherence_records || [];
                const takenRecords = records.filter(r => r.status === 'taken');
                const missedRecords = records.filter(r => r.status === 'missed');
                const adherenceRate = records.length > 0 ? (takenRecords.length / records.length) * 100 : 0;
                const lastTaken = takenRecords.length > 0
                    ? new Date(Math.max(...takenRecords.map(r => new Date(r.taken_time).getTime())))
                    : undefined;
                return {
                    medicine_id: medicine.id,
                    medicine_name: medicine.name,
                    dosage: medicine.dosage,
                    frequency: medicine.frequency,
                    adherence_rate: Math.round(adherenceRate * 100) / 100,
                    missed_doses: missedRecords.length,
                    last_taken: lastTaken,
                };
            });
        }
        let gamificationData = undefined;
        if (query.include_gamification) {
            try {
                const stats = await this.gamificationService.getStats(patientId, { role: 'admin' });
                const dashboard = await this.gamificationService.getDashboard(patientId, { role: 'admin' });
                gamificationData = {
                    total_points: stats.total_points,
                    current_streak: stats.current_streak,
                    longest_streak: stats.longest_streak,
                    completion_rate: stats.completion_rate,
                    level: dashboard.current_stats.level.name,
                };
            }
            catch (error) {
                console.warn('Failed to fetch gamification data:', error);
            }
        }
        const riskAssessment = this.calculateRiskAssessment(adherenceAnalytics, medicationDetails);
        const trends = this.calculateTrends(adherenceAnalytics);
        return {
            patient_id: patientId,
            patient_name: patient.users?.name || 'Unknown',
            report_period: {
                start_date: startDate,
                end_date: endDate,
                days_analyzed: days,
            },
            overall_adherence: {
                total_scheduled: adherenceAnalytics.total_scheduled,
                total_taken: adherenceAnalytics.total_taken,
                total_missed: adherenceAnalytics.total_missed,
                total_skipped: adherenceAnalytics.total_skipped,
                adherence_rate: adherenceAnalytics.adherence_rate,
                on_time_rate: adherenceAnalytics.on_time_rate,
                late_rate: adherenceAnalytics.late_rate,
            },
            risk_assessment: riskAssessment,
            medication_details: medicationDetails,
            gamification_data: gamificationData,
            trends,
        };
    }
    async validatePatientAccess(patientId, currentUser) {
        if (currentUser.role === 'admin')
            return;
        const supabase = this.supabaseService.getClient();
        const { data: patient, error } = await supabase
            .from('patients')
            .select('insurance_id')
            .eq('id', patientId)
            .single();
        if (error || !patient) {
            throw new common_1.NotFoundException(`Patient with ID ${patientId} not found`);
        }
        if (patient.insurance_id !== currentUser.id) {
            throw new common_1.ForbiddenException('You do not have access to this patient\'s data');
        }
    }
    calculateRiskAssessment(adherenceAnalytics, medicationDetails) {
        const adherenceRate = adherenceAnalytics.adherence_rate;
        const currentStreak = adherenceAnalytics.current_streak;
        const missedCount = adherenceAnalytics.total_missed;
        let riskScore = 0;
        let riskLevel = 'low';
        const factors = [];
        const recommendations = [];
        if (adherenceRate < 50) {
            riskScore += 40;
            factors.push('Very low adherence rate');
            recommendations.push('Immediate intervention required');
        }
        else if (adherenceRate < 70) {
            riskScore += 25;
            factors.push('Low adherence rate');
            recommendations.push('Enhanced monitoring and support needed');
        }
        else if (adherenceRate < 85) {
            riskScore += 10;
            factors.push('Moderate adherence concerns');
            recommendations.push('Regular check-ins recommended');
        }
        if (currentStreak === 0) {
            riskScore += 20;
            factors.push('No current adherence streak');
            recommendations.push('Focus on building consistent habits');
        }
        else if (currentStreak < 3) {
            riskScore += 10;
            factors.push('Short adherence streak');
        }
        if (missedCount > 10) {
            riskScore += 15;
            factors.push('High number of missed doses');
            recommendations.push('Review medication schedule and barriers');
        }
        else if (missedCount > 5) {
            riskScore += 8;
            factors.push('Moderate missed doses');
        }
        if (riskScore >= 60) {
            riskLevel = 'critical';
        }
        else if (riskScore >= 40) {
            riskLevel = 'high';
        }
        else if (riskScore >= 20) {
            riskLevel = 'medium';
        }
        if (riskLevel === 'low') {
            recommendations.push('Continue current adherence patterns');
        }
        return {
            level: riskLevel,
            score: riskScore,
            factors,
            recommendations,
        };
    }
    calculateTrends(adherenceAnalytics) {
        const dailyBreakdown = adherenceAnalytics.daily_breakdown || [];
        const weeklyAdherence = [];
        for (let i = 0; i < dailyBreakdown.length; i += 7) {
            const weekData = dailyBreakdown.slice(i, i + 7);
            const weekTotal = weekData.reduce((sum, day) => sum + day.scheduled, 0);
            const weekTaken = weekData.reduce((sum, day) => sum + day.taken, 0);
            const weekRate = weekTotal > 0 ? (weekTaken / weekTotal) * 100 : 0;
            weeklyAdherence.push(Math.round(weekRate * 100) / 100);
        }
        let improvementTrend = 'stable';
        if (weeklyAdherence.length >= 2) {
            const recent = weeklyAdherence.slice(-2);
            const diff = recent[1] - recent[0];
            if (diff > 5) {
                improvementTrend = 'improving';
            }
            else if (diff < -5) {
                improvementTrend = 'declining';
            }
        }
        const avgRate = weeklyAdherence.reduce((sum, rate) => sum + rate, 0) / weeklyAdherence.length;
        const variance = weeklyAdherence.reduce((sum, rate) => sum + Math.pow(rate - avgRate, 2), 0) / weeklyAdherence.length;
        const consistencyScore = Math.max(0, 100 - Math.sqrt(variance));
        return {
            weekly_adherence: weeklyAdherence,
            improvement_trend: improvementTrend,
            consistency_score: Math.round(consistencyScore * 100) / 100,
        };
    }
    async getBulkAdherenceReport(query, currentUser) {
        this.validateInsuranceProvider(currentUser);
        for (const patientId of query.patient_ids) {
            await this.validatePatientAccess(patientId, currentUser);
        }
        const reportGeneratedAt = new Date();
        const patientSummaries = [];
        const detailedReports = [];
        let totalMedicationsTracked = 0;
        for (const patientId of query.patient_ids) {
            try {
                const adherenceAnalytics = await this.adherenceService.getAnalytics({ patient_id: patientId, days: query.days }, { role: 'admin' });
                const riskAssessment = this.calculateRiskAssessment(adherenceAnalytics);
                const supabase = this.supabaseService.getClient();
                const { count: medicationsCount } = await supabase
                    .from('medicines')
                    .select('*', { count: 'exact', head: true })
                    .eq('patient_id', patientId)
                    .gte('end_date', new Date().toISOString().split('T')[0]);
                totalMedicationsTracked += medicationsCount || 0;
                const { data: patient } = await supabase
                    .from('patients')
                    .select('users!patients_id_fkey(name)')
                    .eq('id', patientId)
                    .single();
                const patientSummary = {
                    patient_id: patientId,
                    patient_name: patient?.users?.name || 'Unknown',
                    adherence_rate: adherenceAnalytics.adherence_rate,
                    risk_level: riskAssessment.level,
                    active_medications: medicationsCount || 0,
                    last_update: new Date(),
                };
                patientSummaries.push(patientSummary);
                if (query.include_details) {
                    const detailedReport = await this.getPatientAdherenceReport(patientId, { days: query.days, include_medicine_details: true, include_gamification: false }, currentUser);
                    detailedReports.push(detailedReport);
                }
            }
            catch (error) {
                console.error(`Failed to process patient ${patientId}:`, error);
            }
        }
        const averageAdherenceRate = patientSummaries.length > 0
            ? patientSummaries.reduce((sum, p) => sum + p.adherence_rate, 0) / patientSummaries.length
            : 0;
        const riskCounts = patientSummaries.reduce((counts, patient) => {
            counts[patient.risk_level]++;
            return counts;
        }, { low: 0, medium: 0, high: 0, critical: 0 });
        return {
            insurance_provider_id: currentUser.id,
            report_generated_at: reportGeneratedAt,
            total_patients: query.patient_ids.length,
            summary: {
                average_adherence_rate: Math.round(averageAdherenceRate * 100) / 100,
                high_risk_patients: riskCounts.high + riskCounts.critical,
                medium_risk_patients: riskCounts.medium,
                low_risk_patients: riskCounts.low,
                total_medications_tracked: totalMedicationsTracked,
            },
            patient_summaries: patientSummaries,
            detailed_reports: query.include_details ? detailedReports : undefined,
        };
    }
    async getRiskAssessmentReport(query, currentUser) {
        this.validateInsuranceProvider(currentUser);
        const supabase = this.supabaseService.getClient();
        const { data: patients, error } = await supabase
            .from('patients')
            .select(`
        id,
        users!patients_id_fkey(name, email)
      `)
            .eq('insurance_id', currentUser.id);
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch patients: ${error.message}`);
        }
        const highRiskPatients = [];
        const statistics = {
            total_patients_assessed: patients?.length || 0,
            critical_risk_count: 0,
            high_risk_count: 0,
            medium_risk_count: 0,
            low_risk_count: 0,
            average_adherence_rate: 0,
            patients_needing_intervention: 0,
        };
        let totalAdherenceRate = 0;
        let patientsWithData = 0;
        const days = query.days || 30;
        const minAdherenceRate = query.min_adherence_rate || 0;
        const maxAdherenceRate = query.max_adherence_rate || 100;
        for (const patient of patients || []) {
            try {
                const adherenceAnalytics = await this.adherenceService.getAnalytics({ patient_id: patient.id, days }, { role: 'admin' });
                const riskAssessment = this.calculateRiskAssessment(adherenceAnalytics);
                const adherenceRate = adherenceAnalytics.adherence_rate;
                statistics[`${riskAssessment.level}_risk_count`]++;
                totalAdherenceRate += adherenceRate;
                patientsWithData++;
                const meetsAdherenceFilter = adherenceRate >= minAdherenceRate &&
                    adherenceRate <= maxAdherenceRate;
                const meetsRiskFilter = !query.risk_level || riskAssessment.level === query.risk_level;
                if (meetsAdherenceFilter && meetsRiskFilter &&
                    (riskAssessment.level === 'high' || riskAssessment.level === 'critical')) {
                    const contactPriority = riskAssessment.level === 'critical' ? 'immediate' :
                        riskAssessment.score >= 50 ? 'urgent' : 'routine';
                    highRiskPatients.push({
                        patient_id: patient.id,
                        patient_name: patient.users?.name || 'Unknown',
                        adherence_rate: adherenceRate,
                        risk_score: riskAssessment.score,
                        risk_factors: riskAssessment.factors,
                        recommended_actions: riskAssessment.recommendations,
                        contact_priority: contactPriority,
                    });
                    if (riskAssessment.level === 'high' || riskAssessment.level === 'critical') {
                        statistics.patients_needing_intervention++;
                    }
                }
            }
            catch (error) {
                console.error(`Failed to assess patient ${patient.id}:`, error);
            }
        }
        statistics.average_adherence_rate = patientsWithData > 0
            ? Math.round((totalAdherenceRate / patientsWithData) * 100) / 100
            : 0;
        highRiskPatients.sort((a, b) => b.risk_score - a.risk_score);
        return {
            insurance_provider_id: currentUser.id,
            assessment_date: new Date(),
            criteria: {
                min_adherence_rate: minAdherenceRate,
                max_adherence_rate: maxAdherenceRate,
                analysis_period_days: days,
                risk_level_filter: query.risk_level,
            },
            high_risk_patients: highRiskPatients,
            statistics,
        };
    }
};
exports.InsuranceService = InsuranceService;
exports.InsuranceService = InsuranceService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService,
        adherence_service_1.AdherenceService,
        gamification_service_1.GamificationService])
], InsuranceService);
//# sourceMappingURL=insurance.service.js.map