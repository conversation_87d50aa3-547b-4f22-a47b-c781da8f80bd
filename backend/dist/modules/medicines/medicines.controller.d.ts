import { MedicinesService } from './medicines.service';
import { CreateMedicineDto, UpdateMedicineDto, MedicineQueryDto } from './dto';
import { User } from '../../common/types';
export declare class MedicinesController {
    private readonly medicinesService;
    constructor(medicinesService: MedicinesService);
    create(createMedicineDto: CreateMedicineDto, currentUser: User): Promise<import("../../common/types").Medicine>;
    findAll(query: MedicineQueryDto, currentUser: User): Promise<import("../../common/types").Medicine[]>;
    findByPatient(patientId: string, currentUser: User): Promise<import("../../common/types").Medicine[]>;
    findByPrescription(prescriptionId: string, currentUser: User): Promise<import("../../common/types").Medicine[]>;
    findOne(id: string, currentUser: User): Promise<import("../../common/types").Medicine>;
    update(id: string, updateMedicineDto: UpdateMedicineDto, currentUser: User): Promise<import("../../common/types").Medicine>;
    remove(id: string, currentUser: User): Promise<{
        message: string;
    }>;
}
