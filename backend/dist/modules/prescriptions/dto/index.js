"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessPrescriptionDto = exports.PrescriptionQueryDto = exports.UpdatePrescriptionDto = exports.CreatePrescriptionDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreatePrescriptionDto {
    patient_id;
    doctor_id;
    filename;
    file_url;
    extracted_text;
}
exports.CreatePrescriptionDto = CreatePrescriptionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'uuid-of-patient' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreatePrescriptionDto.prototype, "patient_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'uuid-of-doctor' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreatePrescriptionDto.prototype, "doctor_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'prescription_scan.pdf' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePrescriptionDto.prototype, "filename", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'https://example.com/uploads/prescription_scan.pdf' }),
    (0, class_validator_1.IsUrl)(),
    __metadata("design:type", String)
], CreatePrescriptionDto.prototype, "file_url", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'Extracted text from prescription...' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreatePrescriptionDto.prototype, "extracted_text", void 0);
class UpdatePrescriptionDto {
    doctor_id;
    status;
    extracted_text;
}
exports.UpdatePrescriptionDto = UpdatePrescriptionDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'uuid-of-doctor' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], UpdatePrescriptionDto.prototype, "doctor_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'processing', enum: ['processing', 'completed', 'failed'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['processing', 'completed', 'failed']),
    __metadata("design:type", String)
], UpdatePrescriptionDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'Updated extracted text from prescription...' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdatePrescriptionDto.prototype, "extracted_text", void 0);
class PrescriptionQueryDto {
    patient_id;
    doctor_id;
    status;
    filename;
}
exports.PrescriptionQueryDto = PrescriptionQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'uuid-of-patient' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PrescriptionQueryDto.prototype, "patient_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'uuid-of-doctor' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], PrescriptionQueryDto.prototype, "doctor_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'processing', enum: ['processing', 'completed', 'failed', 'all'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PrescriptionQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'prescription' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PrescriptionQueryDto.prototype, "filename", void 0);
class ProcessPrescriptionDto {
    status;
    extracted_text;
}
exports.ProcessPrescriptionDto = ProcessPrescriptionDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'processing', enum: ['processing', 'completed', 'failed'] }),
    (0, class_validator_1.IsEnum)(['processing', 'completed', 'failed']),
    __metadata("design:type", String)
], ProcessPrescriptionDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'Extracted text from AI processing...' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ProcessPrescriptionDto.prototype, "extracted_text", void 0);
//# sourceMappingURL=index.js.map