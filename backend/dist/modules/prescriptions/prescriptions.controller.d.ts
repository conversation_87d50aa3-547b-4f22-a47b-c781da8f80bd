import { PrescriptionsService } from './prescriptions.service';
import { CreatePrescriptionDto, UpdatePrescriptionDto, PrescriptionQueryDto, ProcessPrescriptionDto } from './dto';
import { User } from '../../common/types';
export declare class PrescriptionsController {
    private readonly prescriptionsService;
    constructor(prescriptionsService: PrescriptionsService);
    create(createPrescriptionDto: CreatePrescriptionDto, currentUser: User): Promise<import("../../common/types").Prescription>;
    uploadPrescription(file: Express.Multer.File, patientId: string, doctorId: string, currentUser: User): Promise<import("../../common/types").Prescription>;
    findAll(query: PrescriptionQueryDto, currentUser: User): Promise<import("../../common/types").Prescription[]>;
    findByPatient(patientId: string, currentUser: User): Promise<import("../../common/types").Prescription[]>;
    findByDoctor(doctorId: string, currentUser: User): Promise<import("../../common/types").Prescription[]>;
    findOne(id: string, currentUser: User): Promise<import("../../common/types").Prescription>;
    update(id: string, updatePrescriptionDto: UpdatePrescriptionDto, currentUser: User): Promise<import("../../common/types").Prescription>;
    processPrescription(id: string, processPrescriptionDto: ProcessPrescriptionDto, currentUser: User): Promise<import("../../common/types").Prescription>;
    remove(id: string, currentUser: User): Promise<{
        message: string;
    }>;
}
