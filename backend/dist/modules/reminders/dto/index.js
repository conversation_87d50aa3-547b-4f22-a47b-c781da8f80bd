"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkReminderDto = exports.ReminderLogDto = exports.ReminderQueryDto = exports.UpdateReminderDto = exports.CreateReminderDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
class CreateReminderDto {
    patient_id;
    medicine_id;
    reminder_type;
    scheduled_time;
    message;
    is_recurring;
    recurrence_pattern;
    recurrence_interval;
    recurrence_days;
    end_date;
}
exports.CreateReminderDto = CreateReminderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'uuid-of-patient' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateReminderDto.prototype, "patient_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'uuid-of-medicine' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], CreateReminderDto.prototype, "medicine_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'sms', enum: ['sms', 'call', 'both'] }),
    (0, class_validator_1.IsEnum)(['sms', 'call', 'both']),
    __metadata("design:type", String)
], CreateReminderDto.prototype, "reminder_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2024-01-15T09:00:00Z' }),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateReminderDto.prototype, "scheduled_time", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'Take your morning medication' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateReminderDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateReminderDto.prototype, "is_recurring", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'daily', enum: ['daily', 'weekly', 'monthly'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['daily', 'weekly', 'monthly']),
    __metadata("design:type", String)
], CreateReminderDto.prototype, "recurrence_pattern", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(30),
    __metadata("design:type", Number)
], CreateReminderDto.prototype, "recurrence_interval", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: ['monday', 'wednesday', 'friday'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], CreateReminderDto.prototype, "recurrence_days", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '2024-12-31T23:59:59Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateReminderDto.prototype, "end_date", void 0);
class UpdateReminderDto {
    reminder_type;
    scheduled_time;
    message;
    is_recurring;
    recurrence_pattern;
    recurrence_interval;
    recurrence_days;
    end_date;
    is_active;
}
exports.UpdateReminderDto = UpdateReminderDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'sms', enum: ['sms', 'call', 'both'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['sms', 'call', 'both']),
    __metadata("design:type", String)
], UpdateReminderDto.prototype, "reminder_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '2024-01-15T09:00:00Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateReminderDto.prototype, "scheduled_time", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'Take your morning medication' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], UpdateReminderDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateReminderDto.prototype, "is_recurring", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'daily', enum: ['daily', 'weekly', 'monthly'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['daily', 'weekly', 'monthly']),
    __metadata("design:type", String)
], UpdateReminderDto.prototype, "recurrence_pattern", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(30),
    __metadata("design:type", Number)
], UpdateReminderDto.prototype, "recurrence_interval", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: ['monday', 'wednesday', 'friday'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], UpdateReminderDto.prototype, "recurrence_days", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '2024-12-31T23:59:59Z' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateReminderDto.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateReminderDto.prototype, "is_active", void 0);
class ReminderQueryDto {
    patient_id;
    medicine_id;
    status;
    reminder_type;
    date_from;
    date_to;
    is_active;
}
exports.ReminderQueryDto = ReminderQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'uuid-of-patient' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ReminderQueryDto.prototype, "patient_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'uuid-of-medicine' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ReminderQueryDto.prototype, "medicine_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'pending', enum: ['pending', 'sent', 'failed', 'all'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReminderQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'sms', enum: ['sms', 'call', 'both', 'all'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReminderQueryDto.prototype, "reminder_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '2024-01-15' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ReminderQueryDto.prototype, "date_from", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: '2024-01-31' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], ReminderQueryDto.prototype, "date_to", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ReminderQueryDto.prototype, "is_active", void 0);
class ReminderLogDto {
    reminder_id;
    status;
    message;
    error_message;
    external_id;
}
exports.ReminderLogDto = ReminderLogDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'uuid-of-reminder' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ReminderLogDto.prototype, "reminder_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'sent', enum: ['sent', 'failed', 'delivered', 'read'] }),
    (0, class_validator_1.IsEnum)(['sent', 'failed', 'delivered', 'read']),
    __metadata("design:type", String)
], ReminderLogDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'SMS sent successfully' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReminderLogDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'Failed to send: Invalid phone number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReminderLogDto.prototype, "error_message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'twilio_message_id_123' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReminderLogDto.prototype, "external_id", void 0);
class BulkReminderDto {
    patient_ids;
    medicine_id;
    reminder_type;
    time;
    message;
    is_recurring;
    recurrence_pattern;
}
exports.BulkReminderDto = BulkReminderDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: ['uuid1', 'uuid2', 'uuid3'] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsUUID)(undefined, { each: true }),
    __metadata("design:type", Array)
], BulkReminderDto.prototype, "patient_ids", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'uuid-of-medicine' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], BulkReminderDto.prototype, "medicine_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'sms', enum: ['sms', 'call', 'both'] }),
    (0, class_validator_1.IsEnum)(['sms', 'call', 'both']),
    __metadata("design:type", String)
], BulkReminderDto.prototype, "reminder_type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '09:00' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BulkReminderDto.prototype, "time", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'Take your daily medication' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BulkReminderDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], BulkReminderDto.prototype, "is_recurring", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'daily', enum: ['daily', 'weekly', 'monthly'] }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['daily', 'weekly', 'monthly']),
    __metadata("design:type", String)
], BulkReminderDto.prototype, "recurrence_pattern", void 0);
//# sourceMappingURL=index.js.map