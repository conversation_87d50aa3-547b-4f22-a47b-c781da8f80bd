"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RemindersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const reminders_service_1 = require("./reminders.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let RemindersController = class RemindersController {
    remindersService;
    constructor(remindersService) {
        this.remindersService = remindersService;
    }
    async create(createReminderDto, currentUser) {
        return this.remindersService.create(createReminderDto, currentUser);
    }
    async createBulk(bulkReminderDto, currentUser) {
        return this.remindersService.createBulkReminders(bulkReminderDto, currentUser);
    }
    async findAll(query, currentUser) {
        return this.remindersService.findAll(query, currentUser);
    }
    async findByPatient(patientId, currentUser) {
        return this.remindersService.findAll({ patient_id: patientId }, currentUser);
    }
    async findByMedicine(medicineId, currentUser) {
        return this.remindersService.findAll({ medicine_id: medicineId }, currentUser);
    }
    async findOne(id, currentUser) {
        return this.remindersService.findOne(id, currentUser);
    }
    async update(id, updateReminderDto, currentUser) {
        return this.remindersService.update(id, updateReminderDto, currentUser);
    }
    async remove(id, currentUser) {
        await this.remindersService.remove(id, currentUser);
        return { message: 'Reminder deleted successfully' };
    }
};
exports.RemindersController = RemindersController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new reminder' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Reminder created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateReminderDto, Object]),
    __metadata("design:returntype", Promise)
], RemindersController.prototype, "create", null);
__decorate([
    (0, common_1.Post)('bulk'),
    (0, swagger_1.ApiOperation)({ summary: 'Create bulk reminders for multiple patients' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Bulk reminders created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkReminderDto, Object]),
    __metadata("design:returntype", Promise)
], RemindersController.prototype, "createBulk", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all reminders' }),
    (0, swagger_1.ApiQuery)({ name: 'patient_id', required: false, description: 'Filter by patient ID' }),
    (0, swagger_1.ApiQuery)({ name: 'medicine_id', required: false, description: 'Filter by medicine ID' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: ['pending', 'sent', 'failed', 'all'], description: 'Filter by status' }),
    (0, swagger_1.ApiQuery)({ name: 'reminder_type', required: false, enum: ['sms', 'call', 'both', 'all'], description: 'Filter by reminder type' }),
    (0, swagger_1.ApiQuery)({ name: 'date_from', required: false, description: 'Filter from date (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'date_to', required: false, description: 'Filter to date (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'is_active', required: false, description: 'Filter by active status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Reminders retrieved successfully' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.ReminderQueryDto, Object]),
    __metadata("design:returntype", Promise)
], RemindersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('patient/:patientId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get reminders for a specific patient' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Patient reminders retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('patientId', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RemindersController.prototype, "findByPatient", null);
__decorate([
    (0, common_1.Get)('medicine/:medicineId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get reminders for a specific medicine' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Medicine reminders retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Medicine not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('medicineId', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RemindersController.prototype, "findByMedicine", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get reminder by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Reminder retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Reminder not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RemindersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update reminder' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Reminder updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Reminder not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateReminderDto, Object]),
    __metadata("design:returntype", Promise)
], RemindersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete reminder' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Reminder deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Reminder not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], RemindersController.prototype, "remove", null);
exports.RemindersController = RemindersController = __decorate([
    (0, swagger_1.ApiTags)('reminders'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('reminders'),
    __metadata("design:paramtypes", [reminders_service_1.RemindersService])
], RemindersController);
//# sourceMappingURL=reminders.controller.js.map