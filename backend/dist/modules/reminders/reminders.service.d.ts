import { SupabaseService } from '../../config/supabase.service';
import { User, Reminder } from '../../common/types';
import { CreateReminderDto, UpdateReminderDto, ReminderQueryDto, BulkReminderDto } from './dto';
export declare class RemindersService {
    private readonly supabaseService;
    private readonly logger;
    constructor(supabaseService: SupabaseService);
    create(createReminderDto: CreateReminderDto, currentUser: User): Promise<Reminder>;
    findAll(query: ReminderQueryDto, currentUser: User): Promise<Reminder[]>;
    findOne(id: string, currentUser: User): Promise<Reminder>;
    update(id: string, updateReminderDto: UpdateReminderDto, currentUser: User): Promise<Reminder>;
    remove(id: string, currentUser: User): Promise<void>;
    createBulkReminders(bulkReminderDto: BulkReminderDto, currentUser: User): Promise<Reminder[]>;
    processPendingReminders(): Promise<void>;
    private processReminder;
    private createNextRecurringReminder;
}
