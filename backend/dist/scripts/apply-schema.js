"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../src/app.module");
const supabase_service_1 = require("../src/config/supabase.service");
const fs = require("fs");
const path = require("path");
async function bootstrap() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const supabaseService = app.get(supabase_service_1.SupabaseService);
    const supabase = supabaseService.getAdminClient();
    console.log('📋 Applying database schema...');
    try {
        const schemaPath = path.join(__dirname, '../src/database/schema.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');
        const statements = schema
            .split(';')
            .map(stmt => stmt.trim())
            .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
        console.log(`Found ${statements.length} SQL statements to execute`);
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            if (statement.trim()) {
                try {
                    console.log(`Executing statement ${i + 1}/${statements.length}...`);
                    const { error } = await supabase.rpc('exec_sql', { sql: statement });
                    if (error) {
                        console.log(`⚠️  Statement ${i + 1} warning:`, error.message);
                    }
                    else {
                        console.log(`✅ Statement ${i + 1} executed successfully`);
                    }
                }
                catch (error) {
                    console.log(`❌ Error executing statement ${i + 1}:`, error.message);
                }
            }
        }
        console.log('✅ Schema application completed!');
    }
    catch (error) {
        console.error('❌ Error applying schema:', error);
    }
    finally {
        await app.close();
    }
}
bootstrap();
//# sourceMappingURL=apply-schema.js.map