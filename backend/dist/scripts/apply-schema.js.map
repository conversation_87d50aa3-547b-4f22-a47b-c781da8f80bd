{"version": 3, "file": "apply-schema.js", "sourceRoot": "", "sources": ["../../scripts/apply-schema.ts"], "names": [], "mappings": ";;AAAA,uCAA2C;AAC3C,kDAA8C;AAC9C,qEAAiE;AACjE,yBAAyB;AACzB,6BAA6B;AAE7B,KAAK,UAAU,SAAS;IACtB,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,wBAAwB,CAAC,sBAAS,CAAC,CAAC;IAElE,MAAM,eAAe,GAAG,GAAG,CAAC,GAAG,CAAC,kCAAe,CAAC,CAAC;IACjD,MAAM,QAAQ,GAAG,eAAe,CAAC,cAAc,EAAE,CAAC;IAElD,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;IAE9C,IAAI,CAAC;QAEH,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,4BAA4B,CAAC,CAAC;QACtE,MAAM,MAAM,GAAG,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAGnD,MAAM,UAAU,GAAG,MAAM;aACtB,KAAK,CAAC,GAAG,CAAC;aACV,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;aACxB,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;QAE7D,OAAO,CAAC,GAAG,CAAC,SAAS,UAAU,CAAC,MAAM,4BAA4B,CAAC,CAAC;QAGpE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC3C,MAAM,SAAS,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;YAChC,IAAI,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;gBACrB,IAAI,CAAC;oBACH,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,CAAC;oBACpE,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,CAAC,GAAG,CAAC,UAAU,EAAE,EAAE,GAAG,EAAE,SAAS,EAAE,CAAC,CAAC;oBAErE,IAAI,KAAK,EAAE,CAAC;wBACV,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;oBAChE,CAAC;yBAAM,CAAC;wBACN,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;oBAC5D,CAAC;gBACH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBACtE,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IAEjD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;IACnD,CAAC;YAAS,CAAC;QACT,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;AACH,CAAC;AAED,SAAS,EAAE,CAAC"}