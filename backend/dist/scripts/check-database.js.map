{"version": 3, "file": "check-database.js", "sourceRoot": "", "sources": ["../../scripts/check-database.ts"], "names": [], "mappings": ";;AAAA,uCAA2C;AAC3C,kDAA8C;AAC9C,qEAAiE;AAEjE,KAAK,UAAU,SAAS;IACtB,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,wBAAwB,CAAC,sBAAS,CAAC,CAAC;IAElE,MAAM,eAAe,GAAG,GAAG,CAAC,GAAG,CAAC,kCAAe,CAAC,CAAC;IACjD,MAAM,QAAQ,GAAG,eAAe,CAAC,cAAc,EAAE,CAAC;IAElD,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;IAEjD,IAAI,CAAC;QAEH,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE,GAAG,MAAM,QAAQ;aACxD,IAAI,CAAC,2BAA2B,CAAC;aACjC,MAAM,CAAC,YAAY,CAAC;aACpB,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;QAEhC,IAAI,WAAW,EAAE,CAAC;YAChB,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC;QAC/D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;YACnC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;QACnE,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;QAC5C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,MAAM,QAAQ;aACtD,IAAI,CAAC,OAAO,CAAC;aACb,MAAM,CAAC,GAAG,CAAC;aACX,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;QAC1D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QACrD,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QACpD,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,kBAAkB,EAAE,GAAG,MAAM,QAAQ;aACtE,IAAI,CAAC,eAAe,CAAC;aACrB,MAAM,CAAC,GAAG,CAAC;aACX,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,kBAAkB,EAAE,CAAC;YACvB,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAC;QAC1E,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;QAC7D,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QACnD,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ;aACpE,IAAI,CAAC,cAAc,CAAC;aACpB,MAAM,CAAC,GAAG,CAAC;aACX,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACxE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAC5D,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,MAAM,QAAQ;aAC9D,IAAI,CAAC,WAAW,CAAC;aACjB,MAAM,CAAC,GAAG,CAAC;aACX,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,cAAc,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,cAAc,CAAC,OAAO,CAAC,CAAC;QAClE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QACzD,CAAC;QAGD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,iBAAiB,EAAE,GAAG,MAAM,QAAQ;aACzE,IAAI,CAAC,oBAAoB,CAAC;aAC1B,MAAM,CAAC,GAAG,CAAC;aACX,KAAK,CAAC,CAAC,CAAC,CAAC;QAEZ,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAC9E,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAClE,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;IACrD,CAAC;YAAS,CAAC;QACT,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;AACH,CAAC;AAED,SAAS,EAAE,CAAC"}