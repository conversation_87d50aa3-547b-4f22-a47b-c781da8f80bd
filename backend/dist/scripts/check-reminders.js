"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const supabase_js_1 = require("@supabase/supabase-js");
const dotenv = require("dotenv");
dotenv.config();
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;
if (!supabaseUrl || !supabaseKey) {
    console.error('Missing Supabase configuration');
    process.exit(1);
}
const supabase = (0, supabase_js_1.createClient)(supabaseUrl, supabaseKey);
async function checkReminders() {
    try {
        console.log('🔍 Checking reminders data...\n');
        const { data: reminders, error } = await supabase
            .from('reminders')
            .select('*')
            .order('scheduled_time', { ascending: true });
        if (error) {
            console.error('Error fetching reminders:', error);
            return;
        }
        console.log(`📋 Total reminders: ${reminders?.length || 0}\n`);
        if (reminders && reminders.length > 0) {
            reminders.forEach((reminder, index) => {
                console.log(`${index + 1}. Reminder ID: ${reminder.id}`);
                console.log(`   Patient ID: ${reminder.patient_id}`);
                console.log(`   Medicine ID: ${reminder.medicine_id}`);
                console.log(`   Scheduled: ${new Date(reminder.scheduled_time).toLocaleString()}`);
                console.log(`   Status: ${reminder.status}`);
                console.log(`   Type: ${reminder.reminder_type}`);
                console.log(`   Active: ${reminder.is_active}`);
                console.log('');
            });
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            const todaysReminders = reminders.filter(r => {
                const scheduledTime = new Date(r.scheduled_time);
                return scheduledTime >= today && scheduledTime < tomorrow;
            });
            console.log(`📅 Today's reminders: ${todaysReminders.length}`);
            const now = new Date();
            const next24Hours = new Date(now.getTime() + 24 * 60 * 60 * 1000);
            const upcomingReminders = reminders.filter(r => {
                const scheduledTime = new Date(r.scheduled_time);
                return scheduledTime > now && scheduledTime <= next24Hours && r.status === 'pending';
            });
            console.log(`⏰ Upcoming reminders (next 24h): ${upcomingReminders.length}`);
            const overdueReminders = reminders.filter(r => {
                const scheduledTime = new Date(r.scheduled_time);
                return scheduledTime < now && r.status === 'pending';
            });
            console.log(`⚠️  Overdue reminders: ${overdueReminders.length}`);
        }
    }
    catch (error) {
        console.error('Error:', error);
    }
}
checkReminders();
//# sourceMappingURL=check-reminders.js.map