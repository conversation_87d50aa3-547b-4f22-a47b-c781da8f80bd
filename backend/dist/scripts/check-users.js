"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../src/app.module");
const supabase_service_1 = require("../src/config/supabase.service");
async function checkUsers() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const supabaseService = app.get(supabase_service_1.SupabaseService);
    const supabase = supabaseService.getAdminClient();
    console.log('🔍 Checking existing users...');
    try {
        const { data: users, error } = await supabase
            .from('users')
            .select('email, name, role')
            .order('email');
        if (error) {
            console.error('❌ Error fetching users:', error);
            return;
        }
        console.log(`\n📊 Found ${users.length} existing users:`);
        users.forEach(user => {
            console.log(`  ${user.role.padEnd(10)} | ${user.email.padEnd(30)} | ${user.name}`);
        });
    }
    catch (error) {
        console.error('❌ Error:', error);
    }
    finally {
        await app.close();
    }
}
checkUsers();
//# sourceMappingURL=check-users.js.map