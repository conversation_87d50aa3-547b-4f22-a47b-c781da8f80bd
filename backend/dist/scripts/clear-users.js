"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../src/app.module");
const supabase_service_1 = require("../src/config/supabase.service");
async function clearUsers() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const supabaseService = app.get(supabase_service_1.SupabaseService);
    const supabase = supabaseService.getAdminClient();
    console.log('🧹 Clearing existing users...');
    try {
        await supabase.from('adherence_records').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        await supabase.from('reminders').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        await supabase.from('medicines').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        await supabase.from('prescriptions').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        await supabase.from('gamification_stats').delete().neq('patient_id', '00000000-0000-0000-0000-000000000000');
        await supabase.from('patients').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        await supabase.from('doctors').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        await supabase.from('hospitals').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        await supabase.from('insurance_providers').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        const { data: users } = await supabase.from('users').select('id');
        if (users) {
            for (const user of users) {
                try {
                    await supabase.auth.admin.deleteUser(user.id);
                    console.log(`🗑️  Deleted auth user: ${user.id}`);
                }
                catch (error) {
                    console.log(`⚠️  Could not delete auth user ${user.id}:`, error);
                }
            }
        }
        await supabase.from('users').delete().neq('id', '00000000-0000-0000-0000-000000000000');
        console.log('✅ All users cleared successfully!');
    }
    catch (error) {
        console.error('❌ Error clearing users:', error);
    }
    finally {
        await app.close();
    }
}
clearUsers();
//# sourceMappingURL=clear-users.js.map