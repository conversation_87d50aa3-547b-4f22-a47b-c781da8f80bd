{"version": 3, "file": "debug-reminders.js", "sourceRoot": "", "sources": ["../../scripts/debug-reminders.ts"], "names": [], "mappings": ";;AAAA,uDAAqD;AACrD,iCAAiC;AAGjC,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC;AAC7C,MAAM,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,yBAAyB,CAAC;AAEjE,IAAI,CAAC,WAAW,IAAI,CAAC,kBAAkB,EAAE,CAAC;IACxC,OAAO,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC;IAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC;AAED,MAAM,QAAQ,GAAG,IAAA,0BAAY,EAAC,WAAW,EAAE,kBAAkB,CAAC,CAAC;AAE/D,KAAK,UAAU,cAAc;IAC3B,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;QAErD,MAAM,aAAa,GAAG,sCAAsC,CAAC;QAC7D,OAAO,CAAC,GAAG,CAAC,oBAAoB,aAAa,IAAI,CAAC,CAAC;QAGnD,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,GAAG,MAAM,QAAQ;aAC9D,IAAI,CAAC,WAAW,CAAC;aACjB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC,CAAC;QAEnC,IAAI,cAAc,EAAE,CAAC;YACnB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,cAAc,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,6BAA6B,SAAS,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;YACnE,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtC,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;oBACpC,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;oBAChD,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;oBACrD,OAAO,CAAC,GAAG,CAAC,mBAAmB,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;oBACvD,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;oBACnF,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC7C,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,SAAS,EAAE,CAAC,CAAC;oBAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;QAEjE,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACrD,OAAO,CAAC,GAAG,CAAC,sBAAsB,UAAU,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAEnE,MAAM,EAAE,IAAI,EAAE,iBAAiB,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG,MAAM,QAAQ;aACrE,IAAI,CAAC,WAAW,CAAC;aACjB,MAAM,CAAC,GAAG,CAAC;aACX,EAAE,CAAC,YAAY,EAAE,aAAa,CAAC;aAC/B,GAAG,CAAC,gBAAgB,EAAE,GAAG,CAAC,WAAW,EAAE,CAAC;aACxC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC;aAC/C,EAAE,CAAC,QAAQ,EAAE,SAAS,CAAC;aACvB,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;QAEzB,IAAI,aAAa,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,oCAAoC,EAAE,aAAa,CAAC,CAAC;QACrE,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,GAAG,CAAC,+BAA+B,iBAAiB,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,CAAC;YAC7E,IAAI,iBAAiB,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACtD,iBAAiB,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE;oBAC5C,OAAO,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,QAAQ,CAAC,EAAE,EAAE,CAAC,CAAC;oBAChD,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;oBACnF,OAAO,CAAC,GAAG,CAAC,cAAc,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;oBAC7C,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;gBAClB,CAAC,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IACjC,CAAC;AACH,CAAC;AAED,cAAc,EAAE,CAAC"}