"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../src/app.module");
const supabase_service_1 = require("../src/config/supabase.service");
const patient_personas_1 = require("./seed-data/patient-personas");
const medical_data_1 = require("./seed-data/medical-data");
const time_series_generator_1 = require("./seed-data/time-series-generator");
async function bootstrap() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const supabaseService = app.get(supabase_service_1.SupabaseService);
    const supabase = supabaseService.getAdminClient();
    console.log('🌱 Starting enhanced database seeding...');
    try {
        const createdUsers = new Map();
        const createdPrescriptions = new Map();
        const createdMedicines = new Map();
        console.log('👥 Seeding enhanced user profiles...');
        for (const persona of patient_personas_1.patientPersonas) {
            try {
                const { data: existingUser } = await supabase
                    .from('users')
                    .select('*')
                    .eq('email', persona.email)
                    .single();
                if (existingUser) {
                    console.log(`⚠️  Patient ${persona.name} already exists, skipping...`);
                    createdUsers.set(persona.email, existingUser);
                    continue;
                }
                const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
                    email: persona.email,
                    password: 'password123',
                    email_confirm: true,
                    user_metadata: {
                        name: persona.name,
                        role: 'patient',
                    },
                });
                if (authError) {
                    console.log(`❌ Error creating auth user ${persona.name}:`, authError.message);
                    console.log(`   Full error:`, JSON.stringify(authError, null, 2));
                    continue;
                }
                const { data: user, error: userError } = await supabase
                    .from('users')
                    .insert({
                    id: authUser.user.id,
                    email: persona.email,
                    name: persona.name,
                    role: 'patient',
                    avatar: persona.avatar,
                })
                    .select()
                    .single();
                if (userError) {
                    console.log(`❌ Error creating user record for ${persona.name}:`, userError.message);
                    continue;
                }
                const { error: patientError } = await supabase
                    .from('patients')
                    .insert({
                    id: user.id,
                    date_of_birth: persona.dateOfBirth,
                    emergency_contact: persona.emergencyContact,
                });
                if (patientError) {
                    console.log(`❌ Error creating patient profile for ${persona.name}:`, patientError.message);
                    continue;
                }
                const { error: gamificationError } = await supabase
                    .from('gamification_stats')
                    .insert({
                    patient_id: user.id,
                    current_streak: 0,
                    longest_streak: 0,
                    total_points: 0,
                    level: 1,
                    total_medicines_taken: 0,
                    adherence_rate: 0.00,
                });
                if (gamificationError) {
                    console.log(`❌ Error creating gamification stats for ${persona.name}:`, gamificationError.message);
                }
                createdUsers.set(persona.email, user);
                console.log(`✅ Created patient: ${persona.name}`);
            }
            catch (error) {
                console.log(`❌ Error processing patient ${persona.name}:`, error.message);
            }
        }
        for (const doctor of patient_personas_1.doctorProfiles) {
            try {
                const { data: existingUser } = await supabase
                    .from('users')
                    .select('*')
                    .eq('email', doctor.email)
                    .single();
                if (existingUser) {
                    console.log(`⚠️  Doctor ${doctor.name} already exists, skipping...`);
                    createdUsers.set(doctor.email, existingUser);
                    continue;
                }
                const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
                    email: doctor.email,
                    password: 'password123',
                    email_confirm: true,
                    user_metadata: {
                        name: doctor.name,
                        role: 'doctor',
                    },
                });
                if (authError) {
                    console.log(`❌ Error creating auth user ${doctor.name}:`, authError.message);
                    continue;
                }
                const { data: user, error: userError } = await supabase
                    .from('users')
                    .insert({
                    id: authUser.user.id,
                    email: doctor.email,
                    name: doctor.name,
                    role: 'doctor',
                    avatar: doctor.avatar,
                })
                    .select()
                    .single();
                if (userError) {
                    console.log(`❌ Error creating user record for ${doctor.name}:`, userError.message);
                    continue;
                }
                const { error: doctorError } = await supabase
                    .from('doctors')
                    .insert({
                    id: user.id,
                    specialization: doctor.specialization,
                    license_number: doctor.licenseNumber,
                });
                if (doctorError) {
                    console.log(`❌ Error creating doctor profile for ${doctor.name}:`, doctorError.message);
                    continue;
                }
                createdUsers.set(doctor.email, user);
                console.log(`✅ Created doctor: ${doctor.name} (${doctor.specialization})`);
            }
            catch (error) {
                console.log(`❌ Error processing doctor ${doctor.name}:`, error.message);
            }
        }
        for (const hospital of patient_personas_1.hospitalProfiles) {
            try {
                const { data: existingUser } = await supabase
                    .from('users')
                    .select('*')
                    .eq('email', hospital.email)
                    .single();
                if (existingUser) {
                    console.log(`⚠️  Hospital ${hospital.name} already exists, skipping...`);
                    createdUsers.set(hospital.email, existingUser);
                    continue;
                }
                const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
                    email: hospital.email,
                    password: 'password123',
                    email_confirm: true,
                    user_metadata: {
                        name: hospital.name,
                        role: 'hospital',
                    },
                });
                if (authError) {
                    console.log(`❌ Error creating auth user ${hospital.name}:`, authError.message);
                    continue;
                }
                const { data: user, error: userError } = await supabase
                    .from('users')
                    .insert({
                    id: authUser.user.id,
                    email: hospital.email,
                    name: hospital.name,
                    role: 'hospital',
                    avatar: hospital.avatar,
                })
                    .select()
                    .single();
                if (userError) {
                    console.log(`❌ Error creating user record for ${hospital.name}:`, userError.message);
                    continue;
                }
                const { error: hospitalError } = await supabase
                    .from('hospitals')
                    .insert({
                    id: user.id,
                    address: hospital.address,
                    phone: hospital.phone,
                    license_number: `HOSP-${Date.now()}`,
                });
                if (hospitalError) {
                    console.log(`❌ Error creating hospital profile for ${hospital.name}:`, hospitalError.message);
                    continue;
                }
                createdUsers.set(hospital.email, user);
                console.log(`✅ Created hospital: ${hospital.name}`);
            }
            catch (error) {
                console.log(`❌ Error processing hospital ${hospital.name}:`, error.message);
            }
        }
        for (const insurance of patient_personas_1.insuranceProfiles) {
            try {
                const { data: existingUser } = await supabase
                    .from('users')
                    .select('*')
                    .eq('email', insurance.email)
                    .single();
                if (existingUser) {
                    console.log(`⚠️  Insurance ${insurance.name} already exists, skipping...`);
                    createdUsers.set(insurance.email, existingUser);
                    continue;
                }
                const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
                    email: insurance.email,
                    password: 'password123',
                    email_confirm: true,
                    user_metadata: {
                        name: insurance.name,
                        role: 'insurance',
                    },
                });
                if (authError) {
                    console.log(`❌ Error creating auth user ${insurance.name}:`, authError.message);
                    continue;
                }
                const { data: user, error: userError } = await supabase
                    .from('users')
                    .insert({
                    id: authUser.user.id,
                    email: insurance.email,
                    name: insurance.name,
                    role: 'insurance',
                    avatar: insurance.avatar,
                })
                    .select()
                    .single();
                if (userError) {
                    console.log(`❌ Error creating user record for ${insurance.name}:`, userError.message);
                    continue;
                }
                const { error: insuranceError } = await supabase
                    .from('insurance_providers')
                    .insert({
                    id: user.id,
                    company_name: insurance.companyName,
                    address: insurance.address,
                    phone: insurance.phone,
                    policy_types: insurance.policyTypes,
                    coverage_details: {
                        coverage_areas: insurance.coverageAreas,
                        website: insurance.website,
                    },
                });
                if (insuranceError) {
                    console.log(`❌ Error creating insurance profile for ${insurance.name}:`, insuranceError.message);
                    continue;
                }
                createdUsers.set(insurance.email, user);
                console.log(`✅ Created insurance provider: ${insurance.name}`);
            }
            catch (error) {
                console.log(`❌ Error processing insurance ${insurance.name}:`, error.message);
            }
        }
        console.log('\n📋 Creating realistic prescriptions and medications...');
        const patients = Array.from(createdUsers.values()).filter(user => user.role === 'patient');
        const doctors = Array.from(createdUsers.values()).filter(user => user.role === 'doctor');
        for (const patient of patients) {
            try {
                const persona = patient_personas_1.patientPersonas.find(p => p.email === patient.email);
                if (!persona)
                    continue;
                const patientMedications = (0, medical_data_1.getRealisticMedicationRegimen)(persona.conditions);
                const assignedDoctor = doctors[Math.floor(Math.random() * doctors.length)];
                const prescriptionId = `prescription-${patient.name.toLowerCase().replace(/\s+/g, '-')}-${Date.now()}`;
                const { data: prescription, error: prescriptionError } = await supabase
                    .from('prescriptions')
                    .insert({
                    patient_id: patient.id,
                    doctor_id: assignedDoctor.id,
                    prescription_date: new Date().toISOString().split('T')[0],
                    file_url: `/uploads/prescriptions/${prescriptionId}.pdf`,
                    status: 'completed',
                    notes: `Prescription for ${patient.name} - ${persona.conditions.join(', ')}`,
                    medicine_info: {
                        conditions: persona.conditions,
                        medications: patientMedications.map(m => m.name),
                    },
                })
                    .select()
                    .single();
                if (prescriptionError) {
                    console.log(`❌ Error creating prescription for ${patient.name}:`, prescriptionError.message);
                    continue;
                }
                createdPrescriptions.set(prescriptionId, prescription);
                let allPatientAdherenceRecords = [];
                for (const medication of patientMedications) {
                    const { data: medicine, error: medicineError } = await supabase
                        .from('medicines')
                        .insert({
                        patient_id: patient.id,
                        prescription_id: prescription.id,
                        name: medication.name,
                        dosage: medication.dosage,
                        frequency: medication.frequency,
                        duration: medication.duration,
                        instructions: medication.instructions,
                        side_effects: medication.sideEffects,
                        is_active: true,
                    })
                        .select()
                        .single();
                    if (medicineError) {
                        console.log(`❌ Error creating medicine ${medication.name} for ${patient.name}:`, medicineError.message);
                        continue;
                    }
                    createdMedicines.set(`${patient.id}-${medication.name}`, medicine);
                    const endDate = new Date();
                    const startDate = new Date();
                    startDate.setDate(startDate.getDate() - 90);
                    const adherenceRecords = time_series_generator_1.TimeSeriesGenerator.generateAdherenceHistory(patient.id, medicine.id, startDate, endDate, medication.frequency, persona.adherenceRate, persona.adherenceRate > 85 ? 'consistent' :
                        persona.adherenceRate < 65 ? 'erratic' : 'declining');
                    allPatientAdherenceRecords.push(...adherenceRecords);
                    const batchSize = 50;
                    for (let i = 0; i < adherenceRecords.length; i += batchSize) {
                        const batch = adherenceRecords.slice(i, i + batchSize);
                        const { error: adherenceError } = await supabase
                            .from('adherence_records')
                            .insert(batch.map(record => ({
                            medicine_id: record.medicineId,
                            patient_id: record.patientId,
                            scheduled_time: record.scheduledTime.toISOString(),
                            taken_time: record.actualTime?.toISOString() || null,
                            status: record.status,
                            notes: record.notes || null,
                        })));
                        if (adherenceError) {
                            console.log(`❌ Error inserting adherence batch for ${medication.name}:`, adherenceError.message);
                        }
                    }
                    const reminderTimes = time_series_generator_1.TimeSeriesGenerator.parseMedicationFrequency(medication.frequency);
                    for (const reminderTime of reminderTimes) {
                        const { error: reminderError } = await supabase
                            .from('reminders')
                            .insert({
                            patient_id: patient.id,
                            medicine_id: medicine.id,
                            scheduled_time: new Date().toISOString().split('T')[0] + 'T' + `${reminderTime.hour.toString().padStart(2, '0')}:${reminderTime.minute.toString().padStart(2, '0')}:00Z`,
                            reminder_type: persona.preferredReminderType,
                            message: `Time to take your ${medication.name}`,
                            is_active: true,
                        });
                        if (reminderError) {
                            console.log(`❌ Error creating reminder for ${medication.name}:`, reminderError.message);
                        }
                    }
                }
                const gamificationStats = time_series_generator_1.TimeSeriesGenerator.calculateGamificationStats(allPatientAdherenceRecords);
                const weeklyProgress = time_series_generator_1.TimeSeriesGenerator.generateWeeklyProgress(allPatientAdherenceRecords);
                const monthlyProgress = time_series_generator_1.TimeSeriesGenerator.generateMonthlyProgress(allPatientAdherenceRecords);
                const { error: gamificationUpdateError } = await supabase
                    .from('gamification_stats')
                    .update({
                    current_streak: gamificationStats.currentStreak,
                    longest_streak: gamificationStats.longestStreak,
                    total_points: gamificationStats.totalPoints,
                    level: gamificationStats.level,
                    total_medicines_taken: gamificationStats.totalMedicinesTaken,
                    adherence_rate: gamificationStats.adherenceRate,
                    last_activity_date: new Date().toISOString().split('T')[0],
                })
                    .eq('patient_id', patient.id);
                if (gamificationUpdateError) {
                    console.log(`❌ Error updating gamification stats for ${patient.name}:`, gamificationUpdateError.message);
                }
                console.log(`✅ Created complete medical profile for ${patient.name} (${patientMedications.length} medications)`);
            }
            catch (error) {
                console.log(`❌ Error processing medical data for patient:`, error.message);
            }
        }
        console.log(`\n📊 Enhanced seeding completed!`);
        console.log(`👥 Total users created: ${createdUsers.size}`);
        console.log(`🏥 Patients: ${patient_personas_1.patientPersonas.length}`);
        console.log(`👨‍⚕️ Doctors: ${patient_personas_1.doctorProfiles.length}`);
        console.log(`🏥 Hospitals: ${patient_personas_1.hospitalProfiles.length}`);
        console.log(`🏢 Insurance Providers: ${patient_personas_1.insuranceProfiles.length}`);
        console.log(`📋 Prescriptions: ${createdPrescriptions.size}`);
        console.log(`💊 Medicines: ${createdMedicines.size}`);
        console.log(`📊 Adherence records: ~${createdMedicines.size * 90 * 2} (90 days average)`);
    }
    catch (error) {
        console.error('❌ Error during enhanced seeding:', error);
    }
    finally {
        await app.close();
    }
}
bootstrap();
//# sourceMappingURL=enhanced-seed-database.js.map