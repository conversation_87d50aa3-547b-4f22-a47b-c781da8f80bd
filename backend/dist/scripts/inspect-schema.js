"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../src/app.module");
const supabase_service_1 = require("../src/config/supabase.service");
async function bootstrap() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const supabaseService = app.get(supabase_service_1.SupabaseService);
    const supabase = supabaseService.getAdminClient();
    console.log('🔍 Inspecting actual database schema...');
    try {
        console.log('\n🏆 Achievements table columns:');
        const { data: achievements, error: achievementsError } = await supabase
            .from('achievements')
            .select('*')
            .limit(0);
        if (achievementsError) {
            console.log('❌ Error:', achievementsError.message);
        }
        else {
            console.log('✅ Achievements table accessible');
        }
        console.log('\n📋 Prescriptions table columns:');
        const { data: prescriptions, error: prescriptionsError } = await supabase
            .from('prescriptions')
            .select('*')
            .limit(0);
        if (prescriptionsError) {
            console.log('❌ Error:', prescriptionsError.message);
        }
        else {
            console.log('✅ Prescriptions table accessible');
        }
        console.log('\n🎮 Gamification stats table columns:');
        const { data: gamificationStats, error: gamificationError } = await supabase
            .from('gamification_stats')
            .select('*')
            .limit(0);
        if (gamificationError) {
            console.log('❌ Error:', gamificationError.message);
        }
        else {
            console.log('✅ Gamification stats table accessible');
        }
        console.log('\n📊 Sample data structures:');
        const { data: sampleUsers } = await supabase
            .from('users')
            .select('*')
            .limit(1);
        if (sampleUsers && sampleUsers.length > 0) {
            console.log('👤 User columns:', Object.keys(sampleUsers[0]));
        }
        console.log('\n🧪 Testing minimal inserts...');
        const { error: minAchievementError } = await supabase
            .from('achievements')
            .insert({
            name: 'Test',
            description: 'Test',
            icon: 'test',
            category: 'milestone',
            points: 10,
        });
        if (minAchievementError) {
            console.log('❌ Minimal achievement error:', minAchievementError.message);
        }
        else {
            console.log('✅ Minimal achievement insert successful');
            await supabase.from('achievements').delete().eq('name', 'Test');
        }
        const { data: patients } = await supabase
            .from('users')
            .select('id')
            .eq('role', 'patient')
            .limit(1);
        if (patients && patients.length > 0) {
            const { error: minPrescriptionError } = await supabase
                .from('prescriptions')
                .insert({
                patient_id: patients[0].id,
                filename: 'test.pdf',
                file_url: '/test',
            });
            if (minPrescriptionError) {
                console.log('❌ Minimal prescription error:', minPrescriptionError.message);
            }
            else {
                console.log('✅ Minimal prescription insert successful');
                await supabase.from('prescriptions').delete().eq('filename', 'test.pdf');
            }
        }
    }
    catch (error) {
        console.error('❌ Error inspecting schema:', error);
    }
    finally {
        await app.close();
    }
}
bootstrap();
//# sourceMappingURL=inspect-schema.js.map