export interface MedicationInfo {
    name: string;
    genericName?: string;
    dosage: string;
    frequency: string;
    duration: string;
    instructions: string;
    sideEffects: string;
    conditions: string[];
    category: string;
    requiresMonitoring: boolean;
    costTier: 1 | 2 | 3 | 4;
}
export declare const medications: MedicationInfo[];
export declare const medicalConditions: string[];
export declare const specializations: string[];
export declare function getMedicationsForConditions(conditions: string[]): MedicationInfo[];
export declare function getRealisticMedicationRegimen(conditions: string[]): MedicationInfo[];
