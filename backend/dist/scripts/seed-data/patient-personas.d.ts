export interface PatientPersona {
    email: string;
    name: string;
    dateOfBirth: string;
    emergencyContact: string;
    avatar: string;
    conditions: string[];
    adherenceRate: number;
    engagementLevel: 'low' | 'medium' | 'high';
    techSavvy: boolean;
    financialStatus: 'struggling' | 'stable' | 'comfortable';
    supportSystem: 'none' | 'family' | 'caregiver' | 'strong';
    preferredReminderType: 'sms' | 'call' | 'both';
    medicationComplexity: 'simple' | 'moderate' | 'complex';
    insuranceType: 'basic' | 'standard' | 'premium';
}
export declare const patientPersonas: PatientPersona[];
export declare const doctorProfiles: {
    email: string;
    name: string;
    specialization: string;
    licenseNumber: string;
    avatar: string;
}[];
export declare const hospitalProfiles: {
    email: string;
    name: string;
    address: string;
    phone: string;
    website: string;
    avatar: string;
}[];
export declare const insuranceProfiles: {
    email: string;
    name: string;
    companyName: string;
    address: string;
    phone: string;
    website: string;
    policyTypes: string[];
    coverageAreas: string[];
    avatar: string;
}[];
