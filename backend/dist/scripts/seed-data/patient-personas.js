"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.insuranceProfiles = exports.hospitalProfiles = exports.doctorProfiles = exports.patientPersonas = void 0;
exports.patientPersonas = [
    {
        email: '<EMAIL>',
        name: '<PERSON>',
        dateOfBirth: '1965-03-15',
        emergencyContact: '******-0201',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=carol',
        conditions: ['Type 2 Diabetes', 'Hypertension'],
        adherenceRate: 96,
        engagementLevel: 'high',
        techSavvy: true,
        financialStatus: 'comfortable',
        supportSystem: 'strong',
        preferredReminderType: 'sms',
        medicationComplexity: 'moderate',
        insuranceType: 'premium',
    },
    {
        email: '<EMAIL>',
        name: '<PERSON>',
        dateOfBirth: '1978-11-22',
        emergencyContact: '******-0202',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=samuel',
        conditions: ['Type 2 Diabetes', 'Depression', 'Hypertension', 'High Cholesterol'],
        adherenceRate: 62,
        engagementLevel: 'low',
        techSavvy: false,
        financialStatus: 'struggling',
        supportSystem: 'none',
        preferredReminderType: 'call',
        medicationComplexity: 'complex',
        insuranceType: 'basic',
    },
    {
        email: '<EMAIL>',
        name: 'Nancy Chen',
        dateOfBirth: '1985-07-08',
        emergencyContact: '******-0203',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=nancy',
        conditions: ['Type 2 Diabetes'],
        adherenceRate: 78,
        engagementLevel: 'medium',
        techSavvy: true,
        financialStatus: 'stable',
        supportSystem: 'family',
        preferredReminderType: 'both',
        medicationComplexity: 'simple',
        insuranceType: 'standard',
    },
    {
        email: '<EMAIL>',
        name: 'Charles Williams',
        dateOfBirth: '1952-12-03',
        emergencyContact: '******-0204',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=charles',
        conditions: ['Heart Disease', 'Type 2 Diabetes', 'Hypertension', 'High Cholesterol', 'Atrial Fibrillation'],
        adherenceRate: 84,
        engagementLevel: 'medium',
        techSavvy: false,
        financialStatus: 'comfortable',
        supportSystem: 'caregiver',
        preferredReminderType: 'call',
        medicationComplexity: 'complex',
        insuranceType: 'premium',
    },
    {
        email: '<EMAIL>',
        name: 'Sally Thompson',
        dateOfBirth: '1940-05-20',
        emergencyContact: '******-0205',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sally',
        conditions: ['Hypertension', 'Osteoporosis'],
        adherenceRate: 91,
        engagementLevel: 'low',
        techSavvy: false,
        financialStatus: 'stable',
        supportSystem: 'family',
        preferredReminderType: 'call',
        medicationComplexity: 'simple',
        insuranceType: 'standard',
    },
    {
        email: '<EMAIL>',
        name: 'Paul Anderson',
        dateOfBirth: '1990-09-14',
        emergencyContact: '******-0206',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=paul',
        conditions: ['Anxiety', 'High Cholesterol'],
        adherenceRate: 73,
        engagementLevel: 'high',
        techSavvy: true,
        financialStatus: 'comfortable',
        supportSystem: 'none',
        preferredReminderType: 'sms',
        medicationComplexity: 'simple',
        insuranceType: 'premium',
    },
    {
        email: '<EMAIL>',
        name: 'Anna Martinez',
        dateOfBirth: '1988-02-28',
        emergencyContact: '******-0207',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=anna',
        conditions: ['Asthma'],
        adherenceRate: 89,
        engagementLevel: 'high',
        techSavvy: true,
        financialStatus: 'stable',
        supportSystem: 'strong',
        preferredReminderType: 'sms',
        medicationComplexity: 'simple',
        insuranceType: 'standard',
    },
    {
        email: '<EMAIL>',
        name: 'Frank Davis',
        dateOfBirth: '1970-06-12',
        emergencyContact: '******-0208',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=frank',
        conditions: ['ADHD', 'Hypertension'],
        adherenceRate: 55,
        engagementLevel: 'medium',
        techSavvy: true,
        financialStatus: 'stable',
        supportSystem: 'family',
        preferredReminderType: 'both',
        medicationComplexity: 'moderate',
        insuranceType: 'standard',
    },
    {
        email: '<EMAIL>',
        name: 'Maria Garcia',
        dateOfBirth: '1975-10-05',
        emergencyContact: '******-0209',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=maria',
        conditions: ['Type 1 Diabetes'],
        adherenceRate: 94,
        engagementLevel: 'high',
        techSavvy: true,
        financialStatus: 'stable',
        supportSystem: 'strong',
        preferredReminderType: 'sms',
        medicationComplexity: 'complex',
        insuranceType: 'premium',
    },
    {
        email: '<EMAIL>',
        name: 'Edward Wilson',
        dateOfBirth: '1935-01-18',
        emergencyContact: '******-0210',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=edward',
        conditions: ['Heart Disease', 'Hypertension', 'High Cholesterol', 'Chronic Pain'],
        adherenceRate: 87,
        engagementLevel: 'low',
        techSavvy: false,
        financialStatus: 'comfortable',
        supportSystem: 'caregiver',
        preferredReminderType: 'call',
        medicationComplexity: 'complex',
        insuranceType: 'premium',
    }
];
exports.doctorProfiles = [
    {
        email: '<EMAIL>',
        name: 'Dr. Sarah Mitchell',
        specialization: 'Endocrinology',
        licenseNumber: 'MD-ENDO-001',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=drsarah',
    },
    {
        email: '<EMAIL>',
        name: 'Dr. James Patterson',
        specialization: 'Cardiology',
        licenseNumber: 'MD-CARD-002',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=drjames',
    },
    {
        email: '<EMAIL>',
        name: 'Dr. Lisa Chang',
        specialization: 'Psychiatry',
        licenseNumber: 'MD-PSYC-003',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=drlisa',
    },
    {
        email: '<EMAIL>',
        name: 'Dr. Michael Brown',
        specialization: 'General Medicine',
        licenseNumber: 'MD-GEN-004',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=drmichael',
    },
    {
        email: '<EMAIL>',
        name: 'Dr. Jennifer Lee',
        specialization: 'Pulmonology',
        licenseNumber: 'MD-PULM-005',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=drjennifer',
    }
];
exports.hospitalProfiles = [
    {
        email: '<EMAIL>',
        name: 'Metro General Hospital',
        address: '123 Healthcare Blvd, Metro City, MC 12345',
        phone: '******-METRO-1',
        website: 'https://metrogeneral.com',
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=MGH',
    },
    {
        email: '<EMAIL>',
        name: 'Riverside Medical Center',
        address: '456 River Road, Riverside, RS 67890',
        phone: '******-RIVER-2',
        website: 'https://riversidemedical.com',
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=RMC',
    },
    {
        email: '<EMAIL>',
        name: 'University Hospital',
        address: '789 University Ave, College Town, CT 54321',
        phone: '******-UNI-HOSP',
        website: 'https://universityhospital.edu',
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=UH',
    }
];
exports.insuranceProfiles = [
    {
        email: '<EMAIL>',
        name: 'HealthFirst Insurance',
        companyName: 'HealthFirst Insurance Corporation',
        address: '100 Insurance Plaza, Coverage City, CC 11111',
        phone: '******-HEALTH-1',
        website: 'https://healthfirst.com',
        policyTypes: ['Health', 'Prescription', 'Dental', 'Vision'],
        coverageAreas: ['California', 'Nevada', 'Arizona', 'Utah'],
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=HFI',
    },
    {
        email: '<EMAIL>',
        name: 'CarePlus Insurance',
        companyName: 'CarePlus Health Solutions',
        address: '200 Wellness Way, Health Valley, HV 22222',
        phone: '******-CARE-PLUS',
        website: 'https://careplus.com',
        policyTypes: ['Health', 'Prescription', 'Mental Health', 'Specialty'],
        coverageAreas: ['New York', 'New Jersey', 'Connecticut', 'Pennsylvania'],
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=CPI',
    }
];
//# sourceMappingURL=patient-personas.js.map