export interface AdherencePattern {
    patientId: string;
    medicineId: string;
    adherenceRate: number;
    consistencyPattern: 'consistent' | 'declining' | 'improving' | 'erratic';
    weekendEffect: boolean;
    timeOfDayPreference: 'morning' | 'evening' | 'flexible';
}
export interface GeneratedAdherenceRecord {
    medicineId: string;
    patientId: string;
    scheduledTime: Date;
    actualTime: Date | null;
    status: 'taken' | 'missed' | 'skipped';
    notes?: string;
}
export declare class TimeSeriesGenerator {
    static generateAdherenceHistory(patientId: string, medicineId: string, startDate: Date, endDate: Date, frequency: string, adherenceRate: number, pattern?: AdherencePattern['consistencyPattern']): GeneratedAdherenceRecord[];
    static parseMedicationFrequency(frequency: string): Array<{
        hour: number;
        minute: number;
    }>;
    private static calculateAdherenceProbability;
    private static getTimeVariance;
    private static isHoliday;
    static calculateGamificationStats(adherenceRecords: GeneratedAdherenceRecord[]): {
        currentStreak: number;
        longestStreak: number;
        totalPoints: number;
        level: number;
        adherenceRate: number;
        totalMedicinesTaken: number;
    };
    static generateWeeklyProgress(adherenceRecords: GeneratedAdherenceRecord[]): number[];
    static generateMonthlyProgress(adherenceRecords: GeneratedAdherenceRecord[]): number[];
}
