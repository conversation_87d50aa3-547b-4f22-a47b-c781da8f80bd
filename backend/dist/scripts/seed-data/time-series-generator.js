"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeSeriesGenerator = void 0;
class TimeSeriesGenerator {
    static generateAdherenceHistory(patientId, medicineId, startDate, endDate, frequency, adherenceRate, pattern = 'consistent') {
        const records = [];
        const currentDate = new Date(startDate);
        const dailyDoses = this.parseMedicationFrequency(frequency);
        while (currentDate <= endDate) {
            dailyDoses.forEach((doseTime, index) => {
                const scheduledDateTime = new Date(currentDate);
                scheduledDateTime.setHours(doseTime.hour, doseTime.minute, 0, 0);
                const adherenceProb = this.calculateAdherenceProbability(adherenceRate, pattern, currentDate, index);
                const record = {
                    medicineId,
                    patientId,
                    scheduledTime: new Date(scheduledDateTime),
                    actualTime: null,
                    status: 'missed',
                };
                if (Math.random() < adherenceProb / 100) {
                    record.status = 'taken';
                    const variance = this.getTimeVariance(doseTime.hour);
                    const actualTime = new Date(scheduledDateTime);
                    actualTime.setMinutes(actualTime.getMinutes() + variance);
                    record.actualTime = actualTime;
                    if (Math.abs(variance) > 60) {
                        record.notes = variance > 0 ? 'Taken late' : 'Taken early';
                    }
                }
                else {
                    if (Math.random() < 0.1) {
                        record.status = 'skipped';
                        record.notes = 'Intentionally skipped';
                    }
                }
                records.push(record);
            });
            currentDate.setDate(currentDate.getDate() + 1);
        }
        return records;
    }
    static parseMedicationFrequency(frequency) {
        const freq = frequency.toLowerCase();
        if (freq.includes('once daily') || freq.includes('once a day')) {
            return [{ hour: 8, minute: 0 }];
        }
        else if (freq.includes('twice daily') || freq.includes('twice a day')) {
            return [{ hour: 8, minute: 0 }, { hour: 20, minute: 0 }];
        }
        else if (freq.includes('three times daily') || freq.includes('three times a day')) {
            return [{ hour: 8, minute: 0 }, { hour: 14, minute: 0 }, { hour: 20, minute: 0 }];
        }
        else if (freq.includes('four times daily') || freq.includes('four times a day')) {
            return [
                { hour: 8, minute: 0 },
                { hour: 12, minute: 0 },
                { hour: 16, minute: 0 },
                { hour: 20, minute: 0 }
            ];
        }
        else if (freq.includes('bedtime') || freq.includes('at night')) {
            return [{ hour: 22, minute: 0 }];
        }
        else if (freq.includes('morning')) {
            return [{ hour: 8, minute: 0 }];
        }
        else if (freq.includes('evening')) {
            return [{ hour: 20, minute: 0 }];
        }
        else if (freq.includes('every 4 hours')) {
            return [
                { hour: 8, minute: 0 },
                { hour: 12, minute: 0 },
                { hour: 16, minute: 0 },
                { hour: 20, minute: 0 }
            ];
        }
        else if (freq.includes('every 6 hours')) {
            return [
                { hour: 8, minute: 0 },
                { hour: 14, minute: 0 },
                { hour: 20, minute: 0 }
            ];
        }
        else if (freq.includes('every 8 hours')) {
            return [
                { hour: 8, minute: 0 },
                { hour: 16, minute: 0 }
            ];
        }
        else if (freq.includes('every 12 hours')) {
            return [{ hour: 8, minute: 0 }, { hour: 20, minute: 0 }];
        }
        else if (freq.includes('weekly')) {
            return [{ hour: 8, minute: 0 }];
        }
        else if (freq.includes('as needed')) {
            const numDoses = Math.floor(Math.random() * 4);
            const doses = [];
            for (let i = 0; i < numDoses; i++) {
                doses.push({
                    hour: 8 + (i * 4) + Math.floor(Math.random() * 3),
                    minute: Math.floor(Math.random() * 60)
                });
            }
            return doses;
        }
        return [{ hour: 8, minute: 0 }];
    }
    static calculateAdherenceProbability(baseRate, pattern, date, doseIndex) {
        let probability = baseRate;
        const daysSinceStart = Math.floor((date.getTime() - new Date('2024-01-01').getTime()) / (1000 * 60 * 60 * 24));
        switch (pattern) {
            case 'declining':
                probability = baseRate * (1 - (daysSinceStart * 0.001));
                break;
            case 'improving':
                probability = Math.min(95, baseRate + (daysSinceStart * 0.002));
                break;
            case 'erratic':
                probability = baseRate + (Math.random() - 0.5) * 40;
                break;
            case 'consistent':
            default:
                probability = baseRate + (Math.random() - 0.5) * 10;
                break;
        }
        const dayOfWeek = date.getDay();
        if (dayOfWeek === 0 || dayOfWeek === 6) {
            probability *= 0.85;
        }
        if (doseIndex > 0) {
            probability *= 0.95;
        }
        if (this.isHoliday(date)) {
            probability *= 0.8;
        }
        return Math.max(0, Math.min(100, probability));
    }
    static getTimeVariance(scheduledHour) {
        const baseVariance = scheduledHour < 12 ? 30 : 45;
        return Math.floor((Math.random() - 0.5) * 2 * baseVariance);
    }
    static isHoliday(date) {
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const holidays = [
            { month: 1, day: 1 },
            { month: 7, day: 4 },
            { month: 12, day: 25 },
            { month: 11, day: 24 },
        ];
        return holidays.some(holiday => holiday.month === month && holiday.day === day);
    }
    static calculateGamificationStats(adherenceRecords) {
        const takenRecords = adherenceRecords.filter(r => r.status === 'taken');
        const totalRecords = adherenceRecords.length;
        let currentStreak = 0;
        let longestStreak = 0;
        let tempStreak = 0;
        const sortedRecords = adherenceRecords.sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime());
        for (const record of sortedRecords) {
            if (record.status === 'taken') {
                tempStreak++;
                longestStreak = Math.max(longestStreak, tempStreak);
            }
            else {
                tempStreak = 0;
            }
        }
        for (let i = sortedRecords.length - 1; i >= 0; i--) {
            if (sortedRecords[i].status === 'taken') {
                currentStreak++;
            }
            else {
                break;
            }
        }
        const adherenceRate = totalRecords > 0 ? (takenRecords.length / totalRecords) * 100 : 0;
        const totalPoints = Math.floor(takenRecords.length * 10 + longestStreak * 5);
        const level = Math.floor(totalPoints / 100) + 1;
        return {
            currentStreak,
            longestStreak,
            totalPoints,
            level,
            adherenceRate: Math.round(adherenceRate * 100) / 100,
            totalMedicinesTaken: takenRecords.length,
        };
    }
    static generateWeeklyProgress(adherenceRecords) {
        const weeklyProgress = new Array(7).fill(0);
        const now = new Date();
        for (let i = 0; i < 7; i++) {
            const targetDate = new Date(now);
            targetDate.setDate(targetDate.getDate() - i);
            const dayRecords = adherenceRecords.filter(record => {
                const recordDate = new Date(record.scheduledTime);
                return recordDate.toDateString() === targetDate.toDateString();
            });
            const takenCount = dayRecords.filter(r => r.status === 'taken').length;
            weeklyProgress[6 - i] = takenCount;
        }
        return weeklyProgress;
    }
    static generateMonthlyProgress(adherenceRecords) {
        const monthlyProgress = new Array(31).fill(0);
        const now = new Date();
        for (let i = 0; i < 31; i++) {
            const targetDate = new Date(now);
            targetDate.setDate(targetDate.getDate() - i);
            const dayRecords = adherenceRecords.filter(record => {
                const recordDate = new Date(record.scheduledTime);
                return recordDate.toDateString() === targetDate.toDateString();
            });
            const takenCount = dayRecords.filter(r => r.status === 'taken').length;
            monthlyProgress[30 - i] = takenCount;
        }
        return monthlyProgress;
    }
}
exports.TimeSeriesGenerator = TimeSeriesGenerator;
//# sourceMappingURL=time-series-generator.js.map