"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("../src/app.module");
const supabase_service_1 = require("../src/config/supabase.service");
async function simpleVerify() {
    const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
    const supabaseService = app.get(supabase_service_1.SupabaseService);
    const supabase = supabaseService.getAdminClient();
    console.log('🔍 Quick verification of enhanced seeding...\n');
    try {
        const tables = [
            'users',
            'patients',
            'doctors',
            'hospitals',
            'insurance_providers',
            'medicines',
            'adherence_records',
            'reminders',
            'gamification_stats'
        ];
        for (const table of tables) {
            const { count, error } = await supabase
                .from(table)
                .select('*', { count: 'exact', head: true });
            if (error) {
                console.log(`❌ Error counting ${table}:`, error.message);
            }
            else {
                console.log(`📊 ${table}: ${count} records`);
            }
        }
        console.log('\n✅ Verification completed!');
    }
    catch (error) {
        console.error('❌ Error:', error);
    }
    finally {
        await app.close();
    }
}
simpleVerify();
//# sourceMappingURL=simple-verify.js.map