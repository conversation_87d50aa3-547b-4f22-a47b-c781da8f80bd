{"version": 3, "file": "simple-verify.js", "sourceRoot": "", "sources": ["../../scripts/simple-verify.ts"], "names": [], "mappings": ";;AAAA,uCAA2C;AAC3C,kDAA8C;AAC9C,qEAAiE;AAEjE,KAAK,UAAU,YAAY;IACzB,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,wBAAwB,CAAC,sBAAS,CAAC,CAAC;IAClE,MAAM,eAAe,GAAG,GAAG,CAAC,GAAG,CAAC,kCAAe,CAAC,CAAC;IACjD,MAAM,QAAQ,GAAG,eAAe,CAAC,cAAc,EAAE,CAAC;IAElD,OAAO,CAAC,GAAG,CAAC,gDAAgD,CAAC,CAAC;IAE9D,IAAI,CAAC;QAEH,MAAM,MAAM,GAAG;YACb,OAAO;YACP,UAAU;YACV,SAAS;YACT,WAAW;YACX,qBAAqB;YACrB,WAAW;YACX,mBAAmB;YACnB,WAAW;YACX,oBAAoB;SACrB,CAAC;QAEF,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ;iBACpC,IAAI,CAAC,KAAK,CAAC;iBACX,MAAM,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;YAE/C,IAAI,KAAK,EAAE,CAAC;gBACV,OAAO,CAAC,GAAG,CAAC,oBAAoB,KAAK,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC3D,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,GAAG,CAAC,MAAM,KAAK,KAAK,KAAK,UAAU,CAAC,CAAC;YAC/C,CAAC;QACH,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAE7C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;IACnC,CAAC;YAAS,CAAC;QACT,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;AACH,CAAC;AAED,YAAY,EAAE,CAAC"}