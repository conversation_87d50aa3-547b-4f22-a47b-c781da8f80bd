import { HttpException, HttpStatus } from '@nestjs/common';
export declare class BusinessException extends HttpException {
    constructor(message: string, statusCode?: HttpStatus, errorCode?: string, details?: any);
}
export declare class UnauthorizedAccessException extends BusinessException {
    constructor(resource: string, action: string);
}
export declare class ResourceNotFoundException extends BusinessException {
    constructor(resource: string, identifier: string);
}
export declare class DuplicateResourceException extends BusinessException {
    constructor(resource: string, field: string, value: string);
}
export declare class InvalidOperationException extends BusinessException {
    constructor(operation: string, reason: string);
}
export declare class InsufficientPermissionsException extends BusinessException {
    constructor(requiredRole: string, currentRole: string);
}
export declare class RateLimitExceededException extends BusinessException {
    constructor(limit: number, windowMs: number);
}
