"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimitExceededException = exports.InsufficientPermissionsException = exports.InvalidOperationException = exports.DuplicateResourceException = exports.ResourceNotFoundException = exports.UnauthorizedAccessException = exports.BusinessException = void 0;
const common_1 = require("@nestjs/common");
class BusinessException extends common_1.HttpException {
    constructor(message, statusCode = common_1.HttpStatus.BAD_REQUEST, errorCode, details) {
        const errorResponse = {
            message,
            error: 'Business Logic Error',
            statusCode,
            errorCode,
            details,
            timestamp: new Date().toISOString(),
        };
        super(errorResponse, statusCode);
    }
}
exports.BusinessException = BusinessException;
class UnauthorizedAccessException extends BusinessException {
    constructor(resource, action) {
        super(`Unauthorized access: Cannot ${action} ${resource}`, common_1.HttpStatus.FORBIDDEN, 'UNAUTHORIZED_ACCESS', { resource, action });
    }
}
exports.UnauthorizedAccessException = UnauthorizedAccessException;
class ResourceNotFoundException extends BusinessException {
    constructor(resource, identifier) {
        super(`${resource} with identifier '${identifier}' not found`, common_1.HttpStatus.NOT_FOUND, 'RESOURCE_NOT_FOUND', { resource, identifier });
    }
}
exports.ResourceNotFoundException = ResourceNotFoundException;
class DuplicateResourceException extends BusinessException {
    constructor(resource, field, value) {
        super(`${resource} with ${field} '${value}' already exists`, common_1.HttpStatus.CONFLICT, 'DUPLICATE_RESOURCE', { resource, field, value });
    }
}
exports.DuplicateResourceException = DuplicateResourceException;
class InvalidOperationException extends BusinessException {
    constructor(operation, reason) {
        super(`Invalid operation: ${operation}. Reason: ${reason}`, common_1.HttpStatus.BAD_REQUEST, 'INVALID_OPERATION', { operation, reason });
    }
}
exports.InvalidOperationException = InvalidOperationException;
class InsufficientPermissionsException extends BusinessException {
    constructor(requiredRole, currentRole) {
        super(`Insufficient permissions: Required role '${requiredRole}', current role '${currentRole}'`, common_1.HttpStatus.FORBIDDEN, 'INSUFFICIENT_PERMISSIONS', { requiredRole, currentRole });
    }
}
exports.InsufficientPermissionsException = InsufficientPermissionsException;
class RateLimitExceededException extends BusinessException {
    constructor(limit, windowMs) {
        super(`Rate limit exceeded: ${limit} requests per ${windowMs}ms`, common_1.HttpStatus.TOO_MANY_REQUESTS, 'RATE_LIMIT_EXCEEDED', { limit, windowMs });
    }
}
exports.RateLimitExceededException = RateLimitExceededException;
//# sourceMappingURL=business.exception.js.map