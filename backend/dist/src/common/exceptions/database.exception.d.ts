import { HttpException, HttpStatus } from '@nestjs/common';
export declare class DatabaseException extends HttpException {
    constructor(message: string, statusCode?: HttpStatus, errorCode?: string, details?: any);
}
export declare class DatabaseConnectionException extends DatabaseException {
    constructor(message?: string);
}
export declare class DatabaseConstraintException extends DatabaseException {
    constructor(constraint: string, table: string, details?: any);
}
export declare class ForeignKeyConstraintException extends DatabaseConstraintException {
    constructor(table: string, column: string, referencedTable: string);
}
export declare class UniqueConstraintException extends DatabaseConstraintException {
    constructor(table: string, column: string, value: any);
}
export declare class DatabaseTimeoutException extends DatabaseException {
    constructor(query: string, timeout: number);
}
export declare class DatabaseTransactionException extends DatabaseException {
    constructor(message: string, operation: string);
}
export declare class DatabaseMigrationException extends DatabaseException {
    constructor(migration: string, message: string);
}
