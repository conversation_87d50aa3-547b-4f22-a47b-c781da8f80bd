"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseMigrationException = exports.DatabaseTransactionException = exports.DatabaseTimeoutException = exports.UniqueConstraintException = exports.ForeignKeyConstraintException = exports.DatabaseConstraintException = exports.DatabaseConnectionException = exports.DatabaseException = void 0;
const common_1 = require("@nestjs/common");
class DatabaseException extends common_1.HttpException {
    constructor(message, statusCode = common_1.HttpStatus.INTERNAL_SERVER_ERROR, errorCode, details) {
        const errorResponse = {
            message: `Database error: ${message}`,
            error: 'Database Error',
            statusCode,
            errorCode,
            details,
            timestamp: new Date().toISOString(),
        };
        super(errorResponse, statusCode);
    }
}
exports.DatabaseException = DatabaseException;
class DatabaseConnectionException extends DatabaseException {
    constructor(message = 'Failed to connect to database') {
        super(message, common_1.HttpStatus.SERVICE_UNAVAILABLE, 'DB_CONNECTION_ERROR');
    }
}
exports.DatabaseConnectionException = DatabaseConnectionException;
class DatabaseConstraintException extends DatabaseException {
    constructor(constraint, table, details) {
        super(`Constraint violation: ${constraint} on table ${table}`, common_1.HttpStatus.CONFLICT, 'DB_CONSTRAINT_VIOLATION', { constraint, table, ...details });
    }
}
exports.DatabaseConstraintException = DatabaseConstraintException;
class ForeignKeyConstraintException extends DatabaseConstraintException {
    constructor(table, column, referencedTable) {
        super(`foreign_key_${column}`, table, {
            column,
            referencedTable,
            type: 'foreign_key',
        });
    }
}
exports.ForeignKeyConstraintException = ForeignKeyConstraintException;
class UniqueConstraintException extends DatabaseConstraintException {
    constructor(table, column, value) {
        super(`unique_${column}`, table, {
            column,
            value,
            type: 'unique',
        });
    }
}
exports.UniqueConstraintException = UniqueConstraintException;
class DatabaseTimeoutException extends DatabaseException {
    constructor(query, timeout) {
        super(`Query timeout after ${timeout}ms`, common_1.HttpStatus.REQUEST_TIMEOUT, 'DB_QUERY_TIMEOUT', { query: query.substring(0, 100), timeout });
    }
}
exports.DatabaseTimeoutException = DatabaseTimeoutException;
class DatabaseTransactionException extends DatabaseException {
    constructor(message, operation) {
        super(`Transaction error during ${operation}: ${message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR, 'DB_TRANSACTION_ERROR', { operation });
    }
}
exports.DatabaseTransactionException = DatabaseTransactionException;
class DatabaseMigrationException extends DatabaseException {
    constructor(migration, message) {
        super(`Migration error in ${migration}: ${message}`, common_1.HttpStatus.INTERNAL_SERVER_ERROR, 'DB_MIGRATION_ERROR', { migration });
    }
}
exports.DatabaseMigrationException = DatabaseMigrationException;
//# sourceMappingURL=database.exception.js.map