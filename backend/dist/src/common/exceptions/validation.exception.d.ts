import { HttpException } from '@nestjs/common';
import { ValidationError } from 'class-validator';
export interface ValidationErrorDetail {
    field: string;
    value: any;
    constraints: Record<string, string>;
    children?: ValidationErrorDetail[];
}
export declare class CustomValidationException extends HttpException {
    constructor(errors: ValidationError[], message?: string);
    private static formatValidationErrors;
    private static mapValidationError;
}
export declare class RequiredFieldException extends HttpException {
    constructor(field: string);
}
export declare class InvalidFieldFormatException extends HttpException {
    constructor(field: string, expectedFormat: string, actualValue: any);
}
export declare class InvalidFieldRangeException extends HttpException {
    constructor(field: string, actualValue: any, minValue?: any, maxValue?: any);
}
export declare class InvalidEnumValueException extends HttpException {
    constructor(field: string, actualValue: any, allowedValues: any[]);
}
