"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InvalidEnumValueException = exports.InvalidFieldRangeException = exports.InvalidFieldFormatException = exports.RequiredFieldException = exports.CustomValidationException = void 0;
const common_1 = require("@nestjs/common");
class CustomValidationException extends common_1.HttpException {
    constructor(errors, message = 'Validation failed') {
        const validationErrors = CustomValidationException.formatValidationErrors(errors);
        const errorResponse = {
            message,
            error: 'Validation Error',
            statusCode: common_1.HttpStatus.BAD_REQUEST,
            errorCode: 'VALIDATION_FAILED',
            validationErrors,
            timestamp: new Date().toISOString(),
        };
        super(errorResponse, common_1.HttpStatus.BAD_REQUEST);
    }
    static formatValidationErrors(errors) {
        return errors.map(error => CustomValidationException.mapValidationError(error));
    }
    static mapValidationError(error) {
        const detail = {
            field: error.property,
            value: error.value,
            constraints: error.constraints || {},
        };
        if (error.children && error.children.length > 0) {
            detail.children = error.children.map(child => CustomValidationException.mapValidationError(child));
        }
        return detail;
    }
}
exports.CustomValidationException = CustomValidationException;
class RequiredFieldException extends common_1.HttpException {
    constructor(field) {
        const errorResponse = {
            message: `Required field '${field}' is missing`,
            error: 'Validation Error',
            statusCode: common_1.HttpStatus.BAD_REQUEST,
            errorCode: 'REQUIRED_FIELD_MISSING',
            field,
            timestamp: new Date().toISOString(),
        };
        super(errorResponse, common_1.HttpStatus.BAD_REQUEST);
    }
}
exports.RequiredFieldException = RequiredFieldException;
class InvalidFieldFormatException extends common_1.HttpException {
    constructor(field, expectedFormat, actualValue) {
        const errorResponse = {
            message: `Invalid format for field '${field}'. Expected: ${expectedFormat}`,
            error: 'Validation Error',
            statusCode: common_1.HttpStatus.BAD_REQUEST,
            errorCode: 'INVALID_FIELD_FORMAT',
            field,
            expectedFormat,
            actualValue,
            timestamp: new Date().toISOString(),
        };
        super(errorResponse, common_1.HttpStatus.BAD_REQUEST);
    }
}
exports.InvalidFieldFormatException = InvalidFieldFormatException;
class InvalidFieldRangeException extends common_1.HttpException {
    constructor(field, actualValue, minValue, maxValue) {
        let message = `Invalid value for field '${field}': ${actualValue}`;
        if (minValue !== undefined && maxValue !== undefined) {
            message += `. Expected range: ${minValue} - ${maxValue}`;
        }
        else if (minValue !== undefined) {
            message += `. Minimum value: ${minValue}`;
        }
        else if (maxValue !== undefined) {
            message += `. Maximum value: ${maxValue}`;
        }
        const errorResponse = {
            message,
            error: 'Validation Error',
            statusCode: common_1.HttpStatus.BAD_REQUEST,
            errorCode: 'INVALID_FIELD_RANGE',
            field,
            actualValue,
            minValue,
            maxValue,
            timestamp: new Date().toISOString(),
        };
        super(errorResponse, common_1.HttpStatus.BAD_REQUEST);
    }
}
exports.InvalidFieldRangeException = InvalidFieldRangeException;
class InvalidEnumValueException extends common_1.HttpException {
    constructor(field, actualValue, allowedValues) {
        const errorResponse = {
            message: `Invalid value '${actualValue}' for field '${field}'. Allowed values: ${allowedValues.join(', ')}`,
            error: 'Validation Error',
            statusCode: common_1.HttpStatus.BAD_REQUEST,
            errorCode: 'INVALID_ENUM_VALUE',
            field,
            actualValue,
            allowedValues,
            timestamp: new Date().toISOString(),
        };
        super(errorResponse, common_1.HttpStatus.BAD_REQUEST);
    }
}
exports.InvalidEnumValueException = InvalidEnumValueException;
//# sourceMappingURL=validation.exception.js.map