import { ExceptionFilter, ArgumentsHost, BadRequestException } from '@nestjs/common';
export declare class ValidationExceptionFilter implements ExceptionFilter {
    private readonly logger;
    catch(exception: BadRequestException, host: ArgumentsHost): void;
    private isValidationError;
    private extractValidationErrors;
}
import { ValidationPipe, ValidationPipeOptions } from '@nestjs/common';
export declare class CustomValidationPipe extends ValidationPipe {
    constructor(options?: ValidationPipeOptions);
}
