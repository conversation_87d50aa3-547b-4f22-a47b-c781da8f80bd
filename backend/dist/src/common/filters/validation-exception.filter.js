"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var ValidationExceptionFilter_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.CustomValidationPipe = exports.ValidationExceptionFilter = void 0;
const common_1 = require("@nestjs/common");
const exceptions_1 = require("../exceptions");
let ValidationExceptionFilter = ValidationExceptionFilter_1 = class ValidationExceptionFilter {
    logger = new common_1.Logger(ValidationExceptionFilter_1.name);
    catch(exception, host) {
        const ctx = host.switchToHttp();
        const response = ctx.getResponse();
        const request = ctx.getRequest();
        const requestId = request.requestId || 'unknown';
        const exceptionResponse = exception.getResponse();
        if (this.isValidationError(exceptionResponse)) {
            const validationErrors = this.extractValidationErrors(exceptionResponse);
            const errorResponse = {
                message: 'Validation failed',
                error: 'Validation Error',
                statusCode: 400,
                errorCode: 'VALIDATION_FAILED',
                validationErrors,
                requestId,
                path: request.url,
                timestamp: new Date().toISOString(),
            };
            this.logger.warn(`Validation Error on ${request.method} ${request.url}`, {
                requestId,
                validationErrors,
                userId: request.user?.id,
            });
            response.status(400).json(errorResponse);
        }
        else {
            throw exception;
        }
    }
    isValidationError(exceptionResponse) {
        return (typeof exceptionResponse === 'object' &&
            Array.isArray(exceptionResponse.message) &&
            exceptionResponse.message.length > 0 &&
            typeof exceptionResponse.message[0] === 'string');
    }
    extractValidationErrors(exceptionResponse) {
        if (!Array.isArray(exceptionResponse.message)) {
            return [];
        }
        return exceptionResponse.message.map((error) => {
            const parts = error.split(' ');
            const property = parts[0];
            const constraints = parts.slice(1).join(' ');
            return {
                field: property,
                constraints: { [property]: constraints },
                message: error,
            };
        });
    }
};
exports.ValidationExceptionFilter = ValidationExceptionFilter;
exports.ValidationExceptionFilter = ValidationExceptionFilter = ValidationExceptionFilter_1 = __decorate([
    (0, common_1.Catch)(common_1.BadRequestException)
], ValidationExceptionFilter);
const common_2 = require("@nestjs/common");
class CustomValidationPipe extends common_2.ValidationPipe {
    constructor(options) {
        super({
            ...options,
            exceptionFactory: (errors) => {
                return new exceptions_1.CustomValidationException(errors);
            },
        });
    }
}
exports.CustomValidationPipe = CustomValidationPipe;
//# sourceMappingURL=validation-exception.filter.js.map