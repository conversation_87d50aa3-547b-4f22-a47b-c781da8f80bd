import { CanActivate, ExecutionContext } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { SupabaseService } from '../../config/supabase.service';
export declare class WsAuthGuard implements CanActivate {
    private readonly jwtService;
    private readonly supabaseService;
    private readonly logger;
    constructor(jwtService: JwtService, supabaseService: SupabaseService);
    canActivate(context: ExecutionContext): Promise<boolean>;
    private extractTokenFromSocket;
    private getUserFromDatabase;
}
export declare class WsRolesGuard implements CanActivate {
    private readonly logger;
    canActivate(context: ExecutionContext): boolean;
    private getRequiredRoles;
}
export declare const WsRoles: (...roles: string[]) => (target: any, propertyKey?: string, descriptor?: PropertyDescriptor) => void;
