"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WsAuthGuard_1, WsRolesGuard_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.WsRoles = exports.WsRolesGuard = exports.WsAuthGuard = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const websockets_1 = require("@nestjs/websockets");
const supabase_service_1 = require("../../config/supabase.service");
let WsAuthGuard = WsAuthGuard_1 = class WsAuthGuard {
    jwtService;
    supabaseService;
    logger = new common_1.Logger(WsAuthGuard_1.name);
    constructor(jwtService, supabaseService) {
        this.jwtService = jwtService;
        this.supabaseService = supabaseService;
    }
    async canActivate(context) {
        try {
            const client = context.switchToWs().getClient();
            const token = this.extractTokenFromSocket(client);
            if (!token) {
                throw new websockets_1.WsException('Authentication token not provided');
            }
            const payload = await this.jwtService.verifyAsync(token, {
                secret: process.env.JWT_SECRET,
            });
            if (!payload || !payload.sub) {
                throw new websockets_1.WsException('Invalid authentication token');
            }
            const user = await this.getUserFromDatabase(payload.sub);
            if (!user) {
                throw new websockets_1.WsException('User not found');
            }
            client.data.user = user;
            client.data.userId = user.id;
            client.data.userRole = user.role;
            this.logger.log(`WebSocket authenticated: ${user.id} (${user.role})`);
            return true;
        }
        catch (error) {
            this.logger.error('WebSocket authentication failed:', error.message);
            throw new websockets_1.WsException('Authentication failed');
        }
    }
    extractTokenFromSocket(client) {
        const authToken = client.handshake.auth?.token;
        if (authToken) {
            return authToken;
        }
        const queryToken = client.handshake.query?.token;
        if (queryToken && typeof queryToken === 'string') {
            return queryToken;
        }
        const headerAuth = client.handshake.headers?.authorization;
        if (headerAuth && typeof headerAuth === 'string') {
            const [type, token] = headerAuth.split(' ');
            if (type === 'Bearer' && token) {
                return token;
            }
        }
        return null;
    }
    async getUserFromDatabase(userId) {
        try {
            const supabase = this.supabaseService.getAdminClient();
            const { data: user, error } = await supabase
                .from('users')
                .select('*')
                .eq('id', userId)
                .single();
            if (error) {
                this.logger.error('Error fetching user from database:', error);
                return null;
            }
            return user;
        }
        catch (error) {
            this.logger.error('Database error while fetching user:', error);
            return null;
        }
    }
};
exports.WsAuthGuard = WsAuthGuard;
exports.WsAuthGuard = WsAuthGuard = WsAuthGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [jwt_1.JwtService,
        supabase_service_1.SupabaseService])
], WsAuthGuard);
let WsRolesGuard = WsRolesGuard_1 = class WsRolesGuard {
    logger = new common_1.Logger(WsRolesGuard_1.name);
    canActivate(context) {
        const requiredRoles = this.getRequiredRoles(context);
        if (!requiredRoles || requiredRoles.length === 0) {
            return true;
        }
        const client = context.switchToWs().getClient();
        const user = client.data.user;
        if (!user) {
            throw new websockets_1.WsException('User not authenticated');
        }
        const hasRole = requiredRoles.includes(user.role);
        if (!hasRole) {
            this.logger.warn(`Access denied for user ${user.id} with role ${user.role}. Required roles: ${requiredRoles.join(', ')}`);
            throw new websockets_1.WsException('Insufficient permissions');
        }
        return true;
    }
    getRequiredRoles(context) {
        const handler = context.getHandler();
        const classRef = context.getClass();
        const methodRoles = Reflect.getMetadata('roles', handler) || [];
        const classRoles = Reflect.getMetadata('roles', classRef) || [];
        return [...methodRoles, ...classRoles];
    }
};
exports.WsRolesGuard = WsRolesGuard;
exports.WsRolesGuard = WsRolesGuard = WsRolesGuard_1 = __decorate([
    (0, common_1.Injectable)()
], WsRolesGuard);
const WsRoles = (...roles) => {
    return (target, propertyKey, descriptor) => {
        if (propertyKey && descriptor) {
            Reflect.defineMetadata('roles', roles, descriptor.value);
        }
        else {
            Reflect.defineMetadata('roles', roles, target);
        }
    };
};
exports.WsRoles = WsRoles;
//# sourceMappingURL=ws-auth.guard.js.map