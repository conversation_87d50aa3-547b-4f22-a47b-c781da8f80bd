"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AwsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AwsService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const client_textract_1 = require("@aws-sdk/client-textract");
const client_s3_1 = require("@aws-sdk/client-s3");
let AwsService = AwsService_1 = class AwsService {
    configService;
    logger = new common_1.Logger(AwsService_1.name);
    textractClient;
    s3Client;
    bucketName;
    constructor(configService) {
        this.configService = configService;
        const region = this.configService.get('AWS_REGION', 'us-east-1');
        const accessKeyId = this.configService.get('AWS_ACCESS_KEY_ID');
        const secretAccessKey = this.configService.get('AWS_SECRET_ACCESS_KEY');
        if (!accessKeyId || !secretAccessKey) {
            throw new Error('AWS credentials are required. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.');
        }
        this.textractClient = new client_textract_1.TextractClient({
            region,
            credentials: {
                accessKeyId,
                secretAccessKey,
            },
        });
        this.s3Client = new client_s3_1.S3Client({
            region,
            credentials: {
                accessKeyId,
                secretAccessKey,
            },
        });
        this.bucketName = this.configService.get('AWS_S3_BUCKET_NAME', 'medcare-prescriptions');
    }
    async uploadFileToS3(file, fileName, contentType) {
        try {
            const key = `prescriptions/${Date.now()}-${fileName}`;
            const command = new client_s3_1.PutObjectCommand({
                Bucket: this.bucketName,
                Key: key,
                Body: file,
                ContentType: contentType,
            });
            await this.s3Client.send(command);
            const region = this.configService.get('AWS_REGION', 'us-east-1');
            return `https://${this.bucketName}.s3.${region}.amazonaws.com/${key}`;
        }
        catch (error) {
            this.logger.error('Failed to upload file to S3:', error);
            throw new Error('Failed to upload file to S3');
        }
    }
    async extractTextFromPrescription(fileBuffer) {
        try {
            const command = new client_textract_1.AnalyzeDocumentCommand({
                Document: {
                    Bytes: fileBuffer,
                },
                FeatureTypes: ['FORMS', 'TABLES'],
            });
            const response = await this.textractClient.send(command);
            if (!response.Blocks) {
                throw new Error('No text blocks found in document');
            }
            const textBlocks = response.Blocks.filter(block => block.BlockType === 'LINE');
            const extractedText = textBlocks
                .map(block => block.Text)
                .filter(text => text && text.trim())
                .join('\n');
            const confidenceScores = textBlocks
                .map(block => block.Confidence || 0)
                .filter(confidence => confidence > 0);
            const averageConfidence = confidenceScores.length > 0
                ? confidenceScores.reduce((sum, conf) => sum + conf, 0) / confidenceScores.length
                : 0;
            const medicineInfo = this.extractMedicineInformation(response.Blocks);
            return {
                extractedText,
                confidence: averageConfidence,
                medicineInfo,
            };
        }
        catch (error) {
            this.logger.error('Failed to extract text from prescription:', error);
            throw new Error('Failed to extract text from prescription');
        }
    }
    extractMedicineInformation(blocks) {
        const medicines = [];
        try {
            const keyValueBlocks = blocks.filter(block => block.BlockType === 'KEY_VALUE_SET');
            const keyBlocks = keyValueBlocks.filter(block => block.EntityTypes?.includes('KEY'));
            const valueBlocks = keyValueBlocks.filter(block => block.EntityTypes?.includes('VALUE'));
            const keyValueMap = new Map();
            keyBlocks.forEach(keyBlock => {
                if (keyBlock.Relationships) {
                    const valueRelationship = keyBlock.Relationships.find(rel => rel.Type === 'VALUE');
                    if (valueRelationship && valueRelationship.Ids) {
                        const valueBlock = valueBlocks.find(vb => valueRelationship.Ids.includes(vb.Id));
                        if (valueBlock) {
                            const keyText = this.getTextFromBlock(keyBlock, blocks);
                            const valueText = this.getTextFromBlock(valueBlock, blocks);
                            keyValueMap.set(keyText.toLowerCase(), valueText);
                        }
                    }
                }
            });
            const medicineKeywords = [
                'medicine', 'medication', 'drug', 'tablet', 'capsule', 'syrup',
                'dosage', 'dose', 'frequency', 'duration', 'instructions'
            ];
            keyValueMap.forEach((value, key) => {
                if (medicineKeywords.some(keyword => key.includes(keyword))) {
                    medicines.push({
                        field: key,
                        value: value,
                        type: this.categorizeField(key)
                    });
                }
            });
            const tableBlocks = blocks.filter(block => block.BlockType === 'TABLE');
            tableBlocks.forEach(table => {
                const tableData = this.extractTableData(table, blocks);
                if (tableData.length > 0) {
                    medicines.push({
                        type: 'table',
                        data: tableData
                    });
                }
            });
        }
        catch (error) {
            this.logger.warn('Failed to extract structured medicine information:', error);
        }
        return medicines;
    }
    getTextFromBlock(block, allBlocks) {
        if (block.Text) {
            return block.Text;
        }
        if (block.Relationships) {
            const childRelationship = block.Relationships.find(rel => rel.Type === 'CHILD');
            if (childRelationship && childRelationship.Ids) {
                const childTexts = childRelationship.Ids
                    .map(id => allBlocks.find(b => b.Id === id))
                    .filter(b => b && b.Text)
                    .map(b => b.Text);
                return childTexts.join(' ');
            }
        }
        return '';
    }
    categorizeField(fieldName) {
        const lowerField = fieldName.toLowerCase();
        if (lowerField.includes('medicine') || lowerField.includes('medication') || lowerField.includes('drug')) {
            return 'medicine_name';
        }
        if (lowerField.includes('dosage') || lowerField.includes('dose')) {
            return 'dosage';
        }
        if (lowerField.includes('frequency')) {
            return 'frequency';
        }
        if (lowerField.includes('duration')) {
            return 'duration';
        }
        if (lowerField.includes('instruction')) {
            return 'instructions';
        }
        return 'other';
    }
    extractTableData(tableBlock, allBlocks) {
        const tableData = [];
        try {
            if (tableBlock.Relationships) {
                const cellRelationship = tableBlock.Relationships.find(rel => rel.Type === 'CHILD');
                if (cellRelationship && cellRelationship.Ids) {
                    const cells = cellRelationship.Ids
                        .map(id => allBlocks.find(b => b.Id === id))
                        .filter(b => b && b.BlockType === 'CELL');
                    const rowMap = new Map();
                    cells.forEach(cell => {
                        const rowIndex = cell.RowIndex || 0;
                        if (!rowMap.has(rowIndex)) {
                            rowMap.set(rowIndex, []);
                        }
                        rowMap.get(rowIndex).push({
                            column: cell.ColumnIndex || 0,
                            text: this.getTextFromBlock(cell, allBlocks)
                        });
                    });
                    rowMap.forEach((cells, rowIndex) => {
                        const sortedCells = cells.sort((a, b) => a.column - b.column);
                        tableData.push({
                            row: rowIndex,
                            cells: sortedCells.map(cell => cell.text)
                        });
                    });
                }
            }
        }
        catch (error) {
            this.logger.warn('Failed to extract table data:', error);
        }
        return tableData;
    }
};
exports.AwsService = AwsService;
exports.AwsService = AwsService = AwsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AwsService);
//# sourceMappingURL=aws.service.js.map