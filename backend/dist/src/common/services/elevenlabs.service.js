"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ElevenLabsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ElevenLabsService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const elevenlabs_js_1 = require("@elevenlabs/elevenlabs-js");
let ElevenLabsService = ElevenLabsService_1 = class ElevenLabsService {
    configService;
    logger = new common_1.Logger(ElevenLabsService_1.name);
    elevenLabsClient;
    isEnabled;
    defaultVoiceId;
    agentId;
    agentPhoneNumberId;
    constructor(configService) {
        this.configService = configService;
        const apiKey = this.configService.get('ELEVENLABS_API_KEY');
        this.defaultVoiceId = this.configService.get('ELEVENLABS_VOICE_ID', 'pNInz6obpgDQGcFmaJgB');
        this.agentId = this.configService.get('ELEVENLABS_AGENT_ID', '');
        this.agentPhoneNumberId = this.configService.get('ELEVENLABS_AGENT_PHONE_NUMBER_ID', '');
        this.isEnabled = !!(apiKey);
        if (this.isEnabled) {
            this.elevenLabsClient = new elevenlabs_js_1.ElevenLabsClient({
                apiKey: apiKey,
            });
            this.logger.log('ElevenLabs service initialized successfully');
        }
        else {
            this.logger.warn('ElevenLabs service disabled - missing configuration (ELEVENLABS_API_KEY)');
        }
    }
    async makeConversationalCall(options) {
        if (!this.isEnabled) {
            this.logger.warn('ElevenLabs not configured - conversational call not made');
            return { success: false, error: 'ElevenLabs not configured' };
        }
        if (!this.agentId || !this.agentPhoneNumberId) {
            this.logger.warn('ElevenLabs agent not configured - conversational call not made');
            return { success: false, error: 'ElevenLabs agent not configured' };
        }
        try {
            const response = await this.elevenLabsClient.conversationalAi.twilio.outboundCall({
                agentId: this.agentId,
                agentPhoneNumberId: this.agentPhoneNumberId,
                toNumber: options.phoneNumber,
            });
            this.logger.log(`Conversational call initiated successfully to ${options.phoneNumber}`);
            return { success: true, callId: response.callSid };
        }
        catch (error) {
            this.logger.error(`Failed to make conversational call to ${options.phoneNumber}:`, error);
            return { success: false, error: error.message };
        }
    }
    async generateSpeech(options) {
        if (!this.isEnabled) {
            this.logger.warn('ElevenLabs not configured - speech generation skipped');
            return { success: false, error: 'ElevenLabs not configured' };
        }
        try {
            const audio = await this.elevenLabsClient.textToSpeech.convert(options.voiceId || this.defaultVoiceId, {
                text: options.text,
                modelId: options.modelId || 'eleven_monolingual_v1',
                voiceSettings: {
                    stability: 0.5,
                    similarityBoost: 0.75,
                },
            });
            const chunks = [];
            for await (const chunk of audio) {
                chunks.push(Buffer.from(chunk));
            }
            const audioBuffer = Buffer.concat(chunks);
            this.logger.log(`Speech generated successfully for text: "${options.text.substring(0, 50)}..."`);
            return { success: true, audioBuffer };
        }
        catch (error) {
            this.logger.error(`Failed to generate speech:`, error);
            return { success: false, error: error.message };
        }
    }
    async generateMedicationReminderSpeech(patientName, medicineName, dosage, voiceId) {
        const message = this.generateMedicationReminderMessage(patientName, medicineName, dosage);
        return this.generateSpeech({
            text: message,
            voiceId: voiceId || this.defaultVoiceId,
        });
    }
    generateMedicationReminderMessage(patientName, medicineName, dosage) {
        return `Hello ${patientName}, this is your friendly medication reminder from MedCare. It's time to take your ${medicineName}, ${dosage}. Please remember to take it as prescribed by your doctor. Taking your medication on time is important for your health and recovery. If you have any questions about your medication, please consult with your healthcare provider. Thank you for trusting MedCare with your health journey. Have a wonderful day!`;
    }
    async getAvailableVoices() {
        if (!this.isEnabled) {
            return [];
        }
        try {
            const voices = await this.elevenLabsClient.voices.getAll();
            return voices.voices || [];
        }
        catch (error) {
            this.logger.error('Failed to fetch available voices:', error);
            return [];
        }
    }
    getServiceStatus() {
        return {
            enabled: this.isEnabled,
            configured: !!this.configService.get('ELEVENLABS_API_KEY'),
            agentConfigured: !!(this.agentId && this.agentPhoneNumberId),
            voiceId: this.defaultVoiceId,
        };
    }
    async createAgent(name, prompt) {
        if (!this.isEnabled) {
            throw new Error('ElevenLabs not configured');
        }
        try {
            const agent = await this.elevenLabsClient.conversationalAi.agents.create({
                name: name,
                conversationConfig: {
                    agent: {
                        prompt: {
                            prompt: prompt || 'You are a helpful medical assistant for medication reminders.',
                        },
                    },
                },
            });
            this.logger.log(`Agent created successfully: ${agent.agentId}`);
            return agent;
        }
        catch (error) {
            this.logger.error('Failed to create agent:', error);
            throw error;
        }
    }
    async generateBulkSpeech(messages) {
        if (!this.isEnabled) {
            return messages.map(m => ({
                fileName: m.fileName,
                success: false,
                error: 'ElevenLabs not configured'
            }));
        }
        const results = [];
        for (const message of messages) {
            const result = await this.generateSpeech({
                text: message.text,
                voiceId: message.voiceId,
            });
            results.push({
                fileName: message.fileName,
                ...result,
            });
            await new Promise(resolve => setTimeout(resolve, 200));
        }
        return results;
    }
};
exports.ElevenLabsService = ElevenLabsService;
exports.ElevenLabsService = ElevenLabsService = ElevenLabsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], ElevenLabsService);
//# sourceMappingURL=elevenlabs.service.js.map