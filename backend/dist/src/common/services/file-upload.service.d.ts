import { ConfigService } from '@nestjs/config';
import * as multer from 'multer';
export declare class FileUploadService {
    private readonly configService;
    private readonly maxFileSize;
    private readonly allowedMimeTypes;
    constructor(configService: ConfigService);
    getMulterOptions(): multer.Options;
    validateFile(file: Express.Multer.File): void;
    generateFileName(originalName: string): string;
}
