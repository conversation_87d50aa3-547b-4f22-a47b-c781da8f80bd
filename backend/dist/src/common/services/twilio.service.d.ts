import { ConfigService } from '@nestjs/config';
export interface SMSOptions {
    to: string;
    message: string;
    from?: string;
}
export interface CallOptions {
    to: string;
    message: string;
    from?: string;
    voice?: 'man' | 'woman' | 'alice';
}
export interface SMSResponse {
    success: boolean;
    messageSid?: string;
    error?: string;
}
export interface CallResponse {
    success: boolean;
    callSid?: string;
    error?: string;
}
export declare class TwilioService {
    private readonly configService;
    private readonly logger;
    private readonly twilioClient;
    private readonly fromPhoneNumber;
    private readonly isEnabled;
    constructor(configService: ConfigService);
    sendSMS(options: SMSOptions): Promise<SMSResponse>;
    makeCall(options: CallOptions): Promise<CallResponse>;
    sendMedicationReminder(phoneNumber: string, patientName: string, medicineName: string, dosage: string, reminderType?: 'sms' | 'call'): Promise<SMSResponse | CallResponse>;
    private generateMedicationReminderMessage;
    private generateTwiML;
    private escapeXml;
    isValidPhoneNumber(phoneNumber: string): boolean;
    getServiceStatus(): {
        enabled: boolean;
        configured: boolean;
    };
    sendBulkSMS(recipients: Array<{
        phoneNumber: string;
        message: string;
    }>): Promise<Array<SMSResponse & {
        phoneNumber: string;
    }>>;
}
