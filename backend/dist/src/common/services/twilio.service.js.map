{"version": 3, "file": "twilio.service.js", "sourceRoot": "", "sources": ["../../../../src/common/services/twilio.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,mCAAgC;AA4BzB,IAAM,aAAa,qBAAnB,MAAM,aAAa;IAMK;IALZ,MAAM,GAAG,IAAI,eAAM,CAAC,eAAa,CAAC,IAAI,CAAC,CAAC;IACxC,YAAY,CAAS;IACrB,eAAe,CAAS;IACxB,SAAS,CAAU;IAEpC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QACvD,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,oBAAoB,CAAC,CAAC;QACxE,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,mBAAmB,CAAC,CAAC;QACtE,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,qBAAqB,EAAE,EAAE,CAAC,CAAC;QAGjF,IAAI,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,SAAS,IAAI,IAAI,CAAC,eAAe,CAAC,CAAC;QAErE,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACnB,IAAI,CAAC,YAAY,GAAG,IAAI,eAAM,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8GAA8G,CAAC,CAAC;QACnI,CAAC;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,OAAmB;QAC/B,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YACzD,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACtD,IAAI,EAAE,OAAO,CAAC,OAAO;gBACrB,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe;gBAC1C,EAAE,EAAE,OAAO,CAAC,EAAE;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,OAAO,CAAC,EAAE,UAAU,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YAC/E,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,yBAAyB,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACjE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAoB;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YAC1D,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC;QAC5D,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC;YAEjE,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC;gBAChD,KAAK,EAAE,KAAK;gBACZ,IAAI,EAAE,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,eAAe;gBAC1C,EAAE,EAAE,OAAO,CAAC,EAAE;aACf,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,OAAO,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;YAClF,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC;QAC9C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAClE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC;QAClD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,sBAAsB,CAC1B,WAAmB,EACnB,WAAmB,EACnB,YAAoB,EACpB,MAAc,EACd,eAA+B,KAAK;QAEpC,MAAM,OAAO,GAAG,IAAI,CAAC,iCAAiC,CAAC,WAAW,EAAE,YAAY,EAAE,MAAM,CAAC,CAAC;QAE1F,IAAI,YAAY,KAAK,KAAK,EAAE,CAAC;YAC3B,OAAO,IAAI,CAAC,OAAO,CAAC;gBAClB,EAAE,EAAE,WAAW;gBACf,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,QAAQ,CAAC;gBACnB,EAAE,EAAE,WAAW;gBACf,OAAO,EAAE,OAAO;gBAChB,KAAK,EAAE,OAAO;aACf,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,iCAAiC,CACvC,WAAmB,EACnB,YAAoB,EACpB,MAAc;QAEd,OAAO,MAAM,WAAW,0DAA0D,YAAY,KAAK,MAAM,8EAA8E,CAAC;IAC1L,CAAC;IAEO,aAAa,CAAC,OAAe,EAAE,QAAgB,OAAO;QAC5D,OAAO;;gBAEK,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;;gBAEjC,KAAK;YACT,CAAC;IACX,CAAC;IAEO,SAAS,CAAC,IAAY;QAC5B,OAAO,IAAI;aACR,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC;aACtB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;aACvB,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAGD,kBAAkB,CAAC,WAAmB;QAEpC,MAAM,UAAU,GAAG,mBAAmB,CAAC;QACvC,OAAO,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACtC,CAAC;IAGD,gBAAgB;QACd,OAAO;YACL,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,UAAU,EAAE,CAAC,CAAC,CACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,oBAAoB,CAAC;gBAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,mBAAmB,CAAC;gBAC3C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAC9C;SACF,CAAC;IACJ,CAAC;IAGD,KAAK,CAAC,WAAW,CAAC,UAA2D;QAC3E,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;YACpB,OAAO,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;gBAC1B,WAAW,EAAE,CAAC,CAAC,WAAW;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,uBAAuB;aAC/B,CAAC,CAAC,CAAC;QACN,CAAC;QAED,MAAM,OAAO,GAAiD,EAAE,CAAC;QAEjE,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE,CAAC;YACnC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC;gBAChC,EAAE,EAAE,SAAS,CAAC,WAAW;gBACzB,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B,CAAC,CAAC;YAEH,OAAO,CAAC,IAAI,CAAC;gBACX,WAAW,EAAE,SAAS,CAAC,WAAW;gBAClC,GAAG,MAAM;aACV,CAAC,CAAC;YAGH,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;CACF,CAAA;AApKY,sCAAa;wBAAb,aAAa;IADzB,IAAA,mBAAU,GAAE;qCAOiC,sBAAa;GAN9C,aAAa,CAoKzB"}