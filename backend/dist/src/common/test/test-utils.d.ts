import { TestingModule } from '@nestjs/testing';
export declare const mockUsers: {
    patient: {
        id: string;
        email: string;
        name: string;
        role: "patient";
        created_at: Date;
        updated_at: Date;
    };
    doctor: {
        id: string;
        email: string;
        name: string;
        role: "doctor";
        created_at: Date;
        updated_at: Date;
    };
    hospital: {
        id: string;
        email: string;
        name: string;
        role: "hospital";
        created_at: Date;
        updated_at: Date;
    };
    insurance: {
        id: string;
        email: string;
        name: string;
        role: "insurance";
        created_at: Date;
        updated_at: Date;
    };
    admin: {
        id: string;
        email: string;
        name: string;
        role: "admin";
        created_at: Date;
        updated_at: Date;
    };
};
export declare const mockMedicine: {
    id: string;
    name: string;
    dosage: string;
    frequency: string;
    duration: number;
    instructions: string;
    start_date: Date;
    end_date: Date;
    prescription_id: string;
    patient_id: string;
    created_at: Date;
    updated_at: Date;
};
export declare const mockPrescription: {
    id: string;
    patient_id: string;
    doctor_id: string;
    filename: string;
    file_url: string;
    status: "completed";
    uploaded_at: Date;
    created_at: Date;
    updated_at: Date;
};
export declare const createMockConfigService: (config?: Record<string, any>) => {
    get: jest.Mock<any, [key: string], any>;
    getOrThrow: jest.Mock<any, [key: string], any>;
};
export declare const createMockJwtService: () => {
    sign: jest.Mock<any, any, any>;
    verify: jest.Mock<any, any, any>;
    decode: jest.Mock<any, any, any>;
};
export declare const createMockSupabaseClient: () => {
    from: jest.Mock<any, any, any>;
    select: jest.Mock<any, any, any>;
    insert: jest.Mock<any, any, any>;
    update: jest.Mock<any, any, any>;
    delete: jest.Mock<any, any, any>;
    eq: jest.Mock<any, any, any>;
    neq: jest.Mock<any, any, any>;
    gt: jest.Mock<any, any, any>;
    gte: jest.Mock<any, any, any>;
    lt: jest.Mock<any, any, any>;
    lte: jest.Mock<any, any, any>;
    like: jest.Mock<any, any, any>;
    ilike: jest.Mock<any, any, any>;
    in: jest.Mock<any, any, any>;
    order: jest.Mock<any, any, any>;
    limit: jest.Mock<any, any, any>;
    range: jest.Mock<any, any, any>;
    single: jest.Mock<any, any, any>;
    maybeSingle: jest.Mock<any, any, any>;
    then: jest.Mock<any, any, any>;
    auth: {
        getUser: jest.Mock<any, any, any>;
        signInWithPassword: jest.Mock<any, any, any>;
        signUp: jest.Mock<any, any, any>;
        signOut: jest.Mock<any, any, any>;
    };
};
export declare const createTestModule: (providers?: any[], imports?: any[], config?: Record<string, any>) => Promise<TestingModule>;
export declare const generateMockJwtToken: (payload?: any) => string;
export declare const createMockRequest: (overrides?: any) => any;
export declare const createMockResponse: () => {
    status: jest.Mock<any, any, any>;
    json: jest.Mock<any, any, any>;
    send: jest.Mock<any, any, any>;
    cookie: jest.Mock<any, any, any>;
    clearCookie: jest.Mock<any, any, any>;
    redirect: jest.Mock<any, any, any>;
};
