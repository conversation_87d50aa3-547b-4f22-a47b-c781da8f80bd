"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMockResponse = exports.createMockRequest = exports.generateMockJwtToken = exports.createTestModule = exports.createMockSupabaseClient = exports.createMockJwtService = exports.createMockConfigService = exports.mockPrescription = exports.mockMedicine = exports.mockUsers = void 0;
const testing_1 = require("@nestjs/testing");
const config_1 = require("@nestjs/config");
const jwt_1 = require("@nestjs/jwt");
exports.mockUsers = {
    patient: {
        id: '550e8400-e29b-41d4-a716-446655440001',
        email: '<EMAIL>',
        name: 'Test Patient',
        role: 'patient',
        created_at: new Date(),
        updated_at: new Date(),
    },
    doctor: {
        id: '550e8400-e29b-41d4-a716-446655440002',
        email: '<EMAIL>',
        name: 'Dr. Test Doctor',
        role: 'doctor',
        created_at: new Date(),
        updated_at: new Date(),
    },
    hospital: {
        id: '550e8400-e29b-41d4-a716-446655440003',
        email: '<EMAIL>',
        name: 'Test Hospital',
        role: 'hospital',
        created_at: new Date(),
        updated_at: new Date(),
    },
    insurance: {
        id: '550e8400-e29b-41d4-a716-446655440004',
        email: '<EMAIL>',
        name: 'Test Insurance',
        role: 'insurance',
        created_at: new Date(),
        updated_at: new Date(),
    },
    admin: {
        id: '550e8400-e29b-41d4-a716-446655440005',
        email: '<EMAIL>',
        name: 'Test Admin',
        role: 'admin',
        created_at: new Date(),
        updated_at: new Date(),
    },
};
exports.mockMedicine = {
    id: '550e8400-e29b-41d4-a716-446655440010',
    name: 'Test Medicine',
    dosage: '10mg',
    frequency: 'twice daily',
    duration: 30,
    instructions: 'Take with food',
    start_date: new Date(),
    end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
    prescription_id: '550e8400-e29b-41d4-a716-446655440020',
    patient_id: exports.mockUsers.patient.id,
    created_at: new Date(),
    updated_at: new Date(),
};
exports.mockPrescription = {
    id: '550e8400-e29b-41d4-a716-446655440020',
    patient_id: exports.mockUsers.patient.id,
    doctor_id: exports.mockUsers.doctor.id,
    filename: 'test-prescription.pdf',
    file_url: 'https://test.com/prescription.pdf',
    status: 'completed',
    uploaded_at: new Date(),
    created_at: new Date(),
    updated_at: new Date(),
};
const createMockConfigService = (config = {}) => ({
    get: jest.fn((key) => config[key]),
    getOrThrow: jest.fn((key) => {
        if (config[key] === undefined) {
            throw new Error(`Configuration key "${key}" not found`);
        }
        return config[key];
    }),
});
exports.createMockConfigService = createMockConfigService;
const createMockJwtService = () => ({
    sign: jest.fn().mockReturnValue('mock-jwt-token'),
    verify: jest.fn().mockReturnValue({ sub: exports.mockUsers.patient.id, role: 'patient' }),
    decode: jest.fn().mockReturnValue({ sub: exports.mockUsers.patient.id, role: 'patient' }),
});
exports.createMockJwtService = createMockJwtService;
const createMockSupabaseClient = () => ({
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    neq: jest.fn().mockReturnThis(),
    gt: jest.fn().mockReturnThis(),
    gte: jest.fn().mockReturnThis(),
    lt: jest.fn().mockReturnThis(),
    lte: jest.fn().mockReturnThis(),
    like: jest.fn().mockReturnThis(),
    ilike: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    range: jest.fn().mockReturnThis(),
    single: jest.fn().mockResolvedValue({ data: null, error: null }),
    maybeSingle: jest.fn().mockResolvedValue({ data: null, error: null }),
    then: jest.fn().mockResolvedValue({ data: [], error: null }),
    auth: {
        getUser: jest.fn().mockResolvedValue({ data: { user: exports.mockUsers.patient }, error: null }),
        signInWithPassword: jest.fn().mockResolvedValue({ data: { user: exports.mockUsers.patient }, error: null }),
        signUp: jest.fn().mockResolvedValue({ data: { user: exports.mockUsers.patient }, error: null }),
        signOut: jest.fn().mockResolvedValue({ error: null }),
    },
});
exports.createMockSupabaseClient = createMockSupabaseClient;
const createTestModule = async (providers = [], imports = [], config = {}) => {
    return testing_1.Test.createTestingModule({
        imports,
        providers: [
            {
                provide: config_1.ConfigService,
                useValue: (0, exports.createMockConfigService)(config),
            },
            {
                provide: jwt_1.JwtService,
                useValue: (0, exports.createMockJwtService)(),
            },
            {
                provide: 'SUPABASE_CLIENT',
                useValue: (0, exports.createMockSupabaseClient)(),
            },
            ...providers,
        ],
    }).compile();
};
exports.createTestModule = createTestModule;
const generateMockJwtToken = (payload = { sub: exports.mockUsers.patient.id, role: 'patient' }) => {
    const header = Buffer.from(JSON.stringify({ alg: 'HS256', typ: 'JWT' })).toString('base64');
    const payloadStr = Buffer.from(JSON.stringify(payload)).toString('base64');
    const signature = 'mock-signature';
    return `${header}.${payloadStr}.${signature}`;
};
exports.generateMockJwtToken = generateMockJwtToken;
const createMockRequest = (overrides = {}) => ({
    method: 'GET',
    url: '/api/v1/test',
    headers: {},
    body: {},
    query: {},
    params: {},
    user: exports.mockUsers.patient,
    ip: '127.0.0.1',
    get: jest.fn().mockReturnValue('test-user-agent'),
    ...overrides,
});
exports.createMockRequest = createMockRequest;
const createMockResponse = () => ({
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    clearCookie: jest.fn().mockReturnThis(),
    redirect: jest.fn().mockReturnThis(),
});
exports.createMockResponse = createMockResponse;
//# sourceMappingURL=test-utils.js.map