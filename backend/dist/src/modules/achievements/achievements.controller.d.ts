import { AchievementsService } from './achievements.service';
import { CreateAchievementDto, UpdateAchievementDto, AchievementQueryDto, AchievementProgress, AchievementSummary } from './dto';
import { Achievement, UserAchievement } from '../../common/types';
export declare class AchievementsController {
    private readonly achievementsService;
    constructor(achievementsService: AchievementsService);
    create(createAchievementDto: CreateAchievementDto, req: any): Promise<Achievement>;
    findAll(query: AchievementQueryDto): Promise<Achievement[]>;
    getUserAchievements(userId: string, req: any): Promise<UserAchievement[]>;
    getAchievementProgress(userId: string, req: any): Promise<AchievementProgress[]>;
    getAchievementSummary(userId: string, req: any): Promise<AchievementSummary>;
    checkAndAwardAchievements(userId: string, req: any): Promise<UserAchievement[]>;
    seedDefaultAchievements(req: any): Promise<{
        message: string;
    }>;
    findOne(id: string): Promise<Achievement>;
    update(id: string, updateAchievementDto: UpdateAchievementDto, req: any): Promise<Achievement>;
    remove(id: string, req: any): Promise<void>;
}
