"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AchievementsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const achievements_service_1 = require("./achievements.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let AchievementsController = class AchievementsController {
    achievementsService;
    constructor(achievementsService) {
        this.achievementsService = achievementsService;
    }
    async create(createAchievementDto, req) {
        return this.achievementsService.create(createAchievementDto, req.user);
    }
    async findAll(query) {
        return this.achievementsService.findAll(query);
    }
    async getUserAchievements(userId, req) {
        return this.achievementsService.getUserAchievements(userId, req.user);
    }
    async getAchievementProgress(userId, req) {
        return this.achievementsService.getAchievementProgress(userId, req.user);
    }
    async getAchievementSummary(userId, req) {
        return this.achievementsService.getAchievementSummary(userId, req.user);
    }
    async checkAndAwardAchievements(userId, req) {
        return this.achievementsService.checkAndAwardAchievements(userId);
    }
    async seedDefaultAchievements(req) {
        await this.achievementsService.seedDefaultAchievements();
        return { message: 'Default achievements seeded successfully' };
    }
    async findOne(id) {
        return this.achievementsService.findOne(id);
    }
    async update(id, updateAchievementDto, req) {
        return this.achievementsService.update(id, updateAchievementDto, req.user);
    }
    async remove(id, req) {
        return this.achievementsService.remove(id, req.user);
    }
};
exports.AchievementsController = AchievementsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create achievement',
        description: 'Create a new achievement (admin only)'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Achievement created successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions - admin only',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateAchievementDto, Object]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all achievements',
        description: 'Retrieve all available achievements with optional filtering'
    }),
    (0, swagger_1.ApiQuery)({ name: 'category', required: false, description: 'Filter by achievement category' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Number of achievements to return' }),
    (0, swagger_1.ApiQuery)({ name: 'offset', required: false, description: 'Number of achievements to skip' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Achievements retrieved successfully',
        type: [Object],
    }),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.AchievementQueryDto]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('user/:user_id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get user achievements',
        description: 'Retrieve achievements earned by a specific user'
    }),
    (0, swagger_1.ApiParam)({ name: 'user_id', description: 'User ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'User achievements retrieved successfully',
        type: [Object],
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    __param(0, (0, common_1.Param)('user_id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "getUserAchievements", null);
__decorate([
    (0, common_1.Get)('progress/:user_id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get achievement progress',
        description: 'Retrieve progress toward all achievements for a user'
    }),
    (0, swagger_1.ApiParam)({ name: 'user_id', description: 'User ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Achievement progress retrieved successfully',
        type: [Object],
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    __param(0, (0, common_1.Param)('user_id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "getAchievementProgress", null);
__decorate([
    (0, common_1.Get)('summary/:user_id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get achievement summary',
        description: 'Retrieve comprehensive achievement summary for a user'
    }),
    (0, swagger_1.ApiParam)({ name: 'user_id', description: 'User ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Achievement summary retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    __param(0, (0, common_1.Param)('user_id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "getAchievementSummary", null);
__decorate([
    (0, common_1.Post)('check/:user_id'),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Check and award achievements',
        description: 'Check user progress and award any newly earned achievements (admin only)'
    }),
    (0, swagger_1.ApiParam)({ name: 'user_id', description: 'User ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Achievement check completed',
        type: [Object],
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions - admin only',
    }),
    __param(0, (0, common_1.Param)('user_id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "checkAndAwardAchievements", null);
__decorate([
    (0, common_1.Post)('seed'),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Seed default achievements',
        description: 'Seed the database with default achievements (admin only)'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Default achievements seeded successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions - admin only',
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "seedDefaultAchievements", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get achievement by ID',
        description: 'Retrieve a specific achievement'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Achievement ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Achievement retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Achievement not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update achievement',
        description: 'Update an existing achievement (admin only)'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Achievement ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Achievement updated successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions - admin only',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Achievement not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateAchievementDto, Object]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete achievement',
        description: 'Delete an achievement (admin only)'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Achievement ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Achievement deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions - admin only',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Achievement not found',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AchievementsController.prototype, "remove", null);
exports.AchievementsController = AchievementsController = __decorate([
    (0, swagger_1.ApiTags)('achievements'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('achievements'),
    __metadata("design:paramtypes", [achievements_service_1.AchievementsService])
], AchievementsController);
//# sourceMappingURL=achievements.controller.js.map