import { SupabaseService } from '../../config/supabase.service';
import { User, Achievement, UserAchievement } from '../../common/types';
import { CreateAchievementDto, UpdateAchievementDto, AchievementQueryDto, AchievementProgress, AchievementSummary } from './dto';
export declare class AchievementsService {
    private readonly supabaseService;
    constructor(supabaseService: SupabaseService);
    create(createAchievementDto: CreateAchievementDto, currentUser: User): Promise<Achievement>;
    findAll(query: AchievementQueryDto): Promise<Achievement[]>;
    findOne(id: string): Promise<Achievement>;
    update(id: string, updateAchievementDto: UpdateAchievementDto, currentUser: User): Promise<Achievement>;
    remove(id: string, currentUser: User): Promise<void>;
    getUserAchievements(userId: string, currentUser: User): Promise<UserAchievement[]>;
    getAchievementProgress(userId: string, currentUser: User): Promise<AchievementProgress[]>;
    private calculateAchievementProgress;
    private estimateCompletionTime;
    getAchievementSummary(userId: string, currentUser: User): Promise<AchievementSummary>;
    checkAndAwardAchievements(userId: string): Promise<UserAchievement[]>;
    private awardAchievementPoints;
    seedDefaultAchievements(): Promise<void>;
}
