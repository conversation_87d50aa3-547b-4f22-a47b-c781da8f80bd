"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AchievementsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../config/supabase.service");
const dto_1 = require("./dto");
let AchievementsService = class AchievementsService {
    supabaseService;
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async create(createAchievementDto, currentUser) {
        if (currentUser.role !== 'admin') {
            throw new common_1.ForbiddenException('Only administrators can create achievements');
        }
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('achievements')
            .insert({
            name: createAchievementDto.name,
            description: createAchievementDto.description,
            icon: createAchievementDto.icon,
            category: createAchievementDto.category,
            requirement: createAchievementDto.requirement,
            points: createAchievementDto.points,
        })
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to create achievement: ${error.message}`);
        }
        return data;
    }
    async findAll(query) {
        const supabase = this.supabaseService.getClient();
        let dbQuery = supabase
            .from('achievements')
            .select('*');
        if (query.category) {
            dbQuery = dbQuery.eq('category', query.category);
        }
        dbQuery = dbQuery
            .order('category', { ascending: true })
            .order('requirement', { ascending: true })
            .range(query.offset || 0, (query.offset || 0) + (query.limit || 20) - 1);
        const { data, error } = await dbQuery;
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch achievements: ${error.message}`);
        }
        return data || [];
    }
    async findOne(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('achievements')
            .select('*')
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Achievement with ID ${id} not found`);
        }
        return data;
    }
    async update(id, updateAchievementDto, currentUser) {
        if (currentUser.role !== 'admin') {
            throw new common_1.ForbiddenException('Only administrators can update achievements');
        }
        await this.findOne(id);
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('achievements')
            .update({
            ...updateAchievementDto,
            updated_at: new Date().toISOString(),
        })
            .eq('id', id)
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to update achievement: ${error.message}`);
        }
        return data;
    }
    async remove(id, currentUser) {
        if (currentUser.role !== 'admin') {
            throw new common_1.ForbiddenException('Only administrators can delete achievements');
        }
        await this.findOne(id);
        const supabase = this.supabaseService.getClient();
        const { error } = await supabase
            .from('achievements')
            .delete()
            .eq('id', id);
        if (error) {
            throw new common_1.BadRequestException(`Failed to delete achievement: ${error.message}`);
        }
    }
    async getUserAchievements(userId, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== userId) {
            throw new common_1.ForbiddenException('Patients can only view their own achievements');
        }
        if (currentUser.role === 'doctor') {
            const supabase = this.supabaseService.getClient();
            const { data: patient } = await supabase
                .from('patients')
                .select('assigned_doctor_id')
                .eq('id', userId)
                .single();
            if (!patient || patient.assigned_doctor_id !== currentUser.id) {
                throw new common_1.ForbiddenException('You can only view achievements for your assigned patients');
            }
        }
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('user_achievements')
            .select(`
        *,
        achievement:achievements(*)
      `)
            .eq('user_id', userId)
            .order('unlocked_at', { ascending: false });
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch user achievements: ${error.message}`);
        }
        return data || [];
    }
    async getAchievementProgress(userId, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== userId) {
            throw new common_1.ForbiddenException('Patients can only view their own progress');
        }
        const supabase = this.supabaseService.getClient();
        const { data: achievements } = await supabase
            .from('achievements')
            .select('*')
            .order('category', { ascending: true })
            .order('requirement', { ascending: true });
        if (!achievements) {
            return [];
        }
        const { data: userAchievements } = await supabase
            .from('user_achievements')
            .select('achievement_id, unlocked_at')
            .eq('user_id', userId);
        const unlockedMap = new Map((userAchievements || []).map(ua => [ua.achievement_id, ua.unlocked_at]));
        const progressList = [];
        for (const achievement of achievements) {
            const isUnlocked = unlockedMap.has(achievement.id);
            const unlockedAt = unlockedMap.get(achievement.id);
            let currentProgress = 0;
            let estimatedCompletion = 'Unknown';
            if (!isUnlocked) {
                currentProgress = await this.calculateAchievementProgress(userId, achievement);
                estimatedCompletion = this.estimateCompletionTime(achievement, currentProgress);
            }
            else {
                currentProgress = achievement.requirement;
            }
            const progressPercentage = Math.min((currentProgress / achievement.requirement) * 100, 100);
            progressList.push({
                achievement_id: achievement.id,
                achievement_name: achievement.name,
                achievement_description: achievement.description,
                achievement_icon: achievement.icon,
                category: achievement.category,
                requirement: achievement.requirement,
                points: achievement.points,
                current_progress: currentProgress,
                progress_percentage: Math.round(progressPercentage * 100) / 100,
                is_unlocked: isUnlocked,
                unlocked_at: unlockedAt ? new Date(unlockedAt) : undefined,
                estimated_completion: isUnlocked ? undefined : estimatedCompletion,
            });
        }
        return progressList;
    }
    async calculateAchievementProgress(userId, achievement) {
        const supabase = this.supabaseService.getClient();
        switch (achievement.category) {
            case 'streak':
                const { data: stats } = await supabase
                    .from('gamification_stats')
                    .select('current_streak')
                    .eq('patient_id', userId)
                    .single();
                return stats?.current_streak || 0;
            case 'consistency':
                const { data: consistencyStats } = await supabase
                    .from('gamification_stats')
                    .select('completion_rate')
                    .eq('patient_id', userId)
                    .single();
                return consistencyStats?.completion_rate || 0;
            case 'milestone':
                const { data: adherenceRecords } = await supabase
                    .from('adherence_records')
                    .select('id')
                    .eq('patient_id', userId)
                    .eq('status', 'taken');
                return adherenceRecords?.length || 0;
            case 'completion':
                const { data: prescriptions } = await supabase
                    .from('prescriptions')
                    .select('id')
                    .eq('patient_id', userId)
                    .eq('status', 'completed');
                return prescriptions?.length || 0;
            default:
                return 0;
        }
    }
    estimateCompletionTime(achievement, currentProgress) {
        const remaining = achievement.requirement - currentProgress;
        if (remaining <= 0)
            return 'Ready to unlock';
        switch (achievement.category) {
            case 'streak':
                return `${remaining} days`;
            case 'consistency':
                return 'Next month review';
            case 'milestone':
                const days = Math.ceil(remaining / 3);
                if (days < 30)
                    return `${days} days`;
                if (days < 365)
                    return `${Math.ceil(days / 30)} months`;
                return `${Math.ceil(days / 365)} years`;
            case 'completion':
                const months = remaining;
                if (months < 12)
                    return `${months} months`;
                return `${Math.ceil(months / 12)} years`;
            default:
                return 'Unknown';
        }
    }
    async getAchievementSummary(userId, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== userId) {
            throw new common_1.ForbiddenException('Patients can only view their own summary');
        }
        const supabase = this.supabaseService.getClient();
        const [achievementsResult, userAchievementsResult] = await Promise.all([
            supabase.from('achievements').select('*'),
            supabase.from('user_achievements').select(`
        *,
        achievement:achievements(name, points)
      `).eq('user_id', userId).order('unlocked_at', { ascending: false })
        ]);
        const achievements = achievementsResult.data || [];
        const userAchievements = userAchievementsResult.data || [];
        const totalAchievements = achievements.length;
        const unlockedAchievements = userAchievements.length;
        const totalPointsFromAchievements = userAchievements.reduce((sum, ua) => sum + (ua.achievement?.points || 0), 0);
        const completionPercentage = totalAchievements > 0
            ? (unlockedAchievements / totalAchievements) * 100
            : 0;
        const recentAchievements = userAchievements.slice(0, 5).map(ua => ({
            achievement_id: ua.achievement_id,
            achievement_name: ua.achievement?.name || 'Unknown Achievement',
            points: ua.achievement?.points || 0,
            unlocked_at: new Date(ua.unlocked_at),
        }));
        const progressList = await this.getAchievementProgress(userId, currentUser);
        const nextAchievements = progressList
            .filter(p => !p.is_unlocked && p.progress_percentage > 0)
            .sort((a, b) => b.progress_percentage - a.progress_percentage)
            .slice(0, 3)
            .map(p => ({
            achievement_id: p.achievement_id,
            achievement_name: p.achievement_name,
            progress_percentage: p.progress_percentage,
            estimated_completion: p.estimated_completion || 'Unknown',
        }));
        const categoryBreakdown = Object.values(dto_1.AchievementCategory).map(category => {
            const categoryAchievements = achievements.filter(a => a.category === category);
            const categoryUnlocked = userAchievements.filter(ua => achievements.find(a => a.id === ua.achievement_id)?.category === category);
            return {
                category,
                total: categoryAchievements.length,
                unlocked: categoryUnlocked.length,
                percentage: categoryAchievements.length > 0
                    ? (categoryUnlocked.length / categoryAchievements.length) * 100
                    : 0,
            };
        });
        return {
            user_id: userId,
            total_achievements: totalAchievements,
            unlocked_achievements: unlockedAchievements,
            total_points_from_achievements: totalPointsFromAchievements,
            completion_percentage: Math.round(completionPercentage * 100) / 100,
            recent_achievements: recentAchievements,
            next_achievements: nextAchievements,
            category_breakdown: categoryBreakdown,
        };
    }
    async checkAndAwardAchievements(userId) {
        const supabase = this.supabaseService.getClient();
        const { data: achievements } = await supabase
            .from('achievements')
            .select('*');
        if (!achievements)
            return [];
        const { data: userAchievements } = await supabase
            .from('user_achievements')
            .select('achievement_id')
            .eq('user_id', userId);
        const unlockedIds = new Set((userAchievements || []).map(ua => ua.achievement_id));
        const newAchievements = [];
        for (const achievement of achievements) {
            if (unlockedIds.has(achievement.id))
                continue;
            const currentProgress = await this.calculateAchievementProgress(userId, achievement);
            if (currentProgress >= achievement.requirement) {
                const { data: newAchievement, error } = await supabase
                    .from('user_achievements')
                    .insert({
                    user_id: userId,
                    achievement_id: achievement.id,
                })
                    .select(`
            *,
            achievement:achievements(*)
          `)
                    .single();
                if (!error && newAchievement) {
                    newAchievements.push(newAchievement);
                    await this.awardAchievementPoints(userId, achievement.points);
                }
            }
        }
        return newAchievements;
    }
    async awardAchievementPoints(userId, points) {
        const supabase = this.supabaseService.getClient();
        const { data: stats } = await supabase
            .from('gamification_stats')
            .select('total_points')
            .eq('patient_id', userId)
            .single();
        const currentPoints = stats?.total_points || 0;
        const newTotalPoints = currentPoints + points;
        await supabase
            .from('gamification_stats')
            .update({
            total_points: newTotalPoints,
            updated_at: new Date().toISOString(),
        })
            .eq('patient_id', userId);
    }
    async seedDefaultAchievements() {
        const supabase = this.supabaseService.getClient();
        const { data: existingAchievements } = await supabase
            .from('achievements')
            .select('name')
            .limit(1);
        if (existingAchievements && existingAchievements.length > 0) {
            return;
        }
        const { error } = await supabase
            .from('achievements')
            .insert(dto_1.DEFAULT_ACHIEVEMENTS);
        if (error) {
            throw new common_1.BadRequestException(`Failed to seed achievements: ${error.message}`);
        }
    }
};
exports.AchievementsService = AchievementsService;
exports.AchievementsService = AchievementsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], AchievementsService);
//# sourceMappingURL=achievements.service.js.map