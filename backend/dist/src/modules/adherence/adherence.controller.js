"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdherenceController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const adherence_service_1 = require("./adherence.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let AdherenceController = class AdherenceController {
    adherenceService;
    constructor(adherenceService) {
        this.adherenceService = adherenceService;
    }
    async create(createAdherenceDto, req) {
        return this.adherenceService.create(createAdherenceDto, req.user);
    }
    async findAll(query, req) {
        return this.adherenceService.findAll(query, req.user);
    }
    async getAnalytics(patientId, req, days) {
        const analyticsDto = {
            patient_id: patientId,
            days: days || 30,
        };
        return this.adherenceService.getAnalytics(analyticsDto, req.user);
    }
    async findOne(id, req) {
        return this.adherenceService.findOne(id, req.user);
    }
    async update(id, updateAdherenceDto, req) {
        return this.adherenceService.update(id, updateAdherenceDto, req.user);
    }
    async remove(id, req) {
        return this.adherenceService.remove(id, req.user);
    }
};
exports.AdherenceController = AdherenceController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Record medication adherence',
        description: 'Create a new adherence record for a patient taking medication'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Adherence record created successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data or duplicate record',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Patient or medicine not found',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateAdherenceRecordDto, Object]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get adherence records',
        description: 'Retrieve adherence records with optional filtering'
    }),
    (0, swagger_1.ApiQuery)({ name: 'patient_id', required: false, description: 'Filter by patient ID' }),
    (0, swagger_1.ApiQuery)({ name: 'medicine_id', required: false, description: 'Filter by medicine ID' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, description: 'Filter by adherence status' }),
    (0, swagger_1.ApiQuery)({ name: 'start_date', required: false, description: 'Start date for filtering' }),
    (0, swagger_1.ApiQuery)({ name: 'end_date', required: false, description: 'End date for filtering' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Number of records to return' }),
    (0, swagger_1.ApiQuery)({ name: 'offset', required: false, description: 'Number of records to skip' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Adherence records retrieved successfully',
        type: [Object],
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid query parameters',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.AdherenceQueryDto, Object]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('analytics/:patient_id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get adherence analytics',
        description: 'Retrieve detailed adherence analytics for a patient'
    }),
    (0, swagger_1.ApiParam)({ name: 'patient_id', description: 'Patient ID for analytics' }),
    (0, swagger_1.ApiQuery)({ name: 'days', required: false, description: 'Number of days to analyze (default: 30)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Analytics retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Patient not found',
    }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Request)()),
    __param(2, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Number]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "getAnalytics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get adherence record by ID',
        description: 'Retrieve a specific adherence record'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Adherence record ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Adherence record retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Adherence record not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update adherence record',
        description: 'Update an existing adherence record'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Adherence record ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Adherence record updated successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Adherence record not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateAdherenceRecordDto, Object]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete adherence record',
        description: 'Delete an adherence record'
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Adherence record ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NO_CONTENT,
        description: 'Adherence record deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Adherence record not found',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], AdherenceController.prototype, "remove", null);
exports.AdherenceController = AdherenceController = __decorate([
    (0, swagger_1.ApiTags)('adherence'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('adherence'),
    __metadata("design:paramtypes", [adherence_service_1.AdherenceService])
], AdherenceController);
//# sourceMappingURL=adherence.controller.js.map