export declare enum AdherenceStatus {
    TAKEN = "taken",
    MISSED = "missed",
    SKIPPED = "skipped"
}
export declare class CreateAdherenceRecordDto {
    patient_id: string;
    medicine_id: string;
    scheduled_time: string;
    taken_time?: string;
    status: AdherenceStatus;
    notes?: string;
}
export declare class UpdateAdherenceRecordDto {
    taken_time?: string;
    status?: AdherenceStatus;
    notes?: string;
}
export declare class AdherenceQueryDto {
    patient_id?: string;
    medicine_id?: string;
    status?: AdherenceStatus;
    start_date?: string;
    end_date?: string;
    limit?: number;
    offset?: number;
}
export declare class AdherenceAnalyticsDto {
    patient_id: string;
    days?: number;
}
export interface AdherenceAnalytics {
    patient_id: string;
    total_scheduled: number;
    total_taken: number;
    total_missed: number;
    total_skipped: number;
    adherence_rate: number;
    on_time_rate: number;
    late_rate: number;
    current_streak: number;
    longest_streak: number;
    daily_breakdown: {
        date: string;
        scheduled: number;
        taken: number;
        missed: number;
        skipped: number;
        rate: number;
    }[];
    medicine_breakdown: {
        medicine_id: string;
        medicine_name: string;
        scheduled: number;
        taken: number;
        missed: number;
        skipped: number;
        rate: number;
    }[];
}
