"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdherenceAnalyticsDto = exports.AdherenceQueryDto = exports.UpdateAdherenceRecordDto = exports.CreateAdherenceRecordDto = exports.AdherenceStatus = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
var AdherenceStatus;
(function (AdherenceStatus) {
    AdherenceStatus["TAKEN"] = "taken";
    AdherenceStatus["MISSED"] = "missed";
    AdherenceStatus["SKIPPED"] = "skipped";
})(AdherenceStatus || (exports.AdherenceStatus = AdherenceStatus = {}));
class CreateAdherenceRecordDto {
    patient_id;
    medicine_id;
    scheduled_time;
    taken_time;
    status;
    notes;
}
exports.CreateAdherenceRecordDto = CreateAdherenceRecordDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the patient',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAdherenceRecordDto.prototype, "patient_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'ID of the medicine',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAdherenceRecordDto.prototype, "medicine_id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Scheduled time for the medication',
        example: '2024-01-15T08:00:00Z',
    }),
    (0, class_validator_1.IsDateString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAdherenceRecordDto.prototype, "scheduled_time", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Actual time when medication was taken',
        example: '2024-01-15T08:15:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], CreateAdherenceRecordDto.prototype, "taken_time", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of medication adherence',
        enum: AdherenceStatus,
        example: AdherenceStatus.TAKEN,
    }),
    (0, class_validator_1.IsEnum)(AdherenceStatus),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], CreateAdherenceRecordDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional notes about the medication event',
        example: 'Took with breakfast',
        maxLength: 500,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], CreateAdherenceRecordDto.prototype, "notes", void 0);
class UpdateAdherenceRecordDto {
    taken_time;
    status;
    notes;
}
exports.UpdateAdherenceRecordDto = UpdateAdherenceRecordDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Actual time when medication was taken',
        example: '2024-01-15T08:15:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], UpdateAdherenceRecordDto.prototype, "taken_time", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Status of medication adherence',
        enum: AdherenceStatus,
        example: AdherenceStatus.TAKEN,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(AdherenceStatus),
    __metadata("design:type", String)
], UpdateAdherenceRecordDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional notes about the medication event',
        example: 'Took with breakfast',
        maxLength: 500,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.MaxLength)(500),
    __metadata("design:type", String)
], UpdateAdherenceRecordDto.prototype, "notes", void 0);
class AdherenceQueryDto {
    patient_id;
    medicine_id;
    status;
    start_date;
    end_date;
    limit = 50;
    offset = 0;
}
exports.AdherenceQueryDto = AdherenceQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by patient ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], AdherenceQueryDto.prototype, "patient_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by medicine ID',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], AdherenceQueryDto.prototype, "medicine_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by adherence status',
        enum: AdherenceStatus,
        example: AdherenceStatus.TAKEN,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(AdherenceStatus),
    __metadata("design:type", String)
], AdherenceQueryDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Start date for filtering (ISO string)',
        example: '2024-01-01T00:00:00Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AdherenceQueryDto.prototype, "start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'End date for filtering (ISO string)',
        example: '2024-01-31T23:59:59Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AdherenceQueryDto.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of records to return',
        example: 50,
        default: 50,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    __metadata("design:type", Number)
], AdherenceQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of records to skip',
        example: 0,
        default: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    __metadata("design:type", Number)
], AdherenceQueryDto.prototype, "offset", void 0);
class AdherenceAnalyticsDto {
    patient_id;
    days = 30;
}
exports.AdherenceAnalyticsDto = AdherenceAnalyticsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Patient ID for analytics',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], AdherenceAnalyticsDto.prototype, "patient_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of days to analyze (default: 30)',
        example: 30,
        default: 30,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    __metadata("design:type", Number)
], AdherenceAnalyticsDto.prototype, "days", void 0);
//# sourceMappingURL=index.js.map