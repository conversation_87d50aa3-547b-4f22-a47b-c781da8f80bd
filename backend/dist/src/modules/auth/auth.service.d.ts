import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { SupabaseService } from '../../config/supabase.service';
import { LoginDto, RegisterDto, AuthResponseDto } from './dto';
export declare class AuthService {
    private jwtService;
    private supabaseService;
    private configService;
    constructor(jwtService: JwtService, supabaseService: SupabaseService, configService: ConfigService);
    register(registerDto: RegisterDto): Promise<AuthResponseDto>;
    login(loginDto: LoginDto): Promise<AuthResponseDto>;
    validateUser(userId: string): Promise<any>;
    logout(userId: string): Promise<void>;
    private createRoleSpecificRecord;
}
