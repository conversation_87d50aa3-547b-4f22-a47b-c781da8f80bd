import { UserRole } from '../../../common/types';
export declare class LoginDto {
    email: string;
    password: string;
}
export declare class RegisterDto {
    email: string;
    password: string;
    name: string;
    role: UserRole;
    dateOfBirth?: string;
    emergencyContact?: string;
    assignedDoctorId?: string;
    insuranceId?: string;
    specialization?: string;
    licenseNumber?: string;
    hospitalId?: string;
    address?: string;
    phone?: string;
    website?: string;
    companyName?: string;
    policyTypes?: string[];
    coverageAreas?: string[];
}
export declare class UserResponseDto {
    id: string;
    email: string;
    name: string;
    role: UserRole;
    avatar?: string;
}
export declare class AuthResponseDto {
    user: UserResponseDto;
    accessToken: string;
}
