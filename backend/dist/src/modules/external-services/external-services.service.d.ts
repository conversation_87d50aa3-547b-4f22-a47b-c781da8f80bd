import { TwilioService } from '../../common/services/twilio.service';
import { ElevenLabsService } from '../../common/services/elevenlabs.service';
import { AwsService } from '../../common/services/aws.service';
export declare class ExternalServicesService {
    private readonly twilioService;
    private readonly elevenLabsService;
    private readonly awsService;
    private readonly logger;
    constructor(twilioService: TwilioService, elevenLabsService: ElevenLabsService, awsService: AwsService);
    getServicesStatus(): Promise<{
        twilio: {
            description: string;
            enabled: boolean;
            configured: boolean;
        };
        elevenlabs: {
            description: string;
            enabled: boolean;
            configured: boolean;
            agentConfigured: boolean;
            voiceId: string;
        };
        aws: {
            s3: {
                enabled: boolean;
                description: string;
            };
            textract: {
                enabled: boolean;
                description: string;
            };
        };
        integration: {
            reminders: {
                sms: boolean;
                calls: boolean;
                conversational_ai: boolean;
            };
        };
    }>;
    testSMS(phoneNumber: string, message: string): Promise<{
        success: boolean;
        error: string;
    } | {
        timestamp: string;
        success: boolean;
        messageSid?: string;
        error?: string;
        service: string;
    }>;
    testCall(phoneNumber: string, message: string): Promise<{
        success: boolean;
        error: string;
    } | {
        timestamp: string;
        success: boolean;
        callSid?: string;
        error?: string;
        service: string;
    }>;
    testElevenLabsCall(phoneNumber: string, patientName: string, medicineName: string, dosage: string): Promise<{
        success: boolean;
        error: string;
    } | {
        timestamp: string;
        success: boolean;
        callId?: string;
        error?: string;
        service: string;
    }>;
    getAWSStatus(): Promise<{
        s3: {
            enabled: boolean;
            configured: boolean;
            description: string;
        };
        textract: {
            enabled: boolean;
            configured: boolean;
            description: string;
        };
    }>;
    testTextract(): Promise<{
        service: string;
        success: boolean;
        result: {
            extractedText: string;
            confidence: number;
            medicineInfo: any[];
        };
        timestamp: string;
        error?: undefined;
    } | {
        service: string;
        success: boolean;
        error: any;
        timestamp: string;
        result?: undefined;
    }>;
    testAllServices(phoneNumber?: string): Promise<{
        timestamp: string;
        services: {};
    }>;
}
