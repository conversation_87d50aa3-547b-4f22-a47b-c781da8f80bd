"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.POINTS_CONFIG = exports.GAMIFICATION_LEVELS = exports.LeaderboardQueryDto = exports.UpdateGamificationStatsDto = exports.GamificationStatsDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const class_transformer_1 = require("class-transformer");
class GamificationStatsDto {
    patient_id;
}
exports.GamificationStatsDto = GamificationStatsDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Patient ID for gamification stats',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], GamificationStatsDto.prototype, "patient_id", void 0);
class UpdateGamificationStatsDto {
    current_streak;
    longest_streak;
    total_points;
    completion_rate;
    weekly_progress;
    monthly_progress;
}
exports.UpdateGamificationStatsDto = UpdateGamificationStatsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Current streak count',
        example: 7,
        minimum: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateGamificationStatsDto.prototype, "current_streak", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Longest streak achieved',
        example: 30,
        minimum: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateGamificationStatsDto.prototype, "longest_streak", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Total points earned',
        example: 1250,
        minimum: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    __metadata("design:type", Number)
], UpdateGamificationStatsDto.prototype, "total_points", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Overall completion rate percentage',
        example: 85.5,
        minimum: 0,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], UpdateGamificationStatsDto.prototype, "completion_rate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Weekly progress array',
        example: [80, 90, 75, 95, 85, 88, 92],
        type: [Number],
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateGamificationStatsDto.prototype, "weekly_progress", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Monthly progress array',
        example: [85, 88, 92, 87],
        type: [Number],
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Array)
], UpdateGamificationStatsDto.prototype, "monthly_progress", void 0);
class LeaderboardQueryDto {
    limit = 10;
    type = 'points';
}
exports.LeaderboardQueryDto = LeaderboardQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of top users to return',
        example: 10,
        default: 10,
        minimum: 1,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], LeaderboardQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Leaderboard type',
        example: 'points',
        enum: ['points', 'streak', 'completion_rate'],
        default: 'points',
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], LeaderboardQueryDto.prototype, "type", void 0);
exports.GAMIFICATION_LEVELS = [
    {
        level: 1,
        name: 'Beginner',
        min_points: 0,
        max_points: 100,
        benefits: ['Basic tracking', 'Simple achievements'],
    },
    {
        level: 2,
        name: 'Committed',
        min_points: 101,
        max_points: 300,
        benefits: ['Streak bonuses', 'Weekly challenges'],
    },
    {
        level: 3,
        name: 'Dedicated',
        min_points: 301,
        max_points: 600,
        benefits: ['Monthly rewards', 'Advanced analytics'],
    },
    {
        level: 4,
        name: 'Expert',
        min_points: 601,
        max_points: 1000,
        benefits: ['Premium features', 'Priority support'],
    },
    {
        level: 5,
        name: 'Master',
        min_points: 1001,
        max_points: 1500,
        benefits: ['Exclusive content', 'Mentor status'],
    },
    {
        level: 6,
        name: 'Champion',
        min_points: 1501,
        max_points: 2500,
        benefits: ['Leadership board', 'Special recognition'],
    },
    {
        level: 7,
        name: 'Legend',
        min_points: 2501,
        max_points: 5000,
        benefits: ['Hall of fame', 'Lifetime benefits'],
    },
    {
        level: 8,
        name: 'Grandmaster',
        min_points: 5001,
        max_points: Number.MAX_SAFE_INTEGER,
        benefits: ['Ultimate status', 'All features unlocked'],
    },
];
exports.POINTS_CONFIG = {
    MEDICATION_ON_TIME: 10,
    MEDICATION_LATE: 5,
    PERFECT_DAY_BONUS: 20,
    WEEKLY_STREAK_BONUS: 50,
    MONTHLY_CONSISTENCY_BONUS: 100,
    ACHIEVEMENT_MULTIPLIER: 1.5,
    STREAK_MULTIPLIER_THRESHOLD: 7,
    STREAK_MULTIPLIER: 1.2,
};
//# sourceMappingURL=index.js.map