"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GamificationController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const gamification_service_1 = require("./gamification.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let GamificationController = class GamificationController {
    gamificationService;
    constructor(gamificationService) {
        this.gamificationService = gamificationService;
    }
    async getStats(patientId, req) {
        return this.gamificationService.getStats(patientId, req.user);
    }
    async getDashboard(patientId, req) {
        return this.gamificationService.getDashboard(patientId, req.user);
    }
    async getLeaderboard(query, req) {
        return this.gamificationService.getLeaderboard(query, req.user);
    }
    async updateStats(patientId, updateDto, req) {
        return this.gamificationService.updateStats(patientId, updateDto, req.user);
    }
    async calculatePoints(patientId, body, req) {
        const scheduledTime = new Date(body.scheduled_time);
        const takenTime = body.taken_time ? new Date(body.taken_time) : undefined;
        return this.gamificationService.calculatePointsForAdherence(patientId, body.adherence_status, scheduledTime, takenTime);
    }
    async getLevels() {
        const { GAMIFICATION_LEVELS } = await Promise.resolve().then(() => require('./dto'));
        return GAMIFICATION_LEVELS;
    }
    async getPointsConfig() {
        const { POINTS_CONFIG } = await Promise.resolve().then(() => require('./dto'));
        return POINTS_CONFIG;
    }
};
exports.GamificationController = GamificationController;
__decorate([
    (0, common_1.Get)('stats/:patient_id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get gamification stats',
        description: 'Retrieve gamification statistics for a patient'
    }),
    (0, swagger_1.ApiParam)({ name: 'patient_id', description: 'Patient ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Gamification stats retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Patient not found',
    }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], GamificationController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)('dashboard/:patient_id'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get gamification dashboard',
        description: 'Retrieve comprehensive gamification dashboard for a patient'
    }),
    (0, swagger_1.ApiParam)({ name: 'patient_id', description: 'Patient ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Dashboard retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Patient not found',
    }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], GamificationController.prototype, "getDashboard", null);
__decorate([
    (0, common_1.Get)('leaderboard'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get leaderboard',
        description: 'Retrieve gamification leaderboard'
    }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Number of top users to return (default: 10)' }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false, description: 'Leaderboard type: points, streak, or completion_rate (default: points)' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Leaderboard retrieved successfully',
        type: [Object],
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid query parameters',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.LeaderboardQueryDto, Object]),
    __metadata("design:returntype", Promise)
], GamificationController.prototype, "getLeaderboard", null);
__decorate([
    (0, common_1.Patch)('stats/:patient_id'),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update gamification stats',
        description: 'Manually update gamification statistics (admin only)'
    }),
    (0, swagger_1.ApiParam)({ name: 'patient_id', description: 'Patient ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Stats updated successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions - admin only',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid input data',
    }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateGamificationStatsDto, Object]),
    __metadata("design:returntype", Promise)
], GamificationController.prototype, "updateStats", null);
__decorate([
    (0, common_1.Post)('calculate-points/:patient_id'),
    (0, roles_decorator_1.Roles)('admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Calculate points for adherence',
        description: 'Calculate points for a specific adherence event (admin/testing only)'
    }),
    (0, swagger_1.ApiParam)({ name: 'patient_id', description: 'Patient ID' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Points calculated successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Insufficient permissions - admin only',
    }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], GamificationController.prototype, "calculatePoints", null);
__decorate([
    (0, common_1.Get)('levels'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get gamification levels',
        description: 'Retrieve all available gamification levels and their requirements'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Levels retrieved successfully',
        type: [Object],
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], GamificationController.prototype, "getLevels", null);
__decorate([
    (0, common_1.Get)('points-config'),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get points configuration',
        description: 'Retrieve the points system configuration'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Points configuration retrieved successfully',
        type: Object,
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], GamificationController.prototype, "getPointsConfig", null);
exports.GamificationController = GamificationController = __decorate([
    (0, swagger_1.ApiTags)('gamification'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('gamification'),
    __metadata("design:paramtypes", [gamification_service_1.GamificationService])
], GamificationController);
//# sourceMappingURL=gamification.controller.js.map