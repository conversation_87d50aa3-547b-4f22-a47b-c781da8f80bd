import { SupabaseService } from '../../config/supabase.service';
import { User, GamificationStats } from '../../common/types';
import { UpdateGamificationStatsDto, LeaderboardQueryDto, LeaderboardEntry, GamificationDashboard, GamificationLevel, PointsCalculation } from './dto';
export declare class GamificationService {
    private readonly supabaseService;
    constructor(supabaseService: SupabaseService);
    getStats(patientId: string, currentUser: User): Promise<GamificationStats>;
    updateStats(patientId: string, updateDto: UpdateGamificationStatsDto, currentUser: User): Promise<GamificationStats>;
    calculatePointsForAdherence(patientId: string, adherenceStatus: string, scheduledTime: Date, takenTime?: Date): Promise<PointsCalculation>;
    updateStatsAfterAdherence(patientId: string, adherenceStatus: string, scheduledTime: Date, takenTime?: Date): Promise<void>;
    private recalculateStats;
    getLeaderboard(query: LeaderboardQueryDto, currentUser: User): Promise<LeaderboardEntry[]>;
    calculateLevel(points: number): GamificationLevel;
    getDashboard(patientId: string, currentUser: User): Promise<GamificationDashboard>;
    private calculateWeeklyProgress;
    private calculateMonthlyProgress;
    private calculateDailyRates;
    private calculateNextMilestones;
}
