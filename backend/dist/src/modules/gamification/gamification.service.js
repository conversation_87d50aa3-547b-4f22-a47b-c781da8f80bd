"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.GamificationService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../config/supabase.service");
const dto_1 = require("./dto");
let GamificationService = class GamificationService {
    supabaseService;
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async getStats(patientId, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== patientId) {
            throw new common_1.ForbiddenException('Patients can only view their own gamification stats');
        }
        if (currentUser.role === 'doctor') {
            const supabase = this.supabaseService.getClient();
            const { data: patient } = await supabase
                .from('patients')
                .select('assigned_doctor_id')
                .eq('id', patientId)
                .single();
            if (!patient || patient.assigned_doctor_id !== currentUser.id) {
                throw new common_1.ForbiddenException('You can only view stats for your assigned patients');
            }
        }
        const supabase = this.supabaseService.getClient();
        let { data: stats, error } = await supabase
            .from('gamification_stats')
            .select('*')
            .eq('patient_id', patientId)
            .single();
        if (error && error.code === 'PGRST116') {
            const { data: newStats, error: createError } = await supabase
                .from('gamification_stats')
                .insert({
                patient_id: patientId,
                current_streak: 0,
                longest_streak: 0,
                total_points: 0,
                completion_rate: 0.0,
                weekly_progress: [],
                monthly_progress: [],
            })
                .select()
                .single();
            if (createError) {
                throw new common_1.BadRequestException(`Failed to create gamification stats: ${createError.message}`);
            }
            stats = newStats;
        }
        else if (error) {
            throw new common_1.BadRequestException(`Failed to fetch gamification stats: ${error.message}`);
        }
        return stats;
    }
    async updateStats(patientId, updateDto, currentUser) {
        if (currentUser.role !== 'admin') {
            throw new common_1.ForbiddenException('Only administrators can manually update gamification stats');
        }
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('gamification_stats')
            .update({
            ...updateDto,
            updated_at: new Date().toISOString(),
        })
            .eq('patient_id', patientId)
            .select()
            .single();
        if (error) {
            throw new common_1.BadRequestException(`Failed to update gamification stats: ${error.message}`);
        }
        return data;
    }
    async calculatePointsForAdherence(patientId, adherenceStatus, scheduledTime, takenTime) {
        let basePoints = 0;
        let bonusPoints = 0;
        let reason = '';
        let multiplier = 1;
        if (adherenceStatus === 'taken' && takenTime) {
            const scheduled = new Date(scheduledTime);
            const taken = new Date(takenTime);
            const diffHours = (taken.getTime() - scheduled.getTime()) / (1000 * 60 * 60);
            if (diffHours <= 1) {
                basePoints = dto_1.POINTS_CONFIG.MEDICATION_ON_TIME;
                reason = 'Medication taken on time';
            }
            else if (diffHours <= 4) {
                basePoints = dto_1.POINTS_CONFIG.MEDICATION_LATE;
                reason = 'Medication taken late';
            }
            const stats = await this.getStats(patientId, { role: 'admin' });
            if (stats.current_streak >= dto_1.POINTS_CONFIG.STREAK_MULTIPLIER_THRESHOLD) {
                multiplier = dto_1.POINTS_CONFIG.STREAK_MULTIPLIER;
                reason += ` (${stats.current_streak}-day streak bonus)`;
            }
            const todayStart = new Date(scheduled);
            todayStart.setHours(0, 0, 0, 0);
            const todayEnd = new Date(scheduled);
            todayEnd.setHours(23, 59, 59, 999);
            const supabase = this.supabaseService.getClient();
            const { data: todayRecords } = await supabase
                .from('adherence_records')
                .select('status')
                .eq('patient_id', patientId)
                .gte('scheduled_time', todayStart.toISOString())
                .lte('scheduled_time', todayEnd.toISOString());
            if (todayRecords) {
                const allTaken = todayRecords.every(record => record.status === 'taken');
                if (allTaken && todayRecords.length > 1) {
                    bonusPoints = dto_1.POINTS_CONFIG.PERFECT_DAY_BONUS;
                    reason += ' + Perfect day bonus';
                }
            }
        }
        const totalPoints = Math.round((basePoints * multiplier) + bonusPoints);
        return {
            base_points: basePoints,
            bonus_points: bonusPoints,
            total_points: totalPoints,
            reason,
            multiplier: multiplier !== 1 ? multiplier : undefined,
        };
    }
    async updateStatsAfterAdherence(patientId, adherenceStatus, scheduledTime, takenTime) {
        const pointsCalc = await this.calculatePointsForAdherence(patientId, adherenceStatus, scheduledTime, takenTime);
        if (pointsCalc.total_points > 0) {
            const supabase = this.supabaseService.getClient();
            const currentStats = await this.getStats(patientId, { role: 'admin' });
            const newTotalPoints = currentStats.total_points + pointsCalc.total_points;
            const { completion_rate, current_streak, longest_streak } = await this.recalculateStats(patientId);
            await supabase
                .from('gamification_stats')
                .update({
                total_points: newTotalPoints,
                completion_rate,
                current_streak,
                longest_streak: Math.max(longest_streak, currentStats.longest_streak),
                updated_at: new Date().toISOString(),
            })
                .eq('patient_id', patientId);
        }
    }
    async recalculateStats(patientId) {
        const supabase = this.supabaseService.getClient();
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const { data: records } = await supabase
            .from('adherence_records')
            .select('status, scheduled_time')
            .eq('patient_id', patientId)
            .gte('scheduled_time', thirtyDaysAgo.toISOString())
            .order('scheduled_time', { ascending: true });
        if (!records || records.length === 0) {
            return { completion_rate: 0, current_streak: 0, longest_streak: 0 };
        }
        const totalRecords = records.length;
        const takenRecords = records.filter(r => r.status === 'taken').length;
        const completion_rate = (takenRecords / totalRecords) * 100;
        const dailyRecords = new Map();
        records.forEach(record => {
            const date = new Date(record.scheduled_time).toDateString();
            if (!dailyRecords.has(date)) {
                dailyRecords.set(date, []);
            }
            dailyRecords.get(date).push(record);
        });
        const dailyRates = [];
        for (const [date, dayRecords] of dailyRecords) {
            const taken = dayRecords.filter(r => r.status === 'taken').length;
            const total = dayRecords.length;
            const rate = (taken / total) * 100;
            dailyRates.push({ date, rate });
        }
        dailyRates.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        let current_streak = 0;
        let longest_streak = 0;
        let tempStreak = 0;
        for (let i = dailyRates.length - 1; i >= 0; i--) {
            if (dailyRates[i].rate >= 80) {
                tempStreak++;
                if (i === dailyRates.length - 1) {
                    current_streak = tempStreak;
                }
            }
            else {
                if (i === dailyRates.length - 1) {
                    current_streak = 0;
                }
                longest_streak = Math.max(longest_streak, tempStreak);
                tempStreak = 0;
            }
        }
        longest_streak = Math.max(longest_streak, tempStreak);
        return {
            completion_rate: Math.round(completion_rate * 100) / 100,
            current_streak,
            longest_streak,
        };
    }
    async getLeaderboard(query, currentUser) {
        const supabase = this.supabaseService.getClient();
        let orderBy = 'total_points';
        if (query.type === 'streak') {
            orderBy = 'current_streak';
        }
        else if (query.type === 'completion_rate') {
            orderBy = 'completion_rate';
        }
        const { data: stats, error } = await supabase
            .from('gamification_stats')
            .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name))
      `)
            .order(orderBy, { ascending: false })
            .limit(query.limit || 10);
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch leaderboard: ${error.message}`);
        }
        return (stats || []).map((stat, index) => ({
            patient_id: stat.patient_id,
            patient_name: stat.patient?.users?.name || 'Unknown Patient',
            rank: index + 1,
            points: stat.total_points,
            current_streak: stat.current_streak,
            completion_rate: stat.completion_rate,
            level: this.calculateLevel(stat.total_points),
        }));
    }
    calculateLevel(points) {
        for (let i = dto_1.GAMIFICATION_LEVELS.length - 1; i >= 0; i--) {
            if (points >= dto_1.GAMIFICATION_LEVELS[i].min_points) {
                return dto_1.GAMIFICATION_LEVELS[i];
            }
        }
        return dto_1.GAMIFICATION_LEVELS[0];
    }
    async getDashboard(patientId, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== patientId) {
            throw new common_1.ForbiddenException('Patients can only view their own dashboard');
        }
        if (currentUser.role === 'doctor') {
            const supabase = this.supabaseService.getClient();
            const { data: patient } = await supabase
                .from('patients')
                .select('assigned_doctor_id')
                .eq('id', patientId)
                .single();
            if (!patient || patient.assigned_doctor_id !== currentUser.id) {
                throw new common_1.ForbiddenException('You can only view dashboard for your assigned patients');
            }
        }
        const supabase = this.supabaseService.getClient();
        const stats = await this.getStats(patientId, currentUser);
        const currentLevel = this.calculateLevel(stats.total_points);
        const nextLevel = dto_1.GAMIFICATION_LEVELS.find(level => level.min_points > stats.total_points);
        const pointsToNextLevel = nextLevel ? nextLevel.min_points - stats.total_points : 0;
        const { data: recentAchievements } = await supabase
            .from('user_achievements')
            .select(`
        achievement_id,
        unlocked_at,
        achievement:achievements(name, points)
      `)
            .eq('user_id', patientId)
            .order('unlocked_at', { ascending: false })
            .limit(5);
        const weeklyProgress = await this.calculateWeeklyProgress(patientId);
        const monthlyProgress = await this.calculateMonthlyProgress(patientId);
        const nextMilestones = this.calculateNextMilestones(stats);
        return {
            patient_id: patientId,
            current_stats: {
                total_points: stats.total_points,
                current_streak: stats.current_streak,
                longest_streak: stats.longest_streak,
                completion_rate: stats.completion_rate,
                level: currentLevel,
                points_to_next_level: pointsToNextLevel,
            },
            recent_achievements: (recentAchievements || []).map(achievement => ({
                achievement_id: achievement.achievement_id,
                achievement_name: achievement.achievement?.name || 'Unknown Achievement',
                points_earned: achievement.achievement?.points || 0,
                unlocked_at: new Date(achievement.unlocked_at),
            })),
            weekly_progress: weeklyProgress,
            monthly_progress: monthlyProgress,
            next_milestones: nextMilestones,
        };
    }
    async calculateWeeklyProgress(patientId) {
        const supabase = this.supabaseService.getClient();
        const now = new Date();
        const currentWeekStart = new Date(now);
        currentWeekStart.setDate(now.getDate() - now.getDay());
        currentWeekStart.setHours(0, 0, 0, 0);
        const previousWeekStart = new Date(currentWeekStart);
        previousWeekStart.setDate(currentWeekStart.getDate() - 7);
        const previousWeekEnd = new Date(currentWeekStart);
        previousWeekEnd.setTime(currentWeekStart.getTime() - 1);
        const { data: currentWeekRecords } = await supabase
            .from('adherence_records')
            .select('status, scheduled_time')
            .eq('patient_id', patientId)
            .gte('scheduled_time', currentWeekStart.toISOString())
            .lte('scheduled_time', now.toISOString());
        const { data: previousWeekRecords } = await supabase
            .from('adherence_records')
            .select('status, scheduled_time')
            .eq('patient_id', patientId)
            .gte('scheduled_time', previousWeekStart.toISOString())
            .lte('scheduled_time', previousWeekEnd.toISOString());
        const currentWeek = this.calculateDailyRates(currentWeekRecords || [], currentWeekStart, 7);
        const previousWeek = this.calculateDailyRates(previousWeekRecords || [], previousWeekStart, 7);
        const currentAvg = currentWeek.reduce((sum, rate) => sum + rate, 0) / currentWeek.length;
        const previousAvg = previousWeek.reduce((sum, rate) => sum + rate, 0) / previousWeek.length;
        const improvement = currentAvg - previousAvg;
        return {
            current_week: currentWeek,
            previous_week: previousWeek,
            improvement: Math.round(improvement * 100) / 100,
        };
    }
    async calculateMonthlyProgress(patientId) {
        const supabase = this.supabaseService.getClient();
        const now = new Date();
        const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const previousMonthEnd = new Date(currentMonthStart);
        previousMonthEnd.setTime(currentMonthStart.getTime() - 1);
        const { data: currentMonthRecords } = await supabase
            .from('adherence_records')
            .select('status, scheduled_time')
            .eq('patient_id', patientId)
            .gte('scheduled_time', currentMonthStart.toISOString())
            .lte('scheduled_time', now.toISOString());
        const { data: previousMonthRecords } = await supabase
            .from('adherence_records')
            .select('status, scheduled_time')
            .eq('patient_id', patientId)
            .gte('scheduled_time', previousMonthStart.toISOString())
            .lte('scheduled_time', previousMonthEnd.toISOString());
        const daysInCurrentMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0).getDate();
        const daysInPreviousMonth = new Date(now.getFullYear(), now.getMonth(), 0).getDate();
        const currentMonth = this.calculateDailyRates(currentMonthRecords || [], currentMonthStart, daysInCurrentMonth);
        const previousMonth = this.calculateDailyRates(previousMonthRecords || [], previousMonthStart, daysInPreviousMonth);
        const currentAvg = currentMonth.reduce((sum, rate) => sum + rate, 0) / currentMonth.length;
        const previousAvg = previousMonth.reduce((sum, rate) => sum + rate, 0) / previousMonth.length;
        const improvement = currentAvg - previousAvg;
        return {
            current_month: currentMonth,
            previous_month: previousMonth,
            improvement: Math.round(improvement * 100) / 100,
        };
    }
    calculateDailyRates(records, startDate, days) {
        const dailyRates = [];
        for (let i = 0; i < days; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            const dateStr = date.toDateString();
            const dayRecords = records.filter(record => new Date(record.scheduled_time).toDateString() === dateStr);
            if (dayRecords.length === 0) {
                dailyRates.push(0);
            }
            else {
                const taken = dayRecords.filter(r => r.status === 'taken').length;
                const rate = (taken / dayRecords.length) * 100;
                dailyRates.push(Math.round(rate * 100) / 100);
            }
        }
        return dailyRates;
    }
    calculateNextMilestones(stats) {
        const milestones = [];
        const streakTargets = [7, 14, 30, 60, 90, 180, 365];
        const nextStreakTarget = streakTargets.find(target => target > stats.current_streak);
        if (nextStreakTarget) {
            milestones.push({
                type: 'streak',
                target: nextStreakTarget,
                current: stats.current_streak,
                progress_percentage: (stats.current_streak / nextStreakTarget) * 100,
                estimated_days: nextStreakTarget - stats.current_streak,
            });
        }
        const pointsTargets = [100, 300, 600, 1000, 1500, 2500, 5000, 10000];
        const nextPointsTarget = pointsTargets.find(target => target > stats.total_points);
        if (nextPointsTarget) {
            const pointsNeeded = nextPointsTarget - stats.total_points;
            const estimatedDays = Math.ceil(pointsNeeded / 10);
            milestones.push({
                type: 'points',
                target: nextPointsTarget,
                current: stats.total_points,
                progress_percentage: (stats.total_points / nextPointsTarget) * 100,
                estimated_days: estimatedDays,
            });
        }
        const completionTargets = [80, 85, 90, 95, 98, 100];
        const nextCompletionTarget = completionTargets.find(target => target > stats.completion_rate);
        if (nextCompletionTarget) {
            milestones.push({
                type: 'completion',
                target: nextCompletionTarget,
                current: stats.completion_rate,
                progress_percentage: (stats.completion_rate / nextCompletionTarget) * 100,
                estimated_days: 30,
            });
        }
        return milestones.slice(0, 3);
    }
};
exports.GamificationService = GamificationService;
exports.GamificationService = GamificationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], GamificationService);
//# sourceMappingURL=gamification.service.js.map