"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HospitalDepartmentAnalysisQueryDto = exports.HospitalBulkAdherenceQueryDto = exports.HospitalAdherenceReportQueryDto = exports.HospitalPatientsQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class HospitalPatientsQueryDto {
    page = 1;
    limit = 10;
    search;
    doctor_id;
    specialization;
}
exports.HospitalPatientsQueryDto = HospitalPatientsQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 1, description: 'Page number' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], HospitalPatientsQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 10, description: 'Items per page' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], HospitalPatientsQueryDto.prototype, "limit", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'John', description: 'Search by patient name' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HospitalPatientsQueryDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'doctor_id', description: 'Filter by assigned doctor' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HospitalPatientsQueryDto.prototype, "doctor_id", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'General Medicine', description: 'Filter by doctor specialization' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HospitalPatientsQueryDto.prototype, "specialization", void 0);
class HospitalAdherenceReportQueryDto {
    days = 30;
    include_medications = false;
    include_gamification = false;
}
exports.HospitalAdherenceReportQueryDto = HospitalAdherenceReportQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 30, description: 'Number of days to analyze' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], HospitalAdherenceReportQueryDto.prototype, "days", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: true, description: 'Include detailed medication breakdown' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true'),
    __metadata("design:type", Boolean)
], HospitalAdherenceReportQueryDto.prototype, "include_medications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: true, description: 'Include gamification data' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true'),
    __metadata("design:type", Boolean)
], HospitalAdherenceReportQueryDto.prototype, "include_gamification", void 0);
class HospitalBulkAdherenceQueryDto {
    patient_ids;
    days = 30;
    include_details = false;
}
exports.HospitalBulkAdherenceQueryDto = HospitalBulkAdherenceQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: ['patient1', 'patient2'], description: 'Array of patient IDs' }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], HospitalBulkAdherenceQueryDto.prototype, "patient_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 30, description: 'Number of days to analyze' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], HospitalBulkAdherenceQueryDto.prototype, "days", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: true, description: 'Include detailed breakdown for each patient' }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], HospitalBulkAdherenceQueryDto.prototype, "include_details", void 0);
class HospitalDepartmentAnalysisQueryDto {
    days = 30;
    department;
    min_adherence_rate = 0;
    risk_level;
}
exports.HospitalDepartmentAnalysisQueryDto = HospitalDepartmentAnalysisQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 30, description: 'Number of days to analyze' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], HospitalDepartmentAnalysisQueryDto.prototype, "days", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'General Medicine', description: 'Filter by department/specialization' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], HospitalDepartmentAnalysisQueryDto.prototype, "department", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 70, description: 'Minimum adherence rate threshold' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseFloat(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], HospitalDepartmentAnalysisQueryDto.prototype, "min_adherence_rate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ example: 'low', description: 'Risk level filter' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['low', 'medium', 'high', 'critical']),
    __metadata("design:type", String)
], HospitalDepartmentAnalysisQueryDto.prototype, "risk_level", void 0);
//# sourceMappingURL=index.js.map