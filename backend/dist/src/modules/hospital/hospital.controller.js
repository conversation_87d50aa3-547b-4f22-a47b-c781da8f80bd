"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HospitalController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const hospital_service_1 = require("./hospital.service");
const dto_1 = require("./dto");
let HospitalController = class HospitalController {
    hospitalService;
    constructor(hospitalService) {
        this.hospitalService = hospitalService;
    }
    async getHospitalPatients(query, req) {
        return this.hospitalService.getHospitalPatients(query, req.user);
    }
    async getPatientAdherenceReport(patientId, query, req) {
        return this.hospitalService.getPatientAdherenceReport(patientId, query, req.user);
    }
    async getBulkAdherenceReport(query, req) {
        return this.hospitalService.getBulkAdherenceReport(query, req.user);
    }
    async getDepartmentAnalysis(query, req) {
        return this.hospitalService.getDepartmentAnalysis(query, req.user);
    }
    async getDashboardSummary(req) {
        return this.hospitalService.getDashboardSummary(req.user);
    }
    async getPatientSummary(patientId, req) {
        const report = await this.hospitalService.getPatientAdherenceReport(patientId, { days: 7, include_medications: false, include_gamification: false }, req.user);
        return {
            patient_id: report.patient_id,
            patient_name: report.patient_name,
            hospital_id: report.hospital_id,
            assigned_doctor: report.assigned_doctor,
            current_adherence_rate: report.overall_adherence.adherence_rate,
            current_streak: report.overall_adherence.current_streak,
            risk_level: report.risk_assessment.risk_level,
            active_medications: report.overall_adherence.total_doses > 0 ? 1 : 0,
            last_dose_taken: new Date(),
            next_scheduled_dose: new Date(Date.now() + 24 * 60 * 60 * 1000),
            recent_alerts: report.risk_assessment.risk_factors,
        };
    }
};
exports.HospitalController = HospitalController;
__decorate([
    (0, common_1.Get)('patients'),
    (0, roles_decorator_1.Roles)('hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all patients associated with the hospital',
        description: 'Retrieve a paginated list of patients connected to the hospital through assigned doctors',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'List of hospital patients retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            patient_id: { type: 'string' },
                            patient_name: { type: 'string' },
                            patient_email: { type: 'string' },
                            date_of_birth: { type: 'string', format: 'date' },
                            emergency_contact: { type: 'string' },
                            assigned_doctor: {
                                type: 'object',
                                properties: {
                                    id: { type: 'string' },
                                    name: { type: 'string' },
                                    specialization: { type: 'string' },
                                },
                            },
                            current_medications_count: { type: 'number' },
                            last_adherence_check: { type: 'string', format: 'date-time' },
                        },
                    },
                },
                total: { type: 'number' },
                page: { type: 'number' },
                limit: { type: 'number' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - Hospital role required' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.HospitalPatientsQueryDto, Object]),
    __metadata("design:returntype", Promise)
], HospitalController.prototype, "getHospitalPatients", null);
__decorate([
    (0, common_1.Get)('adherence-report/:patient_id'),
    (0, roles_decorator_1.Roles)('hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get detailed adherence report for a specific patient',
        description: 'Retrieve comprehensive medication adherence analysis for a patient under hospital care',
    }),
    (0, swagger_1.ApiParam)({
        name: 'patient_id',
        description: 'ID of the patient to get adherence report for',
        type: 'string',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Patient adherence report retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - Hospital does not have access to this patient' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.HospitalAdherenceReportQueryDto, Object]),
    __metadata("design:returntype", Promise)
], HospitalController.prototype, "getPatientAdherenceReport", null);
__decorate([
    (0, common_1.Post)('bulk-adherence-report'),
    (0, roles_decorator_1.Roles)('hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get bulk adherence report for multiple patients',
        description: 'Retrieve adherence analysis for multiple patients with summary statistics and department breakdown',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Bulk adherence report generated successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - Hospital role required' }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.HospitalBulkAdherenceQueryDto, Object]),
    __metadata("design:returntype", Promise)
], HospitalController.prototype, "getBulkAdherenceReport", null);
__decorate([
    (0, common_1.Get)('department-analysis'),
    (0, roles_decorator_1.Roles)('hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get department-wise adherence analysis',
        description: 'Analyze medication adherence performance across different hospital departments and specializations',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Department analysis completed successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - Hospital role required' }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.HospitalDepartmentAnalysisQueryDto, Object]),
    __metadata("design:returntype", Promise)
], HospitalController.prototype, "getDepartmentAnalysis", null);
__decorate([
    (0, common_1.Get)('dashboard-summary'),
    (0, roles_decorator_1.Roles)('hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get hospital dashboard summary',
        description: 'Retrieve key metrics and highlights for hospital administration dashboard',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Dashboard summary retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - Hospital role required' }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], HospitalController.prototype, "getDashboardSummary", null);
__decorate([
    (0, common_1.Get)('patient/:patient_id/summary'),
    (0, roles_decorator_1.Roles)('hospital', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get quick patient summary',
        description: 'Retrieve essential patient information and recent adherence status for quick reference',
    }),
    (0, swagger_1.ApiParam)({
        name: 'patient_id',
        description: 'ID of the patient to get summary for',
        type: 'string',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Patient summary retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                patient_id: { type: 'string' },
                patient_name: { type: 'string' },
                hospital_id: { type: 'string' },
                assigned_doctor: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        name: { type: 'string' },
                        specialization: { type: 'string' },
                    },
                },
                current_adherence_rate: { type: 'number' },
                current_streak: { type: 'number' },
                risk_level: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
                active_medications: { type: 'number' },
                last_dose_taken: { type: 'string', format: 'date-time' },
                next_scheduled_dose: { type: 'string', format: 'date-time' },
                recent_alerts: {
                    type: 'array',
                    items: { type: 'string' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Access denied - Hospital does not have access to this patient' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], HospitalController.prototype, "getPatientSummary", null);
exports.HospitalController = HospitalController = __decorate([
    (0, swagger_1.ApiTags)('hospital'),
    (0, common_1.Controller)('hospital'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [hospital_service_1.HospitalService])
], HospitalController);
//# sourceMappingURL=hospital.controller.js.map