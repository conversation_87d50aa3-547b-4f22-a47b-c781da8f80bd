import { SupabaseService } from '../../config/supabase.service';
import { AdherenceService } from '../adherence/adherence.service';
import { GamificationService } from '../gamification/gamification.service';
import { User } from '../../common/types';
import { HospitalPatientsQueryDto, HospitalAdherenceReportQueryDto, HospitalBulkAdherenceQueryDto, HospitalDepartmentAnalysisQueryDto, HospitalPatient, HospitalAdherenceReport, HospitalBulkAdherenceReport, HospitalDepartmentAnalysis, HospitalDashboardSummary } from './dto';
export declare class HospitalService {
    private readonly supabaseService;
    private readonly adherenceService;
    private readonly gamificationService;
    constructor(supabaseService: SupabaseService, adherenceService: AdherenceService, gamificationService: GamificationService);
    getHospitalPatients(query: HospitalPatientsQueryDto, currentUser: User): Promise<{
        data: HospitalPatient[];
        total: number;
        page: number;
        limit: number;
    }>;
    getPatientAdherenceReport(patientId: string, query: HospitalAdherenceReportQueryDto, currentUser: User): Promise<HospitalAdherenceReport>;
    private validateHospitalUser;
    private validatePatientAccess;
    private calculateWeeklyTrends;
    private generateRecommendations;
    private assessPatientRisk;
    getBulkAdherenceReport(query: HospitalBulkAdherenceQueryDto, currentUser: User): Promise<HospitalBulkAdherenceReport>;
    getDepartmentAnalysis(query: HospitalDepartmentAnalysisQueryDto, currentUser: User): Promise<HospitalDepartmentAnalysis>;
    private calculateLevel;
    getDashboardSummary(currentUser: User): Promise<HospitalDashboardSummary>;
    private calculateRiskLevel;
}
