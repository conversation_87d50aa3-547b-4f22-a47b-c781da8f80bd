export declare class PolicyHolderQueryDto {
    search?: string;
    coverage_area?: string;
    policy_type?: string;
    page?: number;
    limit?: number;
}
export declare class AdherenceReportQueryDto {
    days?: number;
    start_date?: string;
    end_date?: string;
    include_medicine_details?: boolean;
    include_gamification?: boolean;
}
export declare class BulkAdherenceQueryDto {
    patient_ids: string[];
    days?: number;
    include_details?: boolean;
}
export declare class RiskAssessmentQueryDto {
    risk_level?: 'low' | 'medium' | 'high' | 'critical';
    min_adherence_rate?: number;
    max_adherence_rate?: number;
    days?: number;
}
export interface PolicyHolder {
    patient_id: string;
    patient_name: string;
    patient_email: string;
    date_of_birth?: Date;
    emergency_contact?: string;
    assigned_doctor?: {
        id: string;
        name: string;
        specialization: string;
    };
    enrollment_date: Date;
    policy_type: string;
    coverage_area: string;
    active_medications_count: number;
    last_adherence_update: Date;
}
export interface InsuranceAdherenceReport {
    patient_id: string;
    patient_name: string;
    report_period: {
        start_date: Date;
        end_date: Date;
        days_analyzed: number;
    };
    overall_adherence: {
        total_scheduled: number;
        total_taken: number;
        total_missed: number;
        total_skipped: number;
        adherence_rate: number;
        on_time_rate: number;
        late_rate: number;
    };
    risk_assessment: {
        level: 'low' | 'medium' | 'high' | 'critical';
        score: number;
        factors: string[];
        recommendations: string[];
    };
    medication_details?: {
        medicine_id: string;
        medicine_name: string;
        dosage: string;
        frequency: string;
        adherence_rate: number;
        missed_doses: number;
        last_taken?: Date;
    }[];
    gamification_data?: {
        total_points: number;
        current_streak: number;
        longest_streak: number;
        completion_rate: number;
        level: string;
    };
    trends: {
        weekly_adherence: number[];
        improvement_trend: 'improving' | 'declining' | 'stable';
        consistency_score: number;
    };
}
export interface BulkAdherenceReport {
    insurance_provider_id: string;
    report_generated_at: Date;
    total_patients: number;
    summary: {
        average_adherence_rate: number;
        high_risk_patients: number;
        medium_risk_patients: number;
        low_risk_patients: number;
        total_medications_tracked: number;
    };
    patient_summaries: {
        patient_id: string;
        patient_name: string;
        adherence_rate: number;
        risk_level: 'low' | 'medium' | 'high' | 'critical';
        active_medications: number;
        last_update: Date;
    }[];
    detailed_reports?: InsuranceAdherenceReport[];
}
export interface RiskAssessmentReport {
    insurance_provider_id: string;
    assessment_date: Date;
    criteria: {
        min_adherence_rate: number;
        max_adherence_rate: number;
        analysis_period_days: number;
        risk_level_filter?: string;
    };
    high_risk_patients: {
        patient_id: string;
        patient_name: string;
        adherence_rate: number;
        risk_score: number;
        risk_factors: string[];
        recommended_actions: string[];
        contact_priority: 'immediate' | 'urgent' | 'routine';
    }[];
    statistics: {
        total_patients_assessed: number;
        critical_risk_count: number;
        high_risk_count: number;
        medium_risk_count: number;
        low_risk_count: number;
        average_adherence_rate: number;
        patients_needing_intervention: number;
    };
}
