"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RiskAssessmentQueryDto = exports.BulkAdherenceQueryDto = exports.AdherenceReportQueryDto = exports.PolicyHolderQueryDto = void 0;
const swagger_1 = require("@nestjs/swagger");
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
class PolicyHolderQueryDto {
    search;
    coverage_area;
    policy_type;
    page = 1;
    limit = 20;
}
exports.PolicyHolderQueryDto = PolicyHolderQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Search by patient name or email' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyHolderQueryDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by coverage area' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyHolderQueryDto.prototype, "coverage_area", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Filter by policy type' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], PolicyHolderQueryDto.prototype, "policy_type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Page number for pagination', default: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], PolicyHolderQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of items per page', default: 20 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], PolicyHolderQueryDto.prototype, "limit", void 0);
class AdherenceReportQueryDto {
    days = 30;
    start_date;
    end_date;
    include_medicine_details = true;
    include_gamification = false;
}
exports.AdherenceReportQueryDto = AdherenceReportQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of days to analyze', default: 30 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], AdherenceReportQueryDto.prototype, "days", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Start date for analysis (ISO string)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AdherenceReportQueryDto.prototype, "start_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'End date for analysis (ISO string)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], AdherenceReportQueryDto.prototype, "end_date", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Include detailed medicine breakdown', default: true }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdherenceReportQueryDto.prototype, "include_medicine_details", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Include gamification data', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], AdherenceReportQueryDto.prototype, "include_gamification", void 0);
class BulkAdherenceQueryDto {
    patient_ids;
    days = 30;
    include_details = false;
}
exports.BulkAdherenceQueryDto = BulkAdherenceQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Array of patient IDs to analyze', type: [String] }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.IsString)({ each: true }),
    __metadata("design:type", Array)
], BulkAdherenceQueryDto.prototype, "patient_ids", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of days to analyze', default: 30 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], BulkAdherenceQueryDto.prototype, "days", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Include detailed breakdown', default: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => value === 'true' || value === true),
    __metadata("design:type", Boolean)
], BulkAdherenceQueryDto.prototype, "include_details", void 0);
class RiskAssessmentQueryDto {
    risk_level;
    min_adherence_rate = 0;
    max_adherence_rate = 100;
    days = 30;
}
exports.RiskAssessmentQueryDto = RiskAssessmentQueryDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Risk level filter',
        enum: ['low', 'medium', 'high', 'critical'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['low', 'medium', 'high', 'critical']),
    __metadata("design:type", String)
], RiskAssessmentQueryDto.prototype, "risk_level", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Minimum adherence rate threshold (0-100)', default: 0 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseFloat(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], RiskAssessmentQueryDto.prototype, "min_adherence_rate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Maximum adherence rate threshold (0-100)', default: 100 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseFloat(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], RiskAssessmentQueryDto.prototype, "max_adherence_rate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Number of days to analyze', default: 30 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_transformer_1.Transform)(({ value }) => parseInt(value)),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(365),
    __metadata("design:type", Number)
], RiskAssessmentQueryDto.prototype, "days", void 0);
//# sourceMappingURL=index.js.map