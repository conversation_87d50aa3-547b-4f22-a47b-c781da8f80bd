import { InsuranceService } from './insurance.service';
import { PolicyHolderQueryDto, AdherenceReportQueryDto, BulkAdherenceQueryDto, RiskAssessmentQueryDto, PolicyHolder, InsuranceAdherenceReport, BulkAdherenceReport, RiskAssessmentReport } from './dto';
export declare class InsuranceController {
    private readonly insuranceService;
    constructor(insuranceService: InsuranceService);
    getPolicyHolders(query: PolicyHolderQueryDto, req: any): Promise<{
        data: PolicyHolder[];
        total: number;
        page: number;
        limit: number;
    }>;
    getPatientAdherenceReport(patientId: string, query: AdherenceReportQueryDto, req: any): Promise<InsuranceAdherenceReport>;
    getBulkAdherenceReport(query: BulkAdherenceQueryDto, req: any): Promise<BulkAdherenceReport>;
    getRiskAssessmentReport(query: RiskAssessmentQueryDto, req: any): Promise<RiskAssessmentReport>;
    getDashboardSummary(req: any): Promise<any>;
    getPolicyHolderSummary(patientId: string, req: any): Promise<any>;
}
