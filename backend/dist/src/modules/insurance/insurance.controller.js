"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.InsuranceController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const insurance_service_1 = require("./insurance.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
let InsuranceController = class InsuranceController {
    insuranceService;
    constructor(insuranceService) {
        this.insuranceService = insuranceService;
    }
    async getPolicyHolders(query, req) {
        return this.insuranceService.getPolicyHolders(query, req.user);
    }
    async getPatientAdherenceReport(patientId, query, req) {
        return this.insuranceService.getPatientAdherenceReport(patientId, query, req.user);
    }
    async getBulkAdherenceReport(query, req) {
        return this.insuranceService.getBulkAdherenceReport(query, req.user);
    }
    async getRiskAssessmentReport(query, req) {
        return this.insuranceService.getRiskAssessmentReport(query, req.user);
    }
    async getDashboardSummary(req) {
        const policyHolders = await this.insuranceService.getPolicyHolders({ page: 1, limit: 1000 }, req.user);
        const riskAssessment = await this.insuranceService.getRiskAssessmentReport({ days: 30 }, req.user);
        const totalPolicyHolders = policyHolders.total;
        const activeMedications = policyHolders.data.reduce((sum, holder) => sum + holder.active_medications_count, 0);
        const recentUpdates = policyHolders.data.filter(holder => {
            const daysSinceUpdate = Math.floor((Date.now() - holder.last_adherence_update.getTime()) / (1000 * 60 * 60 * 24));
            return daysSinceUpdate <= 7;
        }).length;
        return {
            overview: {
                total_policy_holders: totalPolicyHolders,
                active_medications_tracked: activeMedications,
                recent_adherence_updates: recentUpdates,
                average_adherence_rate: riskAssessment.statistics.average_adherence_rate,
            },
            risk_distribution: {
                critical_risk: riskAssessment.statistics.critical_risk_count,
                high_risk: riskAssessment.statistics.high_risk_count,
                medium_risk: riskAssessment.statistics.medium_risk_count,
                low_risk: riskAssessment.statistics.low_risk_count,
            },
            alerts: {
                patients_needing_immediate_attention: riskAssessment.high_risk_patients.filter(p => p.contact_priority === 'immediate').length,
                patients_needing_urgent_attention: riskAssessment.high_risk_patients.filter(p => p.contact_priority === 'urgent').length,
                total_intervention_needed: riskAssessment.statistics.patients_needing_intervention,
            },
            recent_high_risk_patients: riskAssessment.high_risk_patients.slice(0, 5).map(patient => ({
                patient_id: patient.patient_id,
                patient_name: patient.patient_name,
                adherence_rate: patient.adherence_rate,
                risk_level: patient.risk_score >= 60 ? 'critical' : 'high',
                primary_concern: patient.risk_factors[0] || 'Low adherence',
                contact_priority: patient.contact_priority,
            })),
        };
    }
    async getPolicyHolderSummary(patientId, req) {
        const adherenceReport = await this.insuranceService.getPatientAdherenceReport(patientId, { days: 30, include_medicine_details: true, include_gamification: false }, req.user);
        return {
            patient_info: {
                patient_id: adherenceReport.patient_id,
                patient_name: adherenceReport.patient_name,
                report_period: adherenceReport.report_period,
            },
            key_metrics: {
                adherence_rate: adherenceReport.overall_adherence.adherence_rate,
                risk_level: adherenceReport.risk_assessment.level,
                risk_score: adherenceReport.risk_assessment.score,
                active_medications: adherenceReport.medication_details?.length || 0,
                missed_doses_last_30_days: adherenceReport.overall_adherence.total_missed,
            },
            trends: {
                improvement_trend: adherenceReport.trends.improvement_trend,
                consistency_score: adherenceReport.trends.consistency_score,
                weekly_adherence: adherenceReport.trends.weekly_adherence,
            },
            alerts: {
                risk_factors: adherenceReport.risk_assessment.factors,
                recommendations: adherenceReport.risk_assessment.recommendations,
                requires_immediate_attention: adherenceReport.risk_assessment.level === 'critical',
            },
            medication_summary: adherenceReport.medication_details?.map(med => ({
                name: med.medicine_name,
                adherence_rate: med.adherence_rate,
                missed_doses: med.missed_doses,
                status: med.adherence_rate >= 80 ? 'good' : med.adherence_rate >= 60 ? 'concerning' : 'poor',
            })) || [],
        };
    }
};
exports.InsuranceController = InsuranceController;
__decorate([
    (0, common_1.Get)('policy-holders'),
    (0, roles_decorator_1.Roles)('insurance', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get policy holders',
        description: 'Retrieve list of patients covered by this insurance provider with basic information and adherence status'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Policy holders retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Only insurance providers can access this endpoint',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.PolicyHolderQueryDto, Object]),
    __metadata("design:returntype", Promise)
], InsuranceController.prototype, "getPolicyHolders", null);
__decorate([
    (0, common_1.Get)('adherence-report/:patient_id'),
    (0, roles_decorator_1.Roles)('insurance', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get patient adherence report',
        description: 'Retrieve detailed adherence report for a specific policy holder including risk assessment and trends'
    }),
    (0, swagger_1.ApiParam)({ name: 'patient_id', description: 'Patient ID for the adherence report' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Adherence report retrieved successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.NOT_FOUND,
        description: 'Patient not found or not covered by this insurance provider',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Access denied to this patient\'s data',
    }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Query)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.AdherenceReportQueryDto, Object]),
    __metadata("design:returntype", Promise)
], InsuranceController.prototype, "getPatientAdherenceReport", null);
__decorate([
    (0, common_1.Post)('bulk-adherence-report'),
    (0, roles_decorator_1.Roles)('insurance', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get bulk adherence report',
        description: 'Generate adherence reports for multiple policy holders with summary statistics'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Bulk adherence report generated successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid patient IDs or query parameters',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Access denied to one or more patients\' data',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BulkAdherenceQueryDto, Object]),
    __metadata("design:returntype", Promise)
], InsuranceController.prototype, "getBulkAdherenceReport", null);
__decorate([
    (0, common_1.Get)('risk-assessment'),
    (0, roles_decorator_1.Roles)('insurance', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get risk assessment report',
        description: 'Identify high-risk policy holders based on adherence patterns and provide intervention recommendations'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Risk assessment report generated successfully',
        type: Object,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.FORBIDDEN,
        description: 'Only insurance providers can access this endpoint',
    }),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.RiskAssessmentQueryDto, Object]),
    __metadata("design:returntype", Promise)
], InsuranceController.prototype, "getRiskAssessmentReport", null);
__decorate([
    (0, common_1.Get)('dashboard-summary'),
    (0, roles_decorator_1.Roles)('insurance', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get insurance provider dashboard summary',
        description: 'Get high-level statistics and key metrics for the insurance provider dashboard'
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Dashboard summary retrieved successfully',
        type: Object,
    }),
    __param(0, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], InsuranceController.prototype, "getDashboardSummary", null);
__decorate([
    (0, common_1.Get)('policy-holder/:patient_id/summary'),
    (0, roles_decorator_1.Roles)('insurance', 'admin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get policy holder summary',
        description: 'Get quick summary of a specific policy holder\'s adherence status and key metrics'
    }),
    (0, swagger_1.ApiParam)({ name: 'patient_id', description: 'Patient ID for the summary' }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Policy holder summary retrieved successfully',
        type: Object,
    }),
    __param(0, (0, common_1.Param)('patient_id')),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], InsuranceController.prototype, "getPolicyHolderSummary", null);
exports.InsuranceController = InsuranceController = __decorate([
    (0, swagger_1.ApiTags)('insurance'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('insurance'),
    __metadata("design:paramtypes", [insurance_service_1.InsuranceService])
], InsuranceController);
//# sourceMappingURL=insurance.controller.js.map