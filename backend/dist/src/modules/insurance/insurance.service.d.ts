import { SupabaseService } from '../../config/supabase.service';
import { AdherenceService } from '../adherence/adherence.service';
import { GamificationService } from '../gamification/gamification.service';
import { User } from '../../common/types';
import { PolicyHolderQueryDto, AdherenceReportQueryDto, BulkAdherenceQueryDto, RiskAssessmentQueryDto, PolicyHolder, InsuranceAdherenceReport, BulkAdherenceReport, RiskAssessmentReport } from './dto';
export declare class InsuranceService {
    private readonly supabaseService;
    private readonly adherenceService;
    private readonly gamificationService;
    constructor(supabaseService: SupabaseService, adherenceService: AdherenceService, gamificationService: GamificationService);
    private validateInsuranceProvider;
    getPolicyHolders(query: PolicyHolderQueryDto, currentUser: User): Promise<{
        data: PolicyHolder[];
        total: number;
        page: number;
        limit: number;
    }>;
    getPatientAdherenceReport(patientId: string, query: AdherenceReportQueryDto, currentUser: User): Promise<InsuranceAdherenceReport>;
    private validatePatientAccess;
    private calculateRiskAssessment;
    private calculateTrends;
    getBulkAdherenceReport(query: BulkAdherenceQueryDto, currentUser: User): Promise<BulkAdherenceReport>;
    getRiskAssessmentReport(query: RiskAssessmentQueryDto, currentUser: User): Promise<RiskAssessmentReport>;
}
