export declare class CreateMedicineDto {
    name: string;
    dosage: string;
    frequency: string;
    duration: number;
    instructions: string;
    side_effects?: string;
    start_date: string;
    end_date: string;
    prescription_id: string;
    patient_id: string;
}
export declare class UpdateMedicineDto {
    name?: string;
    dosage?: string;
    frequency?: string;
    duration?: number;
    instructions?: string;
    side_effects?: string;
    start_date?: string;
    end_date?: string;
}
export declare class MedicineQueryDto {
    patient_id?: string;
    prescription_id?: string;
    name?: string;
    status?: 'active' | 'completed' | 'all';
}
