"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MedicinesController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const medicines_service_1 = require("./medicines.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let MedicinesController = class MedicinesController {
    medicinesService;
    constructor(medicinesService) {
        this.medicinesService = medicinesService;
    }
    async create(createMedicineDto, currentUser) {
        return this.medicinesService.create(createMedicineDto, currentUser);
    }
    async findAll(query, currentUser) {
        return this.medicinesService.findAll(query, currentUser);
    }
    async findByPatient(patientId, currentUser) {
        return this.medicinesService.findByPatient(patientId, currentUser);
    }
    async findByPrescription(prescriptionId, currentUser) {
        return this.medicinesService.findByPrescription(prescriptionId, currentUser);
    }
    async findOne(id, currentUser) {
        return this.medicinesService.findOne(id, currentUser);
    }
    async update(id, updateMedicineDto, currentUser) {
        return this.medicinesService.update(id, updateMedicineDto, currentUser);
    }
    async remove(id, currentUser) {
        await this.medicinesService.remove(id, currentUser);
        return { message: 'Medicine deleted successfully' };
    }
};
exports.MedicinesController = MedicinesController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new medicine' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Medicine created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateMedicineDto, Object]),
    __metadata("design:returntype", Promise)
], MedicinesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all medicines' }),
    (0, swagger_1.ApiQuery)({ name: 'patient_id', required: false, description: 'Filter by patient ID' }),
    (0, swagger_1.ApiQuery)({ name: 'prescription_id', required: false, description: 'Filter by prescription ID' }),
    (0, swagger_1.ApiQuery)({ name: 'name', required: false, description: 'Filter by medicine name' }),
    (0, swagger_1.ApiQuery)({ name: 'status', required: false, enum: ['active', 'completed', 'all'], description: 'Filter by status' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Medicines retrieved successfully' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.MedicineQueryDto, Object]),
    __metadata("design:returntype", Promise)
], MedicinesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('patient/:patientId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get medicines for a specific patient' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Patient medicines retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('patientId', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MedicinesController.prototype, "findByPatient", null);
__decorate([
    (0, common_1.Get)('prescription/:prescriptionId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get medicines for a specific prescription' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Prescription medicines retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Prescription not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('prescriptionId', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MedicinesController.prototype, "findByPrescription", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get medicine by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Medicine retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Medicine not found' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'admin', 'insurance'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MedicinesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update medicine' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Medicine updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Medicine not found' }),
    (0, roles_decorator_1.Roles)('doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateMedicineDto, Object]),
    __metadata("design:returntype", Promise)
], MedicinesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete medicine' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Medicine deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Medicine not found' }),
    (0, roles_decorator_1.Roles)('doctor', 'hospital', 'admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], MedicinesController.prototype, "remove", null);
exports.MedicinesController = MedicinesController = __decorate([
    (0, swagger_1.ApiTags)('medicines'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('medicines'),
    __metadata("design:paramtypes", [medicines_service_1.MedicinesService])
], MedicinesController);
//# sourceMappingURL=medicines.controller.js.map