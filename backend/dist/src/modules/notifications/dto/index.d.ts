export declare enum NotificationType {
    REMINDER_DUE = "reminder:due",
    REMINDER_SENT = "reminder:sent",
    REMINDER_FAILED = "reminder:failed",
    ADHERENCE_UPDATED = "adherence:updated",
    ADHERENCE_CRITICAL = "adherence:critical",
    ACHIEVEMENT_UNLOCKED = "achievement:unlocked",
    POINTS_UPDATED = "points:updated",
    MEDICATION_ADDED = "medication:added",
    PATIENT_MISSED_MEDICATION = "patient:missed:medication",
    DASHBOARD_UPDATE = "dashboard:update",
    RISK_ASSESSMENT_CHANGE = "risk:assessment:change"
}
export declare enum NotificationRole {
    PATIENT = "patient",
    DOCTOR = "doctor",
    HOSPITAL = "hospital",
    INSURANCE = "insurance",
    ADMIN = "admin"
}
export interface BaseNotification {
    id: string;
    type: NotificationType;
    title: string;
    message: string;
    userId: string;
    userRole: NotificationRole;
    data?: any;
    timestamp: Date;
    read: boolean;
}
export interface ReminderDueNotification extends BaseNotification {
    type: NotificationType.REMINDER_DUE;
    data: {
        reminderId: string;
        medicineId: string;
        medicineName: string;
        dosage: string;
        scheduledTime: Date;
    };
}
export interface AdherenceUpdatedNotification extends BaseNotification {
    type: NotificationType.ADHERENCE_UPDATED;
    data: {
        medicineId: string;
        medicineName: string;
        status: 'taken' | 'missed' | 'skipped';
        timestamp: Date;
        streak: number;
        points: number;
    };
}
export interface AchievementUnlockedNotification extends BaseNotification {
    type: NotificationType.ACHIEVEMENT_UNLOCKED;
    data: {
        achievementId: string;
        achievementName: string;
        description: string;
        points: number;
        badge: string;
    };
}
export interface PointsUpdatedNotification extends BaseNotification {
    type: NotificationType.POINTS_UPDATED;
    data: {
        totalPoints: number;
        pointsEarned: number;
        currentStreak: number;
        level: number;
        reason: string;
    };
}
export interface PatientAdherenceCriticalNotification extends BaseNotification {
    type: NotificationType.ADHERENCE_CRITICAL;
    data: {
        patientId: string;
        patientName: string;
        medicineId: string;
        medicineName: string;
        adherenceRate: number;
        missedDoses: number;
        riskLevel: 'low' | 'medium' | 'high' | 'critical';
    };
}
export interface PatientMissedMedicationNotification extends BaseNotification {
    type: NotificationType.PATIENT_MISSED_MEDICATION;
    data: {
        patientId: string;
        patientName: string;
        medicineId: string;
        medicineName: string;
        scheduledTime: Date;
        missedTime: Date;
        consecutiveMissed: number;
    };
}
export interface DashboardUpdateNotification extends BaseNotification {
    type: NotificationType.DASHBOARD_UPDATE;
    data: {
        totalPatients: number;
        criticalPatients: number;
        adherenceRate: number;
        recentAlerts: number;
        updatedAt: Date;
    };
}
export interface RiskAssessmentChangeNotification extends BaseNotification {
    type: NotificationType.RISK_ASSESSMENT_CHANGE;
    data: {
        policyHolderId: string;
        policyHolderName: string;
        previousRiskLevel: string;
        newRiskLevel: string;
        adherenceRate: number;
        riskFactors: string[];
    };
}
export declare class SendNotificationDto {
    userId: string;
    userRole: NotificationRole;
    type: NotificationType;
    title: string;
    message: string;
    data?: any;
}
export declare class BroadcastNotificationDto {
    targetRole: NotificationRole;
    type: NotificationType;
    title: string;
    message: string;
    data?: any;
    filter?: {
        hospitalId?: string;
        insuranceId?: string;
        departmentId?: string;
    };
}
export declare class NotificationHistoryQueryDto {
    userId?: string;
    type?: NotificationType;
    read?: boolean;
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
}
export interface ConnectedUser {
    userId: string;
    userRole: NotificationRole;
    socketId: string;
    connectedAt: Date;
    lastActivity: Date;
}
export interface NotificationResponse {
    success: boolean;
    message: string;
    notificationId?: string;
    deliveredTo?: number;
}
export interface NotificationHistoryResponse {
    notifications: BaseNotification[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
