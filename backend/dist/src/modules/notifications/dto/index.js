"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationHistoryQueryDto = exports.BroadcastNotificationDto = exports.SendNotificationDto = exports.NotificationRole = exports.NotificationType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var NotificationType;
(function (NotificationType) {
    NotificationType["REMINDER_DUE"] = "reminder:due";
    NotificationType["REMINDER_SENT"] = "reminder:sent";
    NotificationType["REMINDER_FAILED"] = "reminder:failed";
    NotificationType["ADHERENCE_UPDATED"] = "adherence:updated";
    NotificationType["ADHERENCE_CRITICAL"] = "adherence:critical";
    NotificationType["ACHIEVEMENT_UNLOCKED"] = "achievement:unlocked";
    NotificationType["POINTS_UPDATED"] = "points:updated";
    NotificationType["MEDICATION_ADDED"] = "medication:added";
    NotificationType["PATIENT_MISSED_MEDICATION"] = "patient:missed:medication";
    NotificationType["DASHBOARD_UPDATE"] = "dashboard:update";
    NotificationType["RISK_ASSESSMENT_CHANGE"] = "risk:assessment:change";
})(NotificationType || (exports.NotificationType = NotificationType = {}));
var NotificationRole;
(function (NotificationRole) {
    NotificationRole["PATIENT"] = "patient";
    NotificationRole["DOCTOR"] = "doctor";
    NotificationRole["HOSPITAL"] = "hospital";
    NotificationRole["INSURANCE"] = "insurance";
    NotificationRole["ADMIN"] = "admin";
})(NotificationRole || (exports.NotificationRole = NotificationRole = {}));
class SendNotificationDto {
    userId;
    userRole;
    type;
    title;
    message;
    data;
}
exports.SendNotificationDto = SendNotificationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID to send notification to' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], SendNotificationDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User role', enum: NotificationRole }),
    (0, class_validator_1.IsEnum)(NotificationRole),
    __metadata("design:type", String)
], SendNotificationDto.prototype, "userRole", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Notification type', enum: NotificationType }),
    (0, class_validator_1.IsEnum)(NotificationType),
    __metadata("design:type", String)
], SendNotificationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Notification title' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SendNotificationDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Notification message' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SendNotificationDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Additional notification data', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], SendNotificationDto.prototype, "data", void 0);
class BroadcastNotificationDto {
    targetRole;
    type;
    title;
    message;
    data;
    filter;
}
exports.BroadcastNotificationDto = BroadcastNotificationDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Target user role', enum: NotificationRole }),
    (0, class_validator_1.IsEnum)(NotificationRole),
    __metadata("design:type", String)
], BroadcastNotificationDto.prototype, "targetRole", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Notification type', enum: NotificationType }),
    (0, class_validator_1.IsEnum)(NotificationType),
    __metadata("design:type", String)
], BroadcastNotificationDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Notification title' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BroadcastNotificationDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Notification message' }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BroadcastNotificationDto.prototype, "message", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Additional notification data', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], BroadcastNotificationDto.prototype, "data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Filter criteria', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], BroadcastNotificationDto.prototype, "filter", void 0);
class NotificationHistoryQueryDto {
    userId;
    type;
    read;
    startDate;
    endDate;
    page = 1;
    limit = 20;
}
exports.NotificationHistoryQueryDto = NotificationHistoryQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'User ID', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], NotificationHistoryQueryDto.prototype, "userId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Notification type', enum: NotificationType, required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(NotificationType),
    __metadata("design:type", String)
], NotificationHistoryQueryDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Read status', required: false }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Boolean)
], NotificationHistoryQueryDto.prototype, "read", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Start date', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], NotificationHistoryQueryDto.prototype, "startDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'End date', required: false }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDateString)(),
    __metadata("design:type", String)
], NotificationHistoryQueryDto.prototype, "endDate", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Page number', required: false, default: 1 }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], NotificationHistoryQueryDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ description: 'Items per page', required: false, default: 20 }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], NotificationHistoryQueryDto.prototype, "limit", void 0);
//# sourceMappingURL=index.js.map