import { NotificationsService } from './notifications.service';
import { NotificationsGateway } from './notifications.gateway';
import { SendNotificationDto, BroadcastNotificationDto, NotificationHistoryQueryDto } from './dto';
import { User } from '../../common/types';
export declare class NotificationsController {
    private readonly notificationsService;
    private readonly notificationsGateway;
    constructor(notificationsService: NotificationsService, notificationsGateway: NotificationsGateway);
    sendNotification(sendNotificationDto: SendNotificationDto, currentUser: User): Promise<import("./dto").NotificationResponse>;
    broadcastNotification(broadcastNotificationDto: BroadcastNotificationDto, currentUser: User): Promise<import("./dto").NotificationResponse>;
    getNotificationHistory(query: NotificationHistoryQueryDto, currentUser: User): Promise<import("./dto").NotificationHistoryResponse>;
    markNotificationAsRead(id: string, currentUser: User): Promise<{
        success: boolean;
        notificationId: string;
    }>;
    getConnectedUsers(currentUser: User): Promise<{
        total: number;
        users: unknown[];
        byRole: Record<string, number>;
    }>;
    getNotificationStats(currentUser: User): Promise<{
        connectedUsers: number;
        usersByRole: Record<string, number>;
        timestamp: Date;
    }>;
    testReminderNotification(data: {
        userId: string;
        medicineName: string;
    }, currentUser: User): Promise<{
        success: boolean;
        message: string;
    }>;
    testAchievementNotification(data: {
        userId: string;
        achievementName: string;
    }, currentUser: User): Promise<{
        success: boolean;
        message: string;
    }>;
    testCriticalAlert(data: {
        doctorId: string;
        patientName: string;
    }, currentUser: User): Promise<{
        success: boolean;
        message: string;
    }>;
    private groupUsersByRole;
}
