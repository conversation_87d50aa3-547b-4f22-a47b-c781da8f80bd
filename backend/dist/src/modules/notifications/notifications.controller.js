"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const notifications_service_1 = require("./notifications.service");
const notifications_gateway_1 = require("./notifications.gateway");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let NotificationsController = class NotificationsController {
    notificationsService;
    notificationsGateway;
    constructor(notificationsService, notificationsGateway) {
        this.notificationsService = notificationsService;
        this.notificationsGateway = notificationsGateway;
    }
    async sendNotification(sendNotificationDto, currentUser) {
        const result = await this.notificationsService.sendNotificationToUser(sendNotificationDto);
        if (result.success) {
            await this.notificationsGateway.server
                .to(`user:${sendNotificationDto.userId}`)
                .emit('notification', {
                id: result.notificationId,
                type: sendNotificationDto.type,
                title: sendNotificationDto.title,
                message: sendNotificationDto.message,
                data: sendNotificationDto.data,
                timestamp: new Date(),
            });
        }
        return result;
    }
    async broadcastNotification(broadcastNotificationDto, currentUser) {
        const result = await this.notificationsService.broadcastNotificationToRole(broadcastNotificationDto);
        if (result.success) {
            await this.notificationsGateway.server
                .to(`role:${broadcastNotificationDto.targetRole}`)
                .emit('notification', {
                type: broadcastNotificationDto.type,
                title: broadcastNotificationDto.title,
                message: broadcastNotificationDto.message,
                data: broadcastNotificationDto.data,
                timestamp: new Date(),
            });
        }
        return result;
    }
    async getNotificationHistory(query, currentUser) {
        if (currentUser.role !== 'admin' && query.userId && query.userId !== currentUser.id) {
            throw new Error('Access denied to other user notifications');
        }
        if (!query.userId && currentUser.role !== 'admin') {
            query.userId = currentUser.id;
        }
        return this.notificationsService.getNotificationHistory(query);
    }
    async markNotificationAsRead(id, currentUser) {
        const success = await this.notificationsService.markNotificationAsRead(id, currentUser.id);
        if (success) {
            await this.notificationsGateway.server
                .to(`user:${currentUser.id}`)
                .emit('notification-read', {
                notificationId: id,
                timestamp: new Date(),
            });
        }
        return { success, notificationId: id };
    }
    async getConnectedUsers(currentUser) {
        const connectedUsers = Array.from(this.notificationsService.connectedUsers.values());
        return {
            total: connectedUsers.length,
            users: connectedUsers,
            byRole: this.groupUsersByRole(connectedUsers),
        };
    }
    async getNotificationStats(currentUser) {
        const connectedUsers = Array.from(this.notificationsService.connectedUsers.values());
        return {
            connectedUsers: connectedUsers.length,
            usersByRole: this.groupUsersByRole(connectedUsers),
            timestamp: new Date(),
        };
    }
    async testReminderNotification(data, currentUser) {
        await this.notificationsGateway.emitReminderDue(data.userId, {
            medicineName: data.medicineName,
            dosage: '1 tablet',
            scheduledTime: new Date(),
        });
        return { success: true, message: 'Test reminder notification sent' };
    }
    async testAchievementNotification(data, currentUser) {
        await this.notificationsGateway.emitAchievementUnlocked(data.userId, {
            achievementName: data.achievementName,
            description: 'Test achievement',
            points: 100,
            badge: 'test-badge',
        });
        return { success: true, message: 'Test achievement notification sent' };
    }
    async testCriticalAlert(data, currentUser) {
        await this.notificationsGateway.emitCriticalAdherenceAlert(data.doctorId, {
            patientName: data.patientName,
            adherenceRate: 45,
            riskLevel: 'critical',
        });
        return { success: true, message: 'Test critical alert sent' };
    }
    groupUsersByRole(users) {
        return users.reduce((acc, user) => {
            acc[user.userRole] = (acc[user.userRole] || 0) + 1;
            return acc;
        }, {});
    }
};
exports.NotificationsController = NotificationsController;
__decorate([
    (0, common_1.Post)('send'),
    (0, swagger_1.ApiOperation)({ summary: 'Send notification to specific user' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Notification sent successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('admin', 'doctor', 'hospital'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.SendNotificationDto, Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "sendNotification", null);
__decorate([
    (0, common_1.Post)('broadcast'),
    (0, swagger_1.ApiOperation)({ summary: 'Broadcast notification to user role' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Broadcast notification sent successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('admin', 'hospital'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.BroadcastNotificationDto, Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "broadcastNotification", null);
__decorate([
    (0, common_1.Get)('history'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notification history' }),
    (0, swagger_1.ApiQuery)({ name: 'userId', required: false, description: 'Filter by user ID (admin only)' }),
    (0, swagger_1.ApiQuery)({ name: 'type', required: false, description: 'Filter by notification type' }),
    (0, swagger_1.ApiQuery)({ name: 'read', required: false, description: 'Filter by read status' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Start date filter' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'End date filter' }),
    (0, swagger_1.ApiQuery)({ name: 'page', required: false, description: 'Page number' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Items per page' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification history retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'insurance', 'admin'),
    __param(0, (0, common_1.Query)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.NotificationHistoryQueryDto, Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "getNotificationHistory", null);
__decorate([
    (0, common_1.Patch)(':id/read'),
    (0, swagger_1.ApiOperation)({ summary: 'Mark notification as read' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification marked as read successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Notification not found' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('patient', 'doctor', 'hospital', 'insurance', 'admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "markNotificationAsRead", null);
__decorate([
    (0, common_1.Get)('connected-users'),
    (0, swagger_1.ApiOperation)({ summary: 'Get connected users (admin only)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Connected users retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "getConnectedUsers", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get notification statistics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Notification statistics retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, roles_decorator_1.Roles)('admin', 'hospital', 'doctor'),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "getNotificationStats", null);
__decorate([
    (0, common_1.Post)('test/reminder'),
    (0, swagger_1.ApiOperation)({ summary: 'Test reminder notification (development only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Test reminder sent successfully' }),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "testReminderNotification", null);
__decorate([
    (0, common_1.Post)('test/achievement'),
    (0, swagger_1.ApiOperation)({ summary: 'Test achievement notification (development only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Test achievement sent successfully' }),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "testAchievementNotification", null);
__decorate([
    (0, common_1.Post)('test/critical-alert'),
    (0, swagger_1.ApiOperation)({ summary: 'Test critical adherence alert (development only)' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Test critical alert sent successfully' }),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, Object]),
    __metadata("design:returntype", Promise)
], NotificationsController.prototype, "testCriticalAlert", null);
exports.NotificationsController = NotificationsController = __decorate([
    (0, swagger_1.ApiTags)('notifications'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('notifications'),
    __metadata("design:paramtypes", [notifications_service_1.NotificationsService,
        notifications_gateway_1.NotificationsGateway])
], NotificationsController);
//# sourceMappingURL=notifications.controller.js.map