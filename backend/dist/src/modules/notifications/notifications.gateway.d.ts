import { OnGatewayConnection, OnGatewayDisconnect } from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { NotificationsService } from './notifications.service';
import { NotificationRole, SendNotificationDto, BroadcastNotificationDto, NotificationHistoryQueryDto } from './dto';
import { ArgumentsHost } from '@nestjs/common';
import { BaseWsExceptionFilter, WsException } from '@nestjs/websockets';
export declare class WebSocketExceptionFilter extends BaseWsExceptionFilter {
    catch(exception: WsException, host: ArgumentsHost): void;
}
export declare class NotificationsGateway implements OnGatewayConnection, OnGatewayDisconnect {
    private readonly notificationsService;
    server: Server;
    private readonly logger;
    constructor(notificationsService: NotificationsService);
    handleConnection(client: Socket): Promise<void>;
    handleDisconnect(client: Socket): void;
    handlePing(client: Socket): void;
    handleJoinRoom(client: Socket, data: {
        room: string;
    }): Promise<void>;
    handleLeaveRoom(client: Socket, data: {
        room: string;
    }): Promise<void>;
    handleSendNotification(client: Socket, data: SendNotificationDto): Promise<void>;
    handleBroadcastNotification(client: Socket, data: BroadcastNotificationDto): Promise<void>;
    handleGetNotificationHistory(client: Socket, query: NotificationHistoryQueryDto): Promise<void>;
    handleMarkNotificationRead(client: Socket, data: {
        notificationId: string;
    }): Promise<void>;
    emitReminderDue(userId: string, reminderData: any): Promise<void>;
    emitAdherenceUpdate(userId: string, adherenceData: any): Promise<void>;
    emitAchievementUnlocked(userId: string, achievementData: any): Promise<void>;
    emitCriticalAdherenceAlert(doctorId: string, patientData: any): Promise<void>;
    emitDashboardUpdate(targetRole: NotificationRole, dashboardData: any): Promise<void>;
    handleReminderDue(payload: any): Promise<void>;
    handleReminderSent(payload: any): Promise<void>;
    handleReminderFailed(payload: any): Promise<void>;
    handleAdherenceUpdated(payload: any): Promise<void>;
    handleAchievementUnlocked(payload: any): Promise<void>;
    handleCriticalAdherence(payload: any): Promise<void>;
    handleDashboardUpdate(payload: any): Promise<void>;
    private canJoinRoom;
}
