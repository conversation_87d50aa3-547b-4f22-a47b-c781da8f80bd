"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var NotificationsGateway_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsGateway = exports.WebSocketExceptionFilter = void 0;
const websockets_1 = require("@nestjs/websockets");
const common_1 = require("@nestjs/common");
const event_emitter_1 = require("@nestjs/event-emitter");
const socket_io_1 = require("socket.io");
const notifications_service_1 = require("./notifications.service");
const ws_auth_guard_1 = require("../../common/guards/ws-auth.guard");
const dto_1 = require("./dto");
const common_2 = require("@nestjs/common");
const websockets_2 = require("@nestjs/websockets");
let WebSocketExceptionFilter = class WebSocketExceptionFilter extends websockets_2.BaseWsExceptionFilter {
    catch(exception, host) {
        const client = host.switchToWs().getClient();
        const error = exception.getError();
        const details = error instanceof Object ? { ...error } : { message: error };
        client.emit('error', {
            id: client.id,
            rid: Date.now(),
            ...details,
        });
    }
};
exports.WebSocketExceptionFilter = WebSocketExceptionFilter;
exports.WebSocketExceptionFilter = WebSocketExceptionFilter = __decorate([
    (0, common_2.Catch)(websockets_2.WsException)
], WebSocketExceptionFilter);
let NotificationsGateway = NotificationsGateway_1 = class NotificationsGateway {
    notificationsService;
    server;
    logger = new common_1.Logger(NotificationsGateway_1.name);
    constructor(notificationsService) {
        this.notificationsService = notificationsService;
    }
    async handleConnection(client) {
        try {
            const user = client.data.user;
            if (!user) {
                client.disconnect();
                return;
            }
            this.notificationsService.addConnectedUser(user.id, user.role, client.id);
            await client.join(`user:${user.id}`);
            await client.join(`role:${user.role}`);
            if (user.role === 'patient') {
                await client.join('patients');
            }
            else if (user.role === 'doctor' || user.role === 'hospital') {
                await client.join('healthcare');
            }
            else if (user.role === 'insurance') {
                await client.join('insurance');
            }
            else if (user.role === 'admin') {
                await client.join('admin');
            }
            client.emit('connected', {
                message: 'Successfully connected to notifications',
                userId: user.id,
                role: user.role,
                timestamp: new Date(),
            });
            this.logger.log(`Client connected: ${user.id} (${user.role}) - Socket: ${client.id}`);
        }
        catch (error) {
            this.logger.error('Connection error:', error);
            client.disconnect();
        }
    }
    handleDisconnect(client) {
        this.notificationsService.removeConnectedUser(client.id);
        this.logger.log(`Client disconnected: ${client.id}`);
    }
    handlePing(client) {
        this.notificationsService.updateUserActivity(client.id);
        client.emit('pong', { timestamp: new Date() });
    }
    async handleJoinRoom(client, data) {
        const user = client.data.user;
        if (this.canJoinRoom(user, data.room)) {
            await client.join(data.room);
            client.emit('room-joined', { room: data.room, timestamp: new Date() });
            this.logger.log(`User ${user.id} joined room: ${data.room}`);
        }
        else {
            client.emit('error', { message: 'Access denied to room', room: data.room });
        }
    }
    async handleLeaveRoom(client, data) {
        await client.leave(data.room);
        client.emit('room-left', { room: data.room, timestamp: new Date() });
    }
    async handleSendNotification(client, data) {
        try {
            const result = await this.notificationsService.sendNotificationToUser(data);
            if (result.success) {
                this.server.to(`user:${data.userId}`).emit('notification', {
                    id: result.notificationId,
                    type: data.type,
                    title: data.title,
                    message: data.message,
                    data: data.data,
                    timestamp: new Date(),
                });
            }
            client.emit('notification-sent', result);
        }
        catch (error) {
            client.emit('error', { message: 'Failed to send notification', error: error.message });
        }
    }
    async handleBroadcastNotification(client, data) {
        try {
            const result = await this.notificationsService.broadcastNotificationToRole(data);
            if (result.success) {
                this.server.to(`role:${data.targetRole}`).emit('notification', {
                    type: data.type,
                    title: data.title,
                    message: data.message,
                    data: data.data,
                    timestamp: new Date(),
                });
            }
            client.emit('broadcast-sent', result);
        }
        catch (error) {
            client.emit('error', { message: 'Failed to broadcast notification', error: error.message });
        }
    }
    async handleGetNotificationHistory(client, query) {
        try {
            const user = client.data.user;
            if (user.role !== 'admin' && query.userId && query.userId !== user.id) {
                client.emit('error', { message: 'Access denied to other user notifications' });
                return;
            }
            if (!query.userId && user.role !== 'admin') {
                query.userId = user.id;
            }
            const history = await this.notificationsService.getNotificationHistory(query);
            client.emit('notification-history', history);
        }
        catch (error) {
            client.emit('error', { message: 'Failed to get notification history', error: error.message });
        }
    }
    async handleMarkNotificationRead(client, data) {
        try {
            const user = client.data.user;
            const success = await this.notificationsService.markNotificationAsRead(data.notificationId, user.id);
            client.emit('notification-marked-read', {
                success,
                notificationId: data.notificationId,
            });
        }
        catch (error) {
            client.emit('error', { message: 'Failed to mark notification as read', error: error.message });
        }
    }
    async emitReminderDue(userId, reminderData) {
        this.server.to(`user:${userId}`).emit('notification', {
            type: dto_1.NotificationType.REMINDER_DUE,
            title: 'Medication Reminder',
            message: `Time to take your ${reminderData.medicineName}`,
            data: reminderData,
            timestamp: new Date(),
        });
    }
    async emitAdherenceUpdate(userId, adherenceData) {
        this.server.to(`user:${userId}`).emit('notification', {
            type: dto_1.NotificationType.ADHERENCE_UPDATED,
            title: 'Adherence Updated',
            message: 'Your medication adherence has been updated',
            data: adherenceData,
            timestamp: new Date(),
        });
    }
    async emitAchievementUnlocked(userId, achievementData) {
        this.server.to(`user:${userId}`).emit('notification', {
            type: dto_1.NotificationType.ACHIEVEMENT_UNLOCKED,
            title: 'Achievement Unlocked!',
            message: `Congratulations! You've earned: ${achievementData.achievementName}`,
            data: achievementData,
            timestamp: new Date(),
        });
    }
    async emitCriticalAdherenceAlert(doctorId, patientData) {
        this.server.to(`user:${doctorId}`).emit('notification', {
            type: dto_1.NotificationType.ADHERENCE_CRITICAL,
            title: 'Critical Adherence Alert',
            message: `Patient ${patientData.patientName} has critical adherence issues`,
            data: patientData,
            timestamp: new Date(),
        });
        this.server.to('healthcare').emit('notification', {
            type: dto_1.NotificationType.ADHERENCE_CRITICAL,
            title: 'Critical Adherence Alert',
            message: `Patient ${patientData.patientName} has critical adherence issues`,
            data: patientData,
            timestamp: new Date(),
        });
    }
    async emitDashboardUpdate(targetRole, dashboardData) {
        this.server.to(`role:${targetRole}`).emit('dashboard-update', {
            type: dto_1.NotificationType.DASHBOARD_UPDATE,
            data: dashboardData,
            timestamp: new Date(),
        });
    }
    async handleReminderDue(payload) {
        await this.emitReminderDue(payload.userId, payload);
    }
    async handleReminderSent(payload) {
        this.server.to(`user:${payload.userId}`).emit('reminder-sent', {
            reminderId: payload.reminderId,
            medicineName: payload.medicineName,
            type: payload.type,
            timestamp: payload.timestamp,
        });
    }
    async handleReminderFailed(payload) {
        this.server.to(`user:${payload.userId}`).emit('reminder-failed', {
            reminderId: payload.reminderId,
            medicineName: payload.medicineName,
            type: payload.type,
            error: payload.error,
            timestamp: payload.timestamp,
        });
    }
    async handleAdherenceUpdated(payload) {
        await this.emitAdherenceUpdate(payload.userId, payload);
    }
    async handleAchievementUnlocked(payload) {
        await this.emitAchievementUnlocked(payload.userId, payload);
    }
    async handleCriticalAdherence(payload) {
        await this.emitCriticalAdherenceAlert(payload.doctorId, payload);
    }
    async handleDashboardUpdate(payload) {
        await this.emitDashboardUpdate(payload.targetRole, payload.data);
    }
    canJoinRoom(user, room) {
        const roleRoomAccess = {
            patient: ['patients', `user:${user.id}`],
            doctor: ['healthcare', 'doctors', `user:${user.id}`],
            hospital: ['healthcare', 'hospitals', `user:${user.id}`],
            insurance: ['insurance', `user:${user.id}`],
            admin: ['admin', 'patients', 'healthcare', 'insurance', `user:${user.id}`],
        };
        const allowedRooms = roleRoomAccess[user.role] || [];
        return allowedRooms.includes(room) || room.startsWith(`user:${user.id}`);
    }
};
exports.NotificationsGateway = NotificationsGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", socket_io_1.Server)
], NotificationsGateway.prototype, "server", void 0);
__decorate([
    (0, websockets_1.SubscribeMessage)('ping'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket]),
    __metadata("design:returntype", void 0)
], NotificationsGateway.prototype, "handlePing", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('join-room'),
    (0, common_1.UseGuards)(ws_auth_guard_1.WsRolesGuard),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleJoinRoom", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('leave-room'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleLeaveRoom", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('send-notification'),
    (0, common_1.UseGuards)(ws_auth_guard_1.WsRolesGuard),
    (0, ws_auth_guard_1.WsRoles)('admin', 'doctor', 'hospital'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket,
        dto_1.SendNotificationDto]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleSendNotification", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('broadcast-notification'),
    (0, common_1.UseGuards)(ws_auth_guard_1.WsRolesGuard),
    (0, ws_auth_guard_1.WsRoles)('admin', 'hospital'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket,
        dto_1.BroadcastNotificationDto]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleBroadcastNotification", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('get-notification-history'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket,
        dto_1.NotificationHistoryQueryDto]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleGetNotificationHistory", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('mark-notification-read'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [socket_io_1.Socket, Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleMarkNotificationRead", null);
__decorate([
    (0, event_emitter_1.OnEvent)('reminder.due'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleReminderDue", null);
__decorate([
    (0, event_emitter_1.OnEvent)('reminder.sent'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleReminderSent", null);
__decorate([
    (0, event_emitter_1.OnEvent)('reminder.failed'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleReminderFailed", null);
__decorate([
    (0, event_emitter_1.OnEvent)('adherence.updated'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleAdherenceUpdated", null);
__decorate([
    (0, event_emitter_1.OnEvent)('achievement.unlocked'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleAchievementUnlocked", null);
__decorate([
    (0, event_emitter_1.OnEvent)('adherence.critical'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleCriticalAdherence", null);
__decorate([
    (0, event_emitter_1.OnEvent)('dashboard.update'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], NotificationsGateway.prototype, "handleDashboardUpdate", null);
exports.NotificationsGateway = NotificationsGateway = NotificationsGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        cors: {
            origin: process.env.CORS_ORIGIN || 'http://localhost:3000',
            credentials: true,
        },
        namespace: '/',
    }),
    (0, common_1.UseFilters)(WebSocketExceptionFilter),
    (0, common_1.UseGuards)(ws_auth_guard_1.WsAuthGuard),
    __metadata("design:paramtypes", [notifications_service_1.NotificationsService])
], NotificationsGateway);
//# sourceMappingURL=notifications.gateway.js.map