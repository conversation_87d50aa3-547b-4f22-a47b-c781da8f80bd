import { SupabaseService } from '../../config/supabase.service';
import { BaseNotification, NotificationType, NotificationRole, SendNotificationDto, BroadcastNotificationDto, NotificationHistoryQueryDto, NotificationResponse, NotificationHistoryResponse, ConnectedUser } from './dto';
export declare class NotificationsService {
    private readonly supabaseService;
    private readonly logger;
    private connectedUsers;
    private userSockets;
    constructor(supabaseService: SupabaseService);
    addConnectedUser(userId: string, userRole: NotificationRole, socketId: string): void;
    removeConnectedUser(socketId: string): void;
    updateUserActivity(socketId: string): void;
    getConnectedUser(socketId: string): ConnectedUser | undefined;
    getUserSockets(userId: string): string[];
    getConnectedUsersByRole(role: NotificationRole): ConnectedUser[];
    isUserConnected(userId: string): boolean;
    createNotification(userId: string, userRole: NotificationRole, type: NotificationType, title: string, message: string, data?: any): Promise<BaseNotification>;
    sendNotificationToUser(dto: SendNotificationDto): Promise<NotificationResponse>;
    broadcastNotificationToRole(dto: BroadcastNotificationDto): Promise<NotificationResponse>;
    getNotificationHistory(query: NotificationHistoryQueryDto): Promise<NotificationHistoryResponse>;
    markNotificationAsRead(notificationId: string, userId: string): Promise<boolean>;
    private saveNotificationToDatabase;
    private filterUsersByCriteria;
    sendReminderDueNotification(userId: string, reminderData: any): Promise<void>;
    sendAdherenceUpdateNotification(userId: string, adherenceData: any): Promise<void>;
    sendAchievementNotification(userId: string, achievementData: any): Promise<void>;
    sendCriticalAdherenceAlert(doctorId: string, patientData: any): Promise<void>;
}
