"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var NotificationsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../config/supabase.service");
const dto_1 = require("./dto");
const uuid_1 = require("uuid");
let NotificationsService = NotificationsService_1 = class NotificationsService {
    supabaseService;
    logger = new common_1.Logger(NotificationsService_1.name);
    connectedUsers = new Map();
    userSockets = new Map();
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    addConnectedUser(userId, userRole, socketId) {
        const connectedUser = {
            userId,
            userRole,
            socketId,
            connectedAt: new Date(),
            lastActivity: new Date(),
        };
        this.connectedUsers.set(socketId, connectedUser);
        if (!this.userSockets.has(userId)) {
            this.userSockets.set(userId, new Set());
        }
        this.userSockets.get(userId).add(socketId);
        this.logger.log(`User connected: ${userId} (${userRole}) - Socket: ${socketId}`);
    }
    removeConnectedUser(socketId) {
        const connectedUser = this.connectedUsers.get(socketId);
        if (connectedUser) {
            const { userId } = connectedUser;
            this.connectedUsers.delete(socketId);
            const userSocketSet = this.userSockets.get(userId);
            if (userSocketSet) {
                userSocketSet.delete(socketId);
                if (userSocketSet.size === 0) {
                    this.userSockets.delete(userId);
                }
            }
            this.logger.log(`User disconnected: ${userId} - Socket: ${socketId}`);
        }
    }
    updateUserActivity(socketId) {
        const connectedUser = this.connectedUsers.get(socketId);
        if (connectedUser) {
            connectedUser.lastActivity = new Date();
        }
    }
    getConnectedUser(socketId) {
        return this.connectedUsers.get(socketId);
    }
    getUserSockets(userId) {
        const socketSet = this.userSockets.get(userId);
        return socketSet ? Array.from(socketSet) : [];
    }
    getConnectedUsersByRole(role) {
        return Array.from(this.connectedUsers.values()).filter(user => user.userRole === role);
    }
    isUserConnected(userId) {
        return this.userSockets.has(userId);
    }
    async createNotification(userId, userRole, type, title, message, data) {
        const notification = {
            id: (0, uuid_1.v4)(),
            type,
            title,
            message,
            userId,
            userRole,
            data,
            timestamp: new Date(),
            read: false,
        };
        await this.saveNotificationToDatabase(notification);
        return notification;
    }
    async sendNotificationToUser(dto) {
        try {
            const notification = await this.createNotification(dto.userId, dto.userRole, dto.type, dto.title, dto.message, dto.data);
            const socketIds = this.getUserSockets(dto.userId);
            return {
                success: true,
                message: 'Notification sent successfully',
                notificationId: notification.id,
                deliveredTo: socketIds.length,
            };
        }
        catch (error) {
            this.logger.error('Failed to send notification:', error);
            return {
                success: false,
                message: 'Failed to send notification',
            };
        }
    }
    async broadcastNotificationToRole(dto) {
        try {
            let targetUsers = this.getConnectedUsersByRole(dto.targetRole);
            if (dto.filter) {
                targetUsers = await this.filterUsersByCriteria(targetUsers, dto.filter);
            }
            let deliveredCount = 0;
            for (const user of targetUsers) {
                await this.createNotification(user.userId, user.userRole, dto.type, dto.title, dto.message, dto.data);
                deliveredCount++;
            }
            return {
                success: true,
                message: 'Broadcast notification sent successfully',
                deliveredTo: deliveredCount,
            };
        }
        catch (error) {
            this.logger.error('Failed to broadcast notification:', error);
            return {
                success: false,
                message: 'Failed to broadcast notification',
            };
        }
    }
    async getNotificationHistory(query) {
        try {
            const supabase = this.supabaseService.getAdminClient();
            let queryBuilder = supabase
                .from('notification_history')
                .select('*', { count: 'exact' });
            if (query.userId) {
                queryBuilder = queryBuilder.eq('user_id', query.userId);
            }
            if (query.type) {
                queryBuilder = queryBuilder.eq('type', query.type);
            }
            if (query.read !== undefined) {
                queryBuilder = queryBuilder.eq('read', query.read);
            }
            if (query.startDate) {
                queryBuilder = queryBuilder.gte('timestamp', query.startDate);
            }
            if (query.endDate) {
                queryBuilder = queryBuilder.lte('timestamp', query.endDate);
            }
            const page = query.page || 1;
            const limit = query.limit || 20;
            const offset = (page - 1) * limit;
            queryBuilder = queryBuilder
                .order('timestamp', { ascending: false })
                .range(offset, offset + limit - 1);
            const { data, error, count } = await queryBuilder;
            if (error) {
                throw error;
            }
            return {
                notifications: data || [],
                total: count || 0,
                page: page,
                limit: limit,
                totalPages: Math.ceil((count || 0) / limit),
            };
        }
        catch (error) {
            this.logger.error('Failed to get notification history:', error);
            throw error;
        }
    }
    async markNotificationAsRead(notificationId, userId) {
        try {
            const supabase = this.supabaseService.getAdminClient();
            const { error } = await supabase
                .from('notification_history')
                .update({ read: true })
                .eq('id', notificationId)
                .eq('user_id', userId);
            if (error) {
                throw error;
            }
            return true;
        }
        catch (error) {
            this.logger.error('Failed to mark notification as read:', error);
            return false;
        }
    }
    async saveNotificationToDatabase(notification) {
        try {
            const supabase = this.supabaseService.getAdminClient();
            const { error } = await supabase
                .from('notification_history')
                .insert({
                id: notification.id,
                type: notification.type,
                title: notification.title,
                message: notification.message,
                user_id: notification.userId,
                user_role: notification.userRole,
                data: notification.data,
                timestamp: notification.timestamp.toISOString(),
                read: notification.read,
            });
            if (error) {
                throw error;
            }
        }
        catch (error) {
            this.logger.error('Failed to save notification to database:', error);
        }
    }
    async filterUsersByCriteria(users, filter) {
        return users;
    }
    async sendReminderDueNotification(userId, reminderData) {
        await this.sendNotificationToUser({
            userId,
            userRole: dto_1.NotificationRole.PATIENT,
            type: dto_1.NotificationType.REMINDER_DUE,
            title: 'Medication Reminder',
            message: `Time to take your ${reminderData.medicineName}`,
            data: reminderData,
        });
    }
    async sendAdherenceUpdateNotification(userId, adherenceData) {
        await this.sendNotificationToUser({
            userId,
            userRole: dto_1.NotificationRole.PATIENT,
            type: dto_1.NotificationType.ADHERENCE_UPDATED,
            title: 'Adherence Updated',
            message: `Your medication adherence has been updated`,
            data: adherenceData,
        });
    }
    async sendAchievementNotification(userId, achievementData) {
        await this.sendNotificationToUser({
            userId,
            userRole: dto_1.NotificationRole.PATIENT,
            type: dto_1.NotificationType.ACHIEVEMENT_UNLOCKED,
            title: 'Achievement Unlocked!',
            message: `Congratulations! You've earned: ${achievementData.achievementName}`,
            data: achievementData,
        });
    }
    async sendCriticalAdherenceAlert(doctorId, patientData) {
        await this.sendNotificationToUser({
            userId: doctorId,
            userRole: dto_1.NotificationRole.DOCTOR,
            type: dto_1.NotificationType.ADHERENCE_CRITICAL,
            title: 'Critical Adherence Alert',
            message: `Patient ${patientData.patientName} has critical adherence issues`,
            data: patientData,
        });
    }
};
exports.NotificationsService = NotificationsService;
exports.NotificationsService = NotificationsService = NotificationsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], NotificationsService);
//# sourceMappingURL=notifications.service.js.map