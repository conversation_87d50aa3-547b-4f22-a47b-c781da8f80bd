export declare class CreatePrescriptionDto {
    patient_id: string;
    doctor_id?: string;
    filename: string;
    file_url: string;
    extracted_text?: string;
}
export declare class UpdatePrescriptionDto {
    doctor_id?: string;
    status?: 'processing' | 'completed' | 'failed';
    extracted_text?: string;
}
export declare class PrescriptionQueryDto {
    patient_id?: string;
    doctor_id?: string;
    status?: 'processing' | 'completed' | 'failed' | 'all';
    filename?: string;
}
export declare class ProcessPrescriptionDto {
    status: 'processing' | 'completed' | 'failed';
    extracted_text?: string;
}
