import { SupabaseService } from '../../config/supabase.service';
import { AwsService } from '../../common/services/aws.service';
import { FileUploadService } from '../../common/services/file-upload.service';
import { User, Prescription } from '../../common/types';
import { CreatePrescriptionDto, UpdatePrescriptionDto, PrescriptionQueryDto, ProcessPrescriptionDto } from './dto';
export declare class PrescriptionsService {
    private readonly supabaseService;
    private readonly awsService;
    private readonly fileUploadService;
    constructor(supabaseService: SupabaseService, awsService: AwsService, fileUploadService: FileUploadService);
    create(createPrescriptionDto: CreatePrescriptionDto, currentUser: User): Promise<Prescription>;
    findAll(query: PrescriptionQueryDto, currentUser: User): Promise<Prescription[]>;
    findOne(id: string, currentUser: User): Promise<Prescription>;
    update(id: string, updatePrescriptionDto: UpdatePrescriptionDto, currentUser: User): Promise<Prescription>;
    remove(id: string, currentUser: User): Promise<void>;
    processPrescription(id: string, processPrescriptionDto: ProcessPrescriptionDto, currentUser: User): Promise<Prescription>;
    findByPatient(patientId: string, currentUser: User): Promise<Prescription[]>;
    findByDoctor(doctorId: string, currentUser: User): Promise<Prescription[]>;
    uploadAndProcessPrescription(file: Express.Multer.File, patientId: string, doctorId: string, currentUser: User): Promise<Prescription>;
}
