export declare class CreateReminderDto {
    patient_id: string;
    medicine_id: string;
    reminder_type: 'sms' | 'call' | 'both';
    scheduled_time: string;
    message?: string;
    is_recurring?: boolean;
    recurrence_pattern?: 'daily' | 'weekly' | 'monthly';
    recurrence_interval?: number;
    recurrence_days?: string[];
    end_date?: string;
}
export declare class UpdateReminderDto {
    reminder_type?: 'sms' | 'call' | 'both';
    scheduled_time?: string;
    message?: string;
    is_recurring?: boolean;
    recurrence_pattern?: 'daily' | 'weekly' | 'monthly';
    recurrence_interval?: number;
    recurrence_days?: string[];
    end_date?: string;
    is_active?: boolean;
}
export declare class ReminderQueryDto {
    patient_id?: string;
    medicine_id?: string;
    status?: 'pending' | 'sent' | 'failed' | 'all';
    reminder_type?: 'sms' | 'call' | 'both' | 'all';
    date_from?: string;
    date_to?: string;
    is_active?: boolean;
}
export declare class ReminderLogDto {
    reminder_id: string;
    status: 'sent' | 'failed' | 'delivered' | 'read';
    message?: string;
    error_message?: string;
    external_id?: string;
}
export declare class BulkReminderDto {
    patient_ids: string[];
    medicine_id: string;
    reminder_type: 'sms' | 'call' | 'both';
    time: string;
    message?: string;
    is_recurring?: boolean;
    recurrence_pattern?: 'daily' | 'weekly' | 'monthly';
}
