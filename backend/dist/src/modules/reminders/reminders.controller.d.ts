import { RemindersService } from './reminders.service';
import { CreateReminderDto, UpdateReminderDto, ReminderQueryDto, BulkReminderDto } from './dto';
import { User } from '../../common/types';
export declare class RemindersController {
    private readonly remindersService;
    constructor(remindersService: RemindersService);
    create(createReminderDto: CreateReminderDto, currentUser: User): Promise<import("../../common/types").Reminder>;
    createBulk(bulkReminderDto: BulkReminderDto, currentUser: User): Promise<import("../../common/types").Reminder[]>;
    findAll(query: ReminderQueryDto, currentUser: User): Promise<import("../../common/types").Reminder[]>;
    findByPatient(patientId: string, currentUser: User): Promise<import("../../common/types").Reminder[]>;
    findByMedicine(medicineId: string, currentUser: User): Promise<import("../../common/types").Reminder[]>;
    findOne(id: string, currentUser: User): Promise<import("../../common/types").Reminder>;
    update(id: string, updateReminderDto: UpdateReminderDto, currentUser: User): Promise<import("../../common/types").Reminder>;
    remove(id: string, currentUser: User): Promise<{
        message: string;
    }>;
}
