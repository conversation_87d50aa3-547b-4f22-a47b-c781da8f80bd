import { EventEmitter2 } from '@nestjs/event-emitter';
import { SupabaseService } from '../../config/supabase.service';
import { TwilioService } from '../../common/services/twilio.service';
import { ElevenLabsService } from '../../common/services/elevenlabs.service';
import { User, Reminder } from '../../common/types';
import { CreateReminderDto, UpdateReminderDto, ReminderQueryDto, BulkReminderDto } from './dto';
export declare class RemindersService {
    private readonly supabaseService;
    private readonly twilioService;
    private readonly elevenLabsService;
    private readonly eventEmitter;
    private readonly logger;
    constructor(supabaseService: SupabaseService, twilioService: TwilioService, elevenLabsService: ElevenLabsService, eventEmitter: EventEmitter2);
    create(createReminderDto: CreateReminderDto, currentUser: User): Promise<Reminder>;
    findAll(query: ReminderQueryDto, currentUser: User): Promise<Reminder[]>;
    getTodaysReminders(currentUser: User): Promise<Reminder[]>;
    getUpcomingReminders(currentUser: User, hours?: number): Promise<Reminder[]>;
    getOverdueReminders(currentUser: User): Promise<Reminder[]>;
    findOne(id: string, currentUser: User): Promise<Reminder>;
    update(id: string, updateReminderDto: UpdateReminderDto, currentUser: User): Promise<Reminder>;
    remove(id: string, currentUser: User): Promise<void>;
    createBulkReminders(bulkReminderDto: BulkReminderDto, currentUser: User): Promise<Reminder[]>;
    processPendingReminders(): Promise<void>;
    private processReminder;
    private createNextRecurringReminder;
}
