import { UserRole } from '../../../common/types';
export declare class CreateUserDto {
    email: string;
    name: string;
    role: UserRole;
    avatar?: string;
}
export declare class UpdateUserDto {
    name?: string;
    avatar?: string;
}
export declare class CreatePatientDto extends CreateUserDto {
    role: 'patient';
    date_of_birth?: string;
    emergency_contact?: string;
    assigned_doctor_id?: string;
    insurance_id?: string;
}
export declare class CreateDoctorDto extends CreateUserDto {
    role: 'doctor';
    specialization: string;
    license_number: string;
    hospital_id?: string;
}
export declare class CreateHospitalDto extends CreateUserDto {
    role: 'hospital';
    address: string;
    phone: string;
    website?: string;
}
export declare class CreateInsuranceDto extends CreateUserDto {
    role: 'insurance';
    company_name: string;
    address: string;
    phone: string;
    website?: string;
    policy_types?: string[];
    coverage_areas?: string[];
}
export declare class UpdatePatientDto {
    name?: string;
    avatar?: string;
    date_of_birth?: string;
    emergency_contact?: string;
    assigned_doctor_id?: string;
    insurance_id?: string;
}
export declare class UpdateDoctorDto {
    name?: string;
    avatar?: string;
    specialization?: string;
    hospital_id?: string;
}
export declare class UpdateHospitalDto {
    name?: string;
    avatar?: string;
    address?: string;
    phone?: string;
    website?: string;
}
export declare class UpdateInsuranceDto {
    name?: string;
    avatar?: string;
    company_name?: string;
    address?: string;
    phone?: string;
    website?: string;
    policy_types?: string[];
    coverage_areas?: string[];
}
