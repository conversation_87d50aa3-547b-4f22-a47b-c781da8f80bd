import { UsersService } from './users.service';
import { CreatePatientDto, CreateDoctorDto, CreateHospitalDto, CreateInsuranceDto, UpdatePatientDto, UpdateDoctorDto, UpdateHospitalDto, UpdateInsuranceDto } from './dto';
import { User, UserRole } from '../../common/types';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    findAll(role?: UserRole): Promise<User[]>;
    findOne(id: string): Promise<User>;
    getPatientDetails(id: string, currentUser: User): Promise<any>;
    getDoctorDetails(id: string): Promise<any>;
    getHospitalDetails(id: string): Promise<any>;
    getInsuranceDetails(id: string): Promise<any>;
    createPatient(createPatientDto: CreatePatientDto): Promise<any>;
    createDoctor(createDoctorDto: CreateDoctorDto): Promise<any>;
    createHospital(createHospitalDto: CreateHospitalDto): Promise<any>;
    createInsurance(createInsuranceDto: CreateInsuranceDto): Promise<any>;
    updatePatient(id: string, updatePatientDto: UpdatePatientDto, currentUser: User): Promise<any>;
    updateDoctor(id: string, updateDoctorDto: UpdateDoctorDto, currentUser: User): Promise<any>;
    updateHospital(id: string, updateHospitalDto: UpdateHospitalDto, currentUser: User): Promise<any>;
    updateInsurance(id: string, updateInsuranceDto: UpdateInsuranceDto, currentUser: User): Promise<any>;
    remove(id: string, currentUser: User): Promise<{
        message: string;
    }>;
}
