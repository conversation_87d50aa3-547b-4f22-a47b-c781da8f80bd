"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const users_service_1 = require("./users.service");
const dto_1 = require("./dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const roles_guard_1 = require("../../common/guards/roles.guard");
const roles_decorator_1 = require("../../common/decorators/roles.decorator");
const current_user_decorator_1 = require("../../common/decorators/current-user.decorator");
let UsersController = class UsersController {
    usersService;
    constructor(usersService) {
        this.usersService = usersService;
    }
    async findAll(role) {
        return this.usersService.findAll(role);
    }
    async findOne(id) {
        return this.usersService.findOne(id);
    }
    async getPatientDetails(id, currentUser) {
        if (currentUser.role === 'patient' && currentUser.id !== id) {
            throw new Error('You can only view your own patient details');
        }
        return this.usersService.findPatientDetails(id);
    }
    async getDoctorDetails(id) {
        return this.usersService.findDoctorDetails(id);
    }
    async getHospitalDetails(id) {
        return this.usersService.findHospitalDetails(id);
    }
    async getInsuranceDetails(id) {
        return this.usersService.findInsuranceDetails(id);
    }
    async createPatient(createPatientDto) {
        return this.usersService.createPatient(createPatientDto);
    }
    async createDoctor(createDoctorDto) {
        return this.usersService.createDoctor(createDoctorDto);
    }
    async createHospital(createHospitalDto) {
        return this.usersService.createHospital(createHospitalDto);
    }
    async createInsurance(createInsuranceDto) {
        return this.usersService.createInsurance(createInsuranceDto);
    }
    async updatePatient(id, updatePatientDto, currentUser) {
        return this.usersService.updatePatient(id, updatePatientDto, currentUser);
    }
    async updateDoctor(id, updateDoctorDto, currentUser) {
        return this.usersService.updateDoctor(id, updateDoctorDto, currentUser);
    }
    async updateHospital(id, updateHospitalDto, currentUser) {
        return this.usersService.updateHospital(id, updateHospitalDto, currentUser);
    }
    async updateInsurance(id, updateInsuranceDto, currentUser) {
        return this.usersService.updateInsurance(id, updateInsuranceDto, currentUser);
    }
    async remove(id, currentUser) {
        await this.usersService.remove(id, currentUser);
        return { message: 'User deleted successfully' };
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all users' }),
    (0, swagger_1.ApiQuery)({ name: 'role', required: false, enum: ['patient', 'doctor', 'hospital', 'admin', 'insurance'] }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Users retrieved successfully' }),
    (0, roles_decorator_1.Roles)('admin', 'doctor', 'hospital', 'insurance'),
    __param(0, (0, common_1.Query)('role')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get user by ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found' }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/patient-details'),
    (0, swagger_1.ApiOperation)({ summary: 'Get patient details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Patient details retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    (0, roles_decorator_1.Roles)('admin', 'doctor', 'hospital', 'insurance', 'patient'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getPatientDetails", null);
__decorate([
    (0, common_1.Get)(':id/doctor-details'),
    (0, swagger_1.ApiOperation)({ summary: 'Get doctor details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Doctor details retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Doctor not found' }),
    (0, roles_decorator_1.Roles)('admin', 'doctor', 'hospital', 'patient'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getDoctorDetails", null);
__decorate([
    (0, common_1.Get)(':id/hospital-details'),
    (0, swagger_1.ApiOperation)({ summary: 'Get hospital details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Hospital details retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Hospital not found' }),
    (0, roles_decorator_1.Roles)('admin', 'doctor', 'hospital', 'patient', 'insurance'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getHospitalDetails", null);
__decorate([
    (0, common_1.Get)(':id/insurance-details'),
    (0, swagger_1.ApiOperation)({ summary: 'Get insurance provider details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Insurance provider details retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Insurance provider not found' }),
    (0, roles_decorator_1.Roles)('admin', 'insurance', 'patient', 'hospital'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getInsuranceDetails", null);
__decorate([
    (0, common_1.Post)('patients'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new patient' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Patient created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, roles_decorator_1.Roles)('admin', 'doctor', 'hospital'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreatePatientDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "createPatient", null);
__decorate([
    (0, common_1.Post)('doctors'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new doctor' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Doctor created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, roles_decorator_1.Roles)('admin', 'hospital'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateDoctorDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "createDoctor", null);
__decorate([
    (0, common_1.Post)('hospitals'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new hospital' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Hospital created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateHospitalDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "createHospital", null);
__decorate([
    (0, common_1.Post)('insurance'),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new insurance provider' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Insurance provider created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [dto_1.CreateInsuranceDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "createInsurance", null);
__decorate([
    (0, common_1.Patch)(':id/patient'),
    (0, swagger_1.ApiOperation)({ summary: 'Update patient details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Patient updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Patient not found' }),
    (0, roles_decorator_1.Roles)('admin', 'patient', 'doctor'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdatePatientDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updatePatient", null);
__decorate([
    (0, common_1.Patch)(':id/doctor'),
    (0, swagger_1.ApiOperation)({ summary: 'Update doctor details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Doctor updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Doctor not found' }),
    (0, roles_decorator_1.Roles)('admin', 'doctor', 'hospital'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateDoctorDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateDoctor", null);
__decorate([
    (0, common_1.Patch)(':id/hospital'),
    (0, swagger_1.ApiOperation)({ summary: 'Update hospital details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Hospital updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Hospital not found' }),
    (0, roles_decorator_1.Roles)('admin', 'hospital'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateHospitalDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateHospital", null);
__decorate([
    (0, common_1.Patch)(':id/insurance'),
    (0, swagger_1.ApiOperation)({ summary: 'Update insurance provider details' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Insurance provider updated successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Insurance provider not found' }),
    (0, roles_decorator_1.Roles)('admin', 'insurance'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, dto_1.UpdateInsuranceDto, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "updateInsurance", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete user' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'User deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 403, description: 'Forbidden' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found' }),
    (0, roles_decorator_1.Roles)('admin'),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "remove", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('users'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, common_1.Controller)('users'),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map