import { SupabaseService } from '../../config/supabase.service';
import { User, UserRole } from '../../common/types';
import { CreatePatientDto, CreateDoctorDto, CreateHospitalDto, CreateInsuranceDto, UpdatePatientDto, UpdateDoctorDto, UpdateHospitalDto, UpdateInsuranceDto } from './dto';
export declare class UsersService {
    private readonly supabaseService;
    constructor(supabaseService: SupabaseService);
    findAll(role?: UserRole): Promise<User[]>;
    findOne(id: string): Promise<User>;
    findByEmail(email: string): Promise<User | null>;
    findPatientDetails(id: string): Promise<any>;
    findDoctorDetails(id: string): Promise<any>;
    findHospitalDetails(id: string): Promise<any>;
    findInsuranceDetails(id: string): Promise<any>;
    createPatient(createPatientDto: CreatePatientDto): Promise<any>;
    createDoctor(createDoctorDto: CreateDoctorDto): Promise<any>;
    createHospital(createHospitalDto: CreateHospitalDto): Promise<any>;
    createInsurance(createInsuranceDto: CreateInsuranceDto): Promise<any>;
    updatePatient(id: string, updatePatientDto: UpdatePatientDto, currentUser: User): Promise<any>;
    updateDoctor(id: string, updateDoctorDto: UpdateDoctorDto, currentUser: User): Promise<any>;
    updateHospital(id: string, updateHospitalDto: UpdateHospitalDto, currentUser: User): Promise<any>;
    updateInsurance(id: string, updateInsuranceDto: UpdateInsuranceDto, currentUser: User): Promise<any>;
    remove(id: string, currentUser: User): Promise<void>;
}
