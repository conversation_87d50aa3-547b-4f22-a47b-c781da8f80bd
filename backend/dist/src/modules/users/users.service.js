"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const supabase_service_1 = require("../../config/supabase.service");
let UsersService = class UsersService {
    supabaseService;
    constructor(supabaseService) {
        this.supabaseService = supabaseService;
    }
    async findAll(role) {
        const supabase = this.supabaseService.getClient();
        let query = supabase.from('users').select('*');
        if (role) {
            query = query.eq('role', role);
        }
        const { data, error } = await query.order('created_at', { ascending: false });
        if (error) {
            throw new common_1.BadRequestException(`Failed to fetch users: ${error.message}`);
        }
        return data || [];
    }
    async findOne(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('id', id)
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`User with ID ${id} not found`);
        }
        return data;
    }
    async findByEmail(email) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('users')
            .select('*')
            .eq('email', email)
            .single();
        if (error) {
            return null;
        }
        return data;
    }
    async findPatientDetails(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('users')
            .select(`
        *,
        patients (
          date_of_birth,
          emergency_contact,
          assigned_doctor_id,
          insurance_id,
          assigned_doctor:assigned_doctor_id (id, name, email),
          insurance:insurance_id (id, name, email)
        )
      `)
            .eq('id', id)
            .eq('role', 'patient')
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Patient with ID ${id} not found`);
        }
        return {
            ...data,
            ...data.patients[0],
            patients: undefined,
        };
    }
    async findDoctorDetails(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('users')
            .select(`
        *,
        doctors (
          specialization,
          license_number,
          hospital_id,
          hospital:hospital_id (id, name, email)
        )
      `)
            .eq('id', id)
            .eq('role', 'doctor')
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Doctor with ID ${id} not found`);
        }
        return {
            ...data,
            ...data.doctors[0],
            doctors: undefined,
        };
    }
    async findHospitalDetails(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('users')
            .select(`
        *,
        hospitals (
          address,
          phone,
          website
        )
      `)
            .eq('id', id)
            .eq('role', 'hospital')
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Hospital with ID ${id} not found`);
        }
        return {
            ...data,
            ...data.hospitals[0],
            hospitals: undefined,
        };
    }
    async findInsuranceDetails(id) {
        const supabase = this.supabaseService.getClient();
        const { data, error } = await supabase
            .from('users')
            .select(`
        *,
        insurance_providers (
          company_name,
          address,
          phone,
          website,
          policy_types,
          coverage_areas
        )
      `)
            .eq('id', id)
            .eq('role', 'insurance')
            .single();
        if (error || !data) {
            throw new common_1.NotFoundException(`Insurance provider with ID ${id} not found`);
        }
        return {
            ...data,
            ...data.insurance_providers[0],
            insurance_providers: undefined,
        };
    }
    async createPatient(createPatientDto) {
        const supabase = this.supabaseService.getAdminClient();
        const existingUser = await this.findByEmail(createPatientDto.email);
        if (existingUser) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        const { data: userData, error: userError } = await supabase
            .from('users')
            .insert({
            email: createPatientDto.email,
            name: createPatientDto.name,
            role: 'patient',
            avatar: createPatientDto.avatar,
        })
            .select()
            .single();
        if (userError) {
            throw new common_1.BadRequestException(`Failed to create user: ${userError.message}`);
        }
        const { data: patientData, error: patientError } = await supabase
            .from('patients')
            .insert({
            id: userData.id,
            date_of_birth: createPatientDto.date_of_birth,
            emergency_contact: createPatientDto.emergency_contact,
            assigned_doctor_id: createPatientDto.assigned_doctor_id,
            insurance_id: createPatientDto.insurance_id,
        })
            .select()
            .single();
        if (patientError) {
            await supabase.from('users').delete().eq('id', userData.id);
            throw new common_1.BadRequestException(`Failed to create patient: ${patientError.message}`);
        }
        return {
            ...userData,
            ...patientData,
        };
    }
    async createDoctor(createDoctorDto) {
        const supabase = this.supabaseService.getAdminClient();
        const existingUser = await this.findByEmail(createDoctorDto.email);
        if (existingUser) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        const { data: userData, error: userError } = await supabase
            .from('users')
            .insert({
            email: createDoctorDto.email,
            name: createDoctorDto.name,
            role: 'doctor',
            avatar: createDoctorDto.avatar,
        })
            .select()
            .single();
        if (userError) {
            throw new common_1.BadRequestException(`Failed to create user: ${userError.message}`);
        }
        const { data: doctorData, error: doctorError } = await supabase
            .from('doctors')
            .insert({
            id: userData.id,
            specialization: createDoctorDto.specialization,
            license_number: createDoctorDto.license_number,
            hospital_id: createDoctorDto.hospital_id,
        })
            .select()
            .single();
        if (doctorError) {
            await supabase.from('users').delete().eq('id', userData.id);
            throw new common_1.BadRequestException(`Failed to create doctor: ${doctorError.message}`);
        }
        return {
            ...userData,
            ...doctorData,
        };
    }
    async createHospital(createHospitalDto) {
        const supabase = this.supabaseService.getAdminClient();
        const existingUser = await this.findByEmail(createHospitalDto.email);
        if (existingUser) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        const { data: userData, error: userError } = await supabase
            .from('users')
            .insert({
            email: createHospitalDto.email,
            name: createHospitalDto.name,
            role: 'hospital',
            avatar: createHospitalDto.avatar,
        })
            .select()
            .single();
        if (userError) {
            throw new common_1.BadRequestException(`Failed to create user: ${userError.message}`);
        }
        const { data: hospitalData, error: hospitalError } = await supabase
            .from('hospitals')
            .insert({
            id: userData.id,
            address: createHospitalDto.address,
            phone: createHospitalDto.phone,
            website: createHospitalDto.website,
        })
            .select()
            .single();
        if (hospitalError) {
            await supabase.from('users').delete().eq('id', userData.id);
            throw new common_1.BadRequestException(`Failed to create hospital: ${hospitalError.message}`);
        }
        return {
            ...userData,
            ...hospitalData,
        };
    }
    async createInsurance(createInsuranceDto) {
        const supabase = this.supabaseService.getAdminClient();
        const existingUser = await this.findByEmail(createInsuranceDto.email);
        if (existingUser) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        const { data: userData, error: userError } = await supabase
            .from('users')
            .insert({
            email: createInsuranceDto.email,
            name: createInsuranceDto.name,
            role: 'insurance',
            avatar: createInsuranceDto.avatar,
        })
            .select()
            .single();
        if (userError) {
            throw new common_1.BadRequestException(`Failed to create user: ${userError.message}`);
        }
        const { data: insuranceData, error: insuranceError } = await supabase
            .from('insurance_providers')
            .insert({
            id: userData.id,
            company_name: createInsuranceDto.company_name,
            address: createInsuranceDto.address,
            phone: createInsuranceDto.phone,
            website: createInsuranceDto.website,
            policy_types: createInsuranceDto.policy_types || [],
            coverage_areas: createInsuranceDto.coverage_areas || [],
        })
            .select()
            .single();
        if (insuranceError) {
            await supabase.from('users').delete().eq('id', userData.id);
            throw new common_1.BadRequestException(`Failed to create insurance provider: ${insuranceError.message}`);
        }
        return {
            ...userData,
            ...insuranceData,
        };
    }
    async updatePatient(id, updatePatientDto, currentUser) {
        if (currentUser.role !== 'admin' && currentUser.id !== id) {
            throw new common_1.ForbiddenException('You can only update your own profile');
        }
        const supabase = this.supabaseService.getAdminClient();
        const user = await this.findOne(id);
        if (user.role !== 'patient') {
            throw new common_1.BadRequestException('User is not a patient');
        }
        const userUpdates = {};
        if (updatePatientDto.name)
            userUpdates.name = updatePatientDto.name;
        if (updatePatientDto.avatar !== undefined)
            userUpdates.avatar = updatePatientDto.avatar;
        if (Object.keys(userUpdates).length > 0) {
            const { error: userError } = await supabase
                .from('users')
                .update(userUpdates)
                .eq('id', id);
            if (userError) {
                throw new common_1.BadRequestException(`Failed to update user: ${userError.message}`);
            }
        }
        const patientUpdates = {};
        if (updatePatientDto.date_of_birth !== undefined)
            patientUpdates.date_of_birth = updatePatientDto.date_of_birth;
        if (updatePatientDto.emergency_contact !== undefined)
            patientUpdates.emergency_contact = updatePatientDto.emergency_contact;
        if (updatePatientDto.assigned_doctor_id !== undefined)
            patientUpdates.assigned_doctor_id = updatePatientDto.assigned_doctor_id;
        if (updatePatientDto.insurance_id !== undefined)
            patientUpdates.insurance_id = updatePatientDto.insurance_id;
        if (Object.keys(patientUpdates).length > 0) {
            const { error: patientError } = await supabase
                .from('patients')
                .update(patientUpdates)
                .eq('id', id);
            if (patientError) {
                throw new common_1.BadRequestException(`Failed to update patient: ${patientError.message}`);
            }
        }
        return this.findPatientDetails(id);
    }
    async updateDoctor(id, updateDoctorDto, currentUser) {
        if (currentUser.role !== 'admin' && currentUser.id !== id) {
            throw new common_1.ForbiddenException('You can only update your own profile');
        }
        const supabase = this.supabaseService.getAdminClient();
        const user = await this.findOne(id);
        if (user.role !== 'doctor') {
            throw new common_1.BadRequestException('User is not a doctor');
        }
        const userUpdates = {};
        if (updateDoctorDto.name)
            userUpdates.name = updateDoctorDto.name;
        if (updateDoctorDto.avatar !== undefined)
            userUpdates.avatar = updateDoctorDto.avatar;
        if (Object.keys(userUpdates).length > 0) {
            const { error: userError } = await supabase
                .from('users')
                .update(userUpdates)
                .eq('id', id);
            if (userError) {
                throw new common_1.BadRequestException(`Failed to update user: ${userError.message}`);
            }
        }
        const doctorUpdates = {};
        if (updateDoctorDto.specialization)
            doctorUpdates.specialization = updateDoctorDto.specialization;
        if (updateDoctorDto.hospital_id !== undefined)
            doctorUpdates.hospital_id = updateDoctorDto.hospital_id;
        if (Object.keys(doctorUpdates).length > 0) {
            const { error: doctorError } = await supabase
                .from('doctors')
                .update(doctorUpdates)
                .eq('id', id);
            if (doctorError) {
                throw new common_1.BadRequestException(`Failed to update doctor: ${doctorError.message}`);
            }
        }
        return this.findDoctorDetails(id);
    }
    async updateHospital(id, updateHospitalDto, currentUser) {
        if (currentUser.role !== 'admin' && currentUser.id !== id) {
            throw new common_1.ForbiddenException('You can only update your own profile');
        }
        const supabase = this.supabaseService.getAdminClient();
        const user = await this.findOne(id);
        if (user.role !== 'hospital') {
            throw new common_1.BadRequestException('User is not a hospital');
        }
        const userUpdates = {};
        if (updateHospitalDto.name)
            userUpdates.name = updateHospitalDto.name;
        if (updateHospitalDto.avatar !== undefined)
            userUpdates.avatar = updateHospitalDto.avatar;
        if (Object.keys(userUpdates).length > 0) {
            const { error: userError } = await supabase
                .from('users')
                .update(userUpdates)
                .eq('id', id);
            if (userError) {
                throw new common_1.BadRequestException(`Failed to update user: ${userError.message}`);
            }
        }
        const hospitalUpdates = {};
        if (updateHospitalDto.address)
            hospitalUpdates.address = updateHospitalDto.address;
        if (updateHospitalDto.phone)
            hospitalUpdates.phone = updateHospitalDto.phone;
        if (updateHospitalDto.website !== undefined)
            hospitalUpdates.website = updateHospitalDto.website;
        if (Object.keys(hospitalUpdates).length > 0) {
            const { error: hospitalError } = await supabase
                .from('hospitals')
                .update(hospitalUpdates)
                .eq('id', id);
            if (hospitalError) {
                throw new common_1.BadRequestException(`Failed to update hospital: ${hospitalError.message}`);
            }
        }
        return this.findHospitalDetails(id);
    }
    async updateInsurance(id, updateInsuranceDto, currentUser) {
        if (currentUser.role !== 'admin' && currentUser.id !== id) {
            throw new common_1.ForbiddenException('You can only update your own profile');
        }
        const supabase = this.supabaseService.getAdminClient();
        const user = await this.findOne(id);
        if (user.role !== 'insurance') {
            throw new common_1.BadRequestException('User is not an insurance provider');
        }
        const userUpdates = {};
        if (updateInsuranceDto.name)
            userUpdates.name = updateInsuranceDto.name;
        if (updateInsuranceDto.avatar !== undefined)
            userUpdates.avatar = updateInsuranceDto.avatar;
        if (Object.keys(userUpdates).length > 0) {
            const { error: userError } = await supabase
                .from('users')
                .update(userUpdates)
                .eq('id', id);
            if (userError) {
                throw new common_1.BadRequestException(`Failed to update user: ${userError.message}`);
            }
        }
        const insuranceUpdates = {};
        if (updateInsuranceDto.company_name)
            insuranceUpdates.company_name = updateInsuranceDto.company_name;
        if (updateInsuranceDto.address)
            insuranceUpdates.address = updateInsuranceDto.address;
        if (updateInsuranceDto.phone)
            insuranceUpdates.phone = updateInsuranceDto.phone;
        if (updateInsuranceDto.website !== undefined)
            insuranceUpdates.website = updateInsuranceDto.website;
        if (updateInsuranceDto.policy_types)
            insuranceUpdates.policy_types = updateInsuranceDto.policy_types;
        if (updateInsuranceDto.coverage_areas)
            insuranceUpdates.coverage_areas = updateInsuranceDto.coverage_areas;
        if (Object.keys(insuranceUpdates).length > 0) {
            const { error: insuranceError } = await supabase
                .from('insurance_providers')
                .update(insuranceUpdates)
                .eq('id', id);
            if (insuranceError) {
                throw new common_1.BadRequestException(`Failed to update insurance provider: ${insuranceError.message}`);
            }
        }
        return this.findInsuranceDetails(id);
    }
    async remove(id, currentUser) {
        if (currentUser.role !== 'admin') {
            throw new common_1.ForbiddenException('Only administrators can delete users');
        }
        const supabase = this.supabaseService.getAdminClient();
        await this.findOne(id);
        const { error } = await supabase
            .from('users')
            .delete()
            .eq('id', id);
        if (error) {
            throw new common_1.BadRequestException(`Failed to delete user: ${error.message}`);
        }
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [supabase_service_1.SupabaseService])
], UsersService);
//# sourceMappingURL=users.service.js.map