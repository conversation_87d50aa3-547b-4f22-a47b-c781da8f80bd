-- Migration: Add only the missing AI columns to prescriptions table
-- This version excludes extracted_text since it already exists

-- Add only the new AI columns
ALTER TABLE prescriptions 
ADD COLUMN IF NOT EXISTS ai_extracted_medicines TEXT[];

ALTER TABLE prescriptions 
ADD COLUMN IF NOT EXISTS ai_extraction_success BOOLEAN DEFAULT FALSE;

ALTER TABLE prescriptions 
ADD COLUMN IF NOT EXISTS filename VARCHAR(255);

-- Add indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_prescriptions_ai_extracted_medicines 
ON prescriptions USING GIN (ai_extracted_medicines);

CREATE INDEX IF NOT EXISTS idx_prescriptions_ai_extraction_success 
ON prescriptions (ai_extraction_success);

-- Update existing records to set default values
UPDATE prescriptions 
SET ai_extraction_success = FALSE 
WHERE ai_extraction_success IS NULL;

-- Add comments to document the new columns
COMMENT ON COLUMN prescriptions.ai_extracted_medicines IS 'Array of medicine names extracted by OpenAI from prescription text';
COMMENT ON COLUMN prescriptions.ai_extraction_success IS 'Boolean indicating if AI medicine extraction was successful';
COMMENT ON COLUMN prescriptions.filename IS 'Original filename of the uploaded prescription';
