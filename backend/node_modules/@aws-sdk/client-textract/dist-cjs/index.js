"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  AccessDeniedException: () => AccessDeniedException,
  AdapterVersionStatus: () => AdapterVersionStatus,
  AnalyzeDocumentCommand: () => AnalyzeDocumentCommand,
  AnalyzeExpenseCommand: () => AnalyzeExpenseCommand,
  AnalyzeIDCommand: () => AnalyzeIDCommand,
  AutoUpdate: () => AutoUpdate,
  BadDocumentException: () => BadDocumentException,
  BlockType: () => BlockType,
  ConflictException: () => ConflictException,
  ContentClassifier: () => ContentClassifier,
  CreateAdapterCommand: () => CreateAdapterCommand,
  CreateAdapterVersionCommand: () => CreateAdapterVersionCommand,
  DeleteAdapterCommand: () => DeleteAdapterCommand,
  DeleteAdapterVersionCommand: () => DeleteAdapterVersionCommand,
  DetectDocumentTextCommand: () => DetectDocumentTextCommand,
  DocumentTooLargeException: () => DocumentTooLargeException,
  EntityType: () => EntityType,
  FeatureType: () => FeatureType,
  GetAdapterCommand: () => GetAdapterCommand,
  GetAdapterVersionCommand: () => GetAdapterVersionCommand,
  GetDocumentAnalysisCommand: () => GetDocumentAnalysisCommand,
  GetDocumentTextDetectionCommand: () => GetDocumentTextDetectionCommand,
  GetExpenseAnalysisCommand: () => GetExpenseAnalysisCommand,
  GetLendingAnalysisCommand: () => GetLendingAnalysisCommand,
  GetLendingAnalysisSummaryCommand: () => GetLendingAnalysisSummaryCommand,
  HumanLoopQuotaExceededException: () => HumanLoopQuotaExceededException,
  IdempotentParameterMismatchException: () => IdempotentParameterMismatchException,
  InternalServerError: () => InternalServerError,
  InvalidJobIdException: () => InvalidJobIdException,
  InvalidKMSKeyException: () => InvalidKMSKeyException,
  InvalidParameterException: () => InvalidParameterException,
  InvalidS3ObjectException: () => InvalidS3ObjectException,
  JobStatus: () => JobStatus,
  LimitExceededException: () => LimitExceededException,
  ListAdapterVersionsCommand: () => ListAdapterVersionsCommand,
  ListAdaptersCommand: () => ListAdaptersCommand,
  ListTagsForResourceCommand: () => ListTagsForResourceCommand,
  ProvisionedThroughputExceededException: () => ProvisionedThroughputExceededException,
  RelationshipType: () => RelationshipType,
  ResourceNotFoundException: () => ResourceNotFoundException,
  SelectionStatus: () => SelectionStatus,
  ServiceQuotaExceededException: () => ServiceQuotaExceededException,
  StartDocumentAnalysisCommand: () => StartDocumentAnalysisCommand,
  StartDocumentTextDetectionCommand: () => StartDocumentTextDetectionCommand,
  StartExpenseAnalysisCommand: () => StartExpenseAnalysisCommand,
  StartLendingAnalysisCommand: () => StartLendingAnalysisCommand,
  TagResourceCommand: () => TagResourceCommand,
  TextType: () => TextType,
  Textract: () => Textract,
  TextractClient: () => TextractClient,
  TextractServiceException: () => TextractServiceException,
  ThrottlingException: () => ThrottlingException,
  UnsupportedDocumentException: () => UnsupportedDocumentException,
  UntagResourceCommand: () => UntagResourceCommand,
  UpdateAdapterCommand: () => UpdateAdapterCommand,
  ValidationException: () => ValidationException,
  ValueType: () => ValueType,
  __Client: () => import_smithy_client.Client,
  paginateListAdapterVersions: () => paginateListAdapterVersions,
  paginateListAdapters: () => paginateListAdapters
});
module.exports = __toCommonJS(index_exports);

// src/TextractClient.ts
var import_middleware_host_header = require("@aws-sdk/middleware-host-header");
var import_middleware_logger = require("@aws-sdk/middleware-logger");
var import_middleware_recursion_detection = require("@aws-sdk/middleware-recursion-detection");
var import_middleware_user_agent = require("@aws-sdk/middleware-user-agent");
var import_config_resolver = require("@smithy/config-resolver");
var import_core = require("@smithy/core");
var import_middleware_content_length = require("@smithy/middleware-content-length");
var import_middleware_endpoint = require("@smithy/middleware-endpoint");
var import_middleware_retry = require("@smithy/middleware-retry");

var import_httpAuthSchemeProvider = require("./auth/httpAuthSchemeProvider");

// src/endpoint/EndpointParameters.ts
var resolveClientEndpointParameters = /* @__PURE__ */ __name((options) => {
  return Object.assign(options, {
    useDualstackEndpoint: options.useDualstackEndpoint ?? false,
    useFipsEndpoint: options.useFipsEndpoint ?? false,
    defaultSigningName: "textract"
  });
}, "resolveClientEndpointParameters");
var commonParams = {
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};

// src/TextractClient.ts
var import_runtimeConfig = require("././runtimeConfig");

// src/runtimeExtensions.ts
var import_region_config_resolver = require("@aws-sdk/region-config-resolver");
var import_protocol_http = require("@smithy/protocol-http");
var import_smithy_client = require("@smithy/smithy-client");

// src/auth/httpAuthExtensionConfiguration.ts
var getHttpAuthExtensionConfiguration = /* @__PURE__ */ __name((runtimeConfig) => {
  const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;
  let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;
  let _credentials = runtimeConfig.credentials;
  return {
    setHttpAuthScheme(httpAuthScheme) {
      const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);
      if (index === -1) {
        _httpAuthSchemes.push(httpAuthScheme);
      } else {
        _httpAuthSchemes.splice(index, 1, httpAuthScheme);
      }
    },
    httpAuthSchemes() {
      return _httpAuthSchemes;
    },
    setHttpAuthSchemeProvider(httpAuthSchemeProvider) {
      _httpAuthSchemeProvider = httpAuthSchemeProvider;
    },
    httpAuthSchemeProvider() {
      return _httpAuthSchemeProvider;
    },
    setCredentials(credentials) {
      _credentials = credentials;
    },
    credentials() {
      return _credentials;
    }
  };
}, "getHttpAuthExtensionConfiguration");
var resolveHttpAuthRuntimeConfig = /* @__PURE__ */ __name((config) => {
  return {
    httpAuthSchemes: config.httpAuthSchemes(),
    httpAuthSchemeProvider: config.httpAuthSchemeProvider(),
    credentials: config.credentials()
  };
}, "resolveHttpAuthRuntimeConfig");

// src/runtimeExtensions.ts
var resolveRuntimeExtensions = /* @__PURE__ */ __name((runtimeConfig, extensions) => {
  const extensionConfiguration = Object.assign(
    (0, import_region_config_resolver.getAwsRegionExtensionConfiguration)(runtimeConfig),
    (0, import_smithy_client.getDefaultExtensionConfiguration)(runtimeConfig),
    (0, import_protocol_http.getHttpHandlerExtensionConfiguration)(runtimeConfig),
    getHttpAuthExtensionConfiguration(runtimeConfig)
  );
  extensions.forEach((extension) => extension.configure(extensionConfiguration));
  return Object.assign(
    runtimeConfig,
    (0, import_region_config_resolver.resolveAwsRegionExtensionConfiguration)(extensionConfiguration),
    (0, import_smithy_client.resolveDefaultRuntimeConfig)(extensionConfiguration),
    (0, import_protocol_http.resolveHttpHandlerRuntimeConfig)(extensionConfiguration),
    resolveHttpAuthRuntimeConfig(extensionConfiguration)
  );
}, "resolveRuntimeExtensions");

// src/TextractClient.ts
var TextractClient = class extends import_smithy_client.Client {
  static {
    __name(this, "TextractClient");
  }
  /**
   * The resolved configuration of TextractClient class. This is resolved and normalized from the {@link TextractClientConfig | constructor configuration interface}.
   */
  config;
  constructor(...[configuration]) {
    const _config_0 = (0, import_runtimeConfig.getRuntimeConfig)(configuration || {});
    super(_config_0);
    this.initConfig = _config_0;
    const _config_1 = resolveClientEndpointParameters(_config_0);
    const _config_2 = (0, import_middleware_user_agent.resolveUserAgentConfig)(_config_1);
    const _config_3 = (0, import_middleware_retry.resolveRetryConfig)(_config_2);
    const _config_4 = (0, import_config_resolver.resolveRegionConfig)(_config_3);
    const _config_5 = (0, import_middleware_host_header.resolveHostHeaderConfig)(_config_4);
    const _config_6 = (0, import_middleware_endpoint.resolveEndpointConfig)(_config_5);
    const _config_7 = (0, import_httpAuthSchemeProvider.resolveHttpAuthSchemeConfig)(_config_6);
    const _config_8 = resolveRuntimeExtensions(_config_7, configuration?.extensions || []);
    this.config = _config_8;
    this.middlewareStack.use((0, import_middleware_user_agent.getUserAgentPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_retry.getRetryPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_content_length.getContentLengthPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_host_header.getHostHeaderPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_logger.getLoggerPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_recursion_detection.getRecursionDetectionPlugin)(this.config));
    this.middlewareStack.use(
      (0, import_core.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {
        httpAuthSchemeParametersProvider: import_httpAuthSchemeProvider.defaultTextractHttpAuthSchemeParametersProvider,
        identityProviderConfigProvider: /* @__PURE__ */ __name(async (config) => new import_core.DefaultIdentityProviderConfig({
          "aws.auth#sigv4": config.credentials
        }), "identityProviderConfigProvider")
      })
    );
    this.middlewareStack.use((0, import_core.getHttpSigningPlugin)(this.config));
  }
  /**
   * Destroy underlying resources, like sockets. It's usually not necessary to do this.
   * However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
   * Otherwise, sockets might stay open for quite a long time before the server terminates them.
   */
  destroy() {
    super.destroy();
  }
};

// src/Textract.ts


// src/commands/AnalyzeDocumentCommand.ts

var import_middleware_serde = require("@smithy/middleware-serde");


// src/protocols/Aws_json1_1.ts
var import_core2 = require("@aws-sdk/core");


var import_uuid = require("uuid");

// src/models/TextractServiceException.ts

var TextractServiceException = class _TextractServiceException extends import_smithy_client.ServiceException {
  static {
    __name(this, "TextractServiceException");
  }
  /**
   * @internal
   */
  constructor(options) {
    super(options);
    Object.setPrototypeOf(this, _TextractServiceException.prototype);
  }
};

// src/models/models_0.ts
var AccessDeniedException = class _AccessDeniedException extends TextractServiceException {
  static {
    __name(this, "AccessDeniedException");
  }
  name = "AccessDeniedException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "AccessDeniedException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _AccessDeniedException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var FeatureType = {
  FORMS: "FORMS",
  LAYOUT: "LAYOUT",
  QUERIES: "QUERIES",
  SIGNATURES: "SIGNATURES",
  TABLES: "TABLES"
};
var AdapterVersionStatus = {
  ACTIVE: "ACTIVE",
  AT_RISK: "AT_RISK",
  CREATION_ERROR: "CREATION_ERROR",
  CREATION_IN_PROGRESS: "CREATION_IN_PROGRESS",
  DEPRECATED: "DEPRECATED"
};
var ContentClassifier = {
  FREE_OF_ADULT_CONTENT: "FreeOfAdultContent",
  FREE_OF_PERSONALLY_IDENTIFIABLE_INFORMATION: "FreeOfPersonallyIdentifiableInformation"
};
var BlockType = {
  CELL: "CELL",
  KEY_VALUE_SET: "KEY_VALUE_SET",
  LAYOUT_FIGURE: "LAYOUT_FIGURE",
  LAYOUT_FOOTER: "LAYOUT_FOOTER",
  LAYOUT_HEADER: "LAYOUT_HEADER",
  LAYOUT_KEY_VALUE: "LAYOUT_KEY_VALUE",
  LAYOUT_LIST: "LAYOUT_LIST",
  LAYOUT_PAGE_NUMBER: "LAYOUT_PAGE_NUMBER",
  LAYOUT_SECTION_HEADER: "LAYOUT_SECTION_HEADER",
  LAYOUT_TABLE: "LAYOUT_TABLE",
  LAYOUT_TEXT: "LAYOUT_TEXT",
  LAYOUT_TITLE: "LAYOUT_TITLE",
  LINE: "LINE",
  MERGED_CELL: "MERGED_CELL",
  PAGE: "PAGE",
  QUERY: "QUERY",
  QUERY_RESULT: "QUERY_RESULT",
  SELECTION_ELEMENT: "SELECTION_ELEMENT",
  SIGNATURE: "SIGNATURE",
  TABLE: "TABLE",
  TABLE_FOOTER: "TABLE_FOOTER",
  TABLE_TITLE: "TABLE_TITLE",
  TITLE: "TITLE",
  WORD: "WORD"
};
var EntityType = {
  COLUMN_HEADER: "COLUMN_HEADER",
  KEY: "KEY",
  SEMI_STRUCTURED_TABLE: "SEMI_STRUCTURED_TABLE",
  STRUCTURED_TABLE: "STRUCTURED_TABLE",
  TABLE_FOOTER: "TABLE_FOOTER",
  TABLE_SECTION_TITLE: "TABLE_SECTION_TITLE",
  TABLE_SUMMARY: "TABLE_SUMMARY",
  TABLE_TITLE: "TABLE_TITLE",
  VALUE: "VALUE"
};
var RelationshipType = {
  ANSWER: "ANSWER",
  CHILD: "CHILD",
  COMPLEX_FEATURES: "COMPLEX_FEATURES",
  MERGED_CELL: "MERGED_CELL",
  TABLE: "TABLE",
  TABLE_FOOTER: "TABLE_FOOTER",
  TABLE_TITLE: "TABLE_TITLE",
  TITLE: "TITLE",
  VALUE: "VALUE"
};
var SelectionStatus = {
  NOT_SELECTED: "NOT_SELECTED",
  SELECTED: "SELECTED"
};
var TextType = {
  HANDWRITING: "HANDWRITING",
  PRINTED: "PRINTED"
};
var BadDocumentException = class _BadDocumentException extends TextractServiceException {
  static {
    __name(this, "BadDocumentException");
  }
  name = "BadDocumentException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "BadDocumentException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _BadDocumentException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var DocumentTooLargeException = class _DocumentTooLargeException extends TextractServiceException {
  static {
    __name(this, "DocumentTooLargeException");
  }
  name = "DocumentTooLargeException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "DocumentTooLargeException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _DocumentTooLargeException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var HumanLoopQuotaExceededException = class _HumanLoopQuotaExceededException extends TextractServiceException {
  static {
    __name(this, "HumanLoopQuotaExceededException");
  }
  name = "HumanLoopQuotaExceededException";
  $fault = "client";
  /**
   * <p>The resource type.</p>
   * @public
   */
  ResourceType;
  /**
   * <p>The quota code.</p>
   * @public
   */
  QuotaCode;
  /**
   * <p>The service code.</p>
   * @public
   */
  ServiceCode;
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "HumanLoopQuotaExceededException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _HumanLoopQuotaExceededException.prototype);
    this.ResourceType = opts.ResourceType;
    this.QuotaCode = opts.QuotaCode;
    this.ServiceCode = opts.ServiceCode;
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var InternalServerError = class _InternalServerError extends TextractServiceException {
  static {
    __name(this, "InternalServerError");
  }
  name = "InternalServerError";
  $fault = "server";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InternalServerError",
      $fault: "server",
      ...opts
    });
    Object.setPrototypeOf(this, _InternalServerError.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var InvalidParameterException = class _InvalidParameterException extends TextractServiceException {
  static {
    __name(this, "InvalidParameterException");
  }
  name = "InvalidParameterException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidParameterException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidParameterException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var InvalidS3ObjectException = class _InvalidS3ObjectException extends TextractServiceException {
  static {
    __name(this, "InvalidS3ObjectException");
  }
  name = "InvalidS3ObjectException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidS3ObjectException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidS3ObjectException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var ProvisionedThroughputExceededException = class _ProvisionedThroughputExceededException extends TextractServiceException {
  static {
    __name(this, "ProvisionedThroughputExceededException");
  }
  name = "ProvisionedThroughputExceededException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ProvisionedThroughputExceededException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ProvisionedThroughputExceededException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var ThrottlingException = class _ThrottlingException extends TextractServiceException {
  static {
    __name(this, "ThrottlingException");
  }
  name = "ThrottlingException";
  $fault = "server";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ThrottlingException",
      $fault: "server",
      ...opts
    });
    Object.setPrototypeOf(this, _ThrottlingException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var UnsupportedDocumentException = class _UnsupportedDocumentException extends TextractServiceException {
  static {
    __name(this, "UnsupportedDocumentException");
  }
  name = "UnsupportedDocumentException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "UnsupportedDocumentException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _UnsupportedDocumentException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var ValueType = {
  DATE: "DATE"
};
var AutoUpdate = {
  DISABLED: "DISABLED",
  ENABLED: "ENABLED"
};
var ConflictException = class _ConflictException extends TextractServiceException {
  static {
    __name(this, "ConflictException");
  }
  name = "ConflictException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ConflictException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ConflictException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var IdempotentParameterMismatchException = class _IdempotentParameterMismatchException extends TextractServiceException {
  static {
    __name(this, "IdempotentParameterMismatchException");
  }
  name = "IdempotentParameterMismatchException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "IdempotentParameterMismatchException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _IdempotentParameterMismatchException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var LimitExceededException = class _LimitExceededException extends TextractServiceException {
  static {
    __name(this, "LimitExceededException");
  }
  name = "LimitExceededException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "LimitExceededException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _LimitExceededException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var ServiceQuotaExceededException = class _ServiceQuotaExceededException extends TextractServiceException {
  static {
    __name(this, "ServiceQuotaExceededException");
  }
  name = "ServiceQuotaExceededException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ServiceQuotaExceededException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ServiceQuotaExceededException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var ValidationException = class _ValidationException extends TextractServiceException {
  static {
    __name(this, "ValidationException");
  }
  name = "ValidationException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ValidationException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ValidationException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var InvalidKMSKeyException = class _InvalidKMSKeyException extends TextractServiceException {
  static {
    __name(this, "InvalidKMSKeyException");
  }
  name = "InvalidKMSKeyException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidKMSKeyException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidKMSKeyException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var ResourceNotFoundException = class _ResourceNotFoundException extends TextractServiceException {
  static {
    __name(this, "ResourceNotFoundException");
  }
  name = "ResourceNotFoundException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ResourceNotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ResourceNotFoundException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};
var JobStatus = {
  FAILED: "FAILED",
  IN_PROGRESS: "IN_PROGRESS",
  PARTIAL_SUCCESS: "PARTIAL_SUCCESS",
  SUCCEEDED: "SUCCEEDED"
};
var InvalidJobIdException = class _InvalidJobIdException extends TextractServiceException {
  static {
    __name(this, "InvalidJobIdException");
  }
  name = "InvalidJobIdException";
  $fault = "client";
  Message;
  Code;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InvalidJobIdException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _InvalidJobIdException.prototype);
    this.Message = opts.Message;
    this.Code = opts.Code;
  }
};

// src/protocols/Aws_json1_1.ts
var se_AnalyzeDocumentCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AnalyzeDocument");
  let body;
  body = JSON.stringify(se_AnalyzeDocumentRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AnalyzeDocumentCommand");
var se_AnalyzeExpenseCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AnalyzeExpense");
  let body;
  body = JSON.stringify(se_AnalyzeExpenseRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AnalyzeExpenseCommand");
var se_AnalyzeIDCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("AnalyzeID");
  let body;
  body = JSON.stringify(se_AnalyzeIDRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_AnalyzeIDCommand");
var se_CreateAdapterCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateAdapter");
  let body;
  body = JSON.stringify(se_CreateAdapterRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateAdapterCommand");
var se_CreateAdapterVersionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("CreateAdapterVersion");
  let body;
  body = JSON.stringify(se_CreateAdapterVersionRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_CreateAdapterVersionCommand");
var se_DeleteAdapterCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteAdapter");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteAdapterCommand");
var se_DeleteAdapterVersionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DeleteAdapterVersion");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DeleteAdapterVersionCommand");
var se_DetectDocumentTextCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("DetectDocumentText");
  let body;
  body = JSON.stringify(se_DetectDocumentTextRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_DetectDocumentTextCommand");
var se_GetAdapterCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetAdapter");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetAdapterCommand");
var se_GetAdapterVersionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetAdapterVersion");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetAdapterVersionCommand");
var se_GetDocumentAnalysisCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetDocumentAnalysis");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetDocumentAnalysisCommand");
var se_GetDocumentTextDetectionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetDocumentTextDetection");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetDocumentTextDetectionCommand");
var se_GetExpenseAnalysisCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetExpenseAnalysis");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetExpenseAnalysisCommand");
var se_GetLendingAnalysisCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetLendingAnalysis");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetLendingAnalysisCommand");
var se_GetLendingAnalysisSummaryCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("GetLendingAnalysisSummary");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_GetLendingAnalysisSummaryCommand");
var se_ListAdaptersCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListAdapters");
  let body;
  body = JSON.stringify(se_ListAdaptersRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListAdaptersCommand");
var se_ListAdapterVersionsCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListAdapterVersions");
  let body;
  body = JSON.stringify(se_ListAdapterVersionsRequest(input, context));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListAdapterVersionsCommand");
var se_ListTagsForResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("ListTagsForResource");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_ListTagsForResourceCommand");
var se_StartDocumentAnalysisCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("StartDocumentAnalysis");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_StartDocumentAnalysisCommand");
var se_StartDocumentTextDetectionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("StartDocumentTextDetection");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_StartDocumentTextDetectionCommand");
var se_StartExpenseAnalysisCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("StartExpenseAnalysis");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_StartExpenseAnalysisCommand");
var se_StartLendingAnalysisCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("StartLendingAnalysis");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_StartLendingAnalysisCommand");
var se_TagResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("TagResource");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_TagResourceCommand");
var se_UntagResourceCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UntagResource");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UntagResourceCommand");
var se_UpdateAdapterCommand = /* @__PURE__ */ __name(async (input, context) => {
  const headers = sharedHeaders("UpdateAdapter");
  let body;
  body = JSON.stringify((0, import_smithy_client._json)(input));
  return buildHttpRpcRequest(context, headers, "/", void 0, body);
}, "se_UpdateAdapterCommand");
var de_AnalyzeDocumentCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AnalyzeDocumentResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AnalyzeDocumentCommand");
var de_AnalyzeExpenseCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AnalyzeExpenseResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AnalyzeExpenseCommand");
var de_AnalyzeIDCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_AnalyzeIDResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_AnalyzeIDCommand");
var de_CreateAdapterCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateAdapterCommand");
var de_CreateAdapterVersionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_CreateAdapterVersionCommand");
var de_DeleteAdapterCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DeleteAdapterCommand");
var de_DeleteAdapterVersionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DeleteAdapterVersionCommand");
var de_DetectDocumentTextCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_DetectDocumentTextResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_DetectDocumentTextCommand");
var de_GetAdapterCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetAdapterResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetAdapterCommand");
var de_GetAdapterVersionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetAdapterVersionResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetAdapterVersionCommand");
var de_GetDocumentAnalysisCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetDocumentAnalysisResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetDocumentAnalysisCommand");
var de_GetDocumentTextDetectionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetDocumentTextDetectionResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetDocumentTextDetectionCommand");
var de_GetExpenseAnalysisCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetExpenseAnalysisResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetExpenseAnalysisCommand");
var de_GetLendingAnalysisCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_GetLendingAnalysisResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetLendingAnalysisCommand");
var de_GetLendingAnalysisSummaryCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_GetLendingAnalysisSummaryCommand");
var de_ListAdaptersCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListAdaptersResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListAdaptersCommand");
var de_ListAdapterVersionsCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_ListAdapterVersionsResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListAdapterVersionsCommand");
var de_ListTagsForResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_ListTagsForResourceCommand");
var de_StartDocumentAnalysisCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_StartDocumentAnalysisCommand");
var de_StartDocumentTextDetectionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_StartDocumentTextDetectionCommand");
var de_StartExpenseAnalysisCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_StartExpenseAnalysisCommand");
var de_StartLendingAnalysisCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_StartLendingAnalysisCommand");
var de_TagResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_TagResourceCommand");
var de_UntagResourceCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = (0, import_smithy_client._json)(data);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UntagResourceCommand");
var de_UpdateAdapterCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  let contents = {};
  contents = de_UpdateAdapterResponse(data, context);
  const response = {
    $metadata: deserializeMetadata(output),
    ...contents
  };
  return response;
}, "de_UpdateAdapterCommand");
var de_CommandError = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonErrorBody)(output.body, context)
  };
  const errorCode = (0, import_core2.loadRestJsonErrorCode)(output, parsedOutput.body);
  switch (errorCode) {
    case "AccessDeniedException":
    case "com.amazonaws.textract#AccessDeniedException":
      throw await de_AccessDeniedExceptionRes(parsedOutput, context);
    case "BadDocumentException":
    case "com.amazonaws.textract#BadDocumentException":
      throw await de_BadDocumentExceptionRes(parsedOutput, context);
    case "DocumentTooLargeException":
    case "com.amazonaws.textract#DocumentTooLargeException":
      throw await de_DocumentTooLargeExceptionRes(parsedOutput, context);
    case "HumanLoopQuotaExceededException":
    case "com.amazonaws.textract#HumanLoopQuotaExceededException":
      throw await de_HumanLoopQuotaExceededExceptionRes(parsedOutput, context);
    case "InternalServerError":
    case "com.amazonaws.textract#InternalServerError":
      throw await de_InternalServerErrorRes(parsedOutput, context);
    case "InvalidParameterException":
    case "com.amazonaws.textract#InvalidParameterException":
      throw await de_InvalidParameterExceptionRes(parsedOutput, context);
    case "InvalidS3ObjectException":
    case "com.amazonaws.textract#InvalidS3ObjectException":
      throw await de_InvalidS3ObjectExceptionRes(parsedOutput, context);
    case "ProvisionedThroughputExceededException":
    case "com.amazonaws.textract#ProvisionedThroughputExceededException":
      throw await de_ProvisionedThroughputExceededExceptionRes(parsedOutput, context);
    case "ThrottlingException":
    case "com.amazonaws.textract#ThrottlingException":
      throw await de_ThrottlingExceptionRes(parsedOutput, context);
    case "UnsupportedDocumentException":
    case "com.amazonaws.textract#UnsupportedDocumentException":
      throw await de_UnsupportedDocumentExceptionRes(parsedOutput, context);
    case "ConflictException":
    case "com.amazonaws.textract#ConflictException":
      throw await de_ConflictExceptionRes(parsedOutput, context);
    case "IdempotentParameterMismatchException":
    case "com.amazonaws.textract#IdempotentParameterMismatchException":
      throw await de_IdempotentParameterMismatchExceptionRes(parsedOutput, context);
    case "LimitExceededException":
    case "com.amazonaws.textract#LimitExceededException":
      throw await de_LimitExceededExceptionRes(parsedOutput, context);
    case "ServiceQuotaExceededException":
    case "com.amazonaws.textract#ServiceQuotaExceededException":
      throw await de_ServiceQuotaExceededExceptionRes(parsedOutput, context);
    case "ValidationException":
    case "com.amazonaws.textract#ValidationException":
      throw await de_ValidationExceptionRes(parsedOutput, context);
    case "InvalidKMSKeyException":
    case "com.amazonaws.textract#InvalidKMSKeyException":
      throw await de_InvalidKMSKeyExceptionRes(parsedOutput, context);
    case "ResourceNotFoundException":
    case "com.amazonaws.textract#ResourceNotFoundException":
      throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);
    case "InvalidJobIdException":
    case "com.amazonaws.textract#InvalidJobIdException":
      throw await de_InvalidJobIdExceptionRes(parsedOutput, context);
    default:
      const parsedBody = parsedOutput.body;
      return throwDefaultError({
        output,
        parsedBody,
        errorCode
      });
  }
}, "de_CommandError");
var de_AccessDeniedExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new AccessDeniedException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_AccessDeniedExceptionRes");
var de_BadDocumentExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new BadDocumentException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_BadDocumentExceptionRes");
var de_ConflictExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ConflictException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ConflictExceptionRes");
var de_DocumentTooLargeExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new DocumentTooLargeException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_DocumentTooLargeExceptionRes");
var de_HumanLoopQuotaExceededExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new HumanLoopQuotaExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_HumanLoopQuotaExceededExceptionRes");
var de_IdempotentParameterMismatchExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new IdempotentParameterMismatchException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_IdempotentParameterMismatchExceptionRes");
var de_InternalServerErrorRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InternalServerError({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InternalServerErrorRes");
var de_InvalidJobIdExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidJobIdException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidJobIdExceptionRes");
var de_InvalidKMSKeyExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidKMSKeyException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidKMSKeyExceptionRes");
var de_InvalidParameterExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidParameterException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidParameterExceptionRes");
var de_InvalidS3ObjectExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new InvalidS3ObjectException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_InvalidS3ObjectExceptionRes");
var de_LimitExceededExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new LimitExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_LimitExceededExceptionRes");
var de_ProvisionedThroughputExceededExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ProvisionedThroughputExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ProvisionedThroughputExceededExceptionRes");
var de_ResourceNotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ResourceNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ResourceNotFoundExceptionRes");
var de_ServiceQuotaExceededExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ServiceQuotaExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ServiceQuotaExceededExceptionRes");
var de_ThrottlingExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ThrottlingException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ThrottlingExceptionRes");
var de_UnsupportedDocumentExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new UnsupportedDocumentException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_UnsupportedDocumentExceptionRes");
var de_ValidationExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const body = parsedOutput.body;
  const deserialized = (0, import_smithy_client._json)(body);
  const exception = new ValidationException({
    $metadata: deserializeMetadata(parsedOutput),
    ...deserialized
  });
  return (0, import_smithy_client.decorateServiceException)(exception, body);
}, "de_ValidationExceptionRes");
var se_AnalyzeDocumentRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AdaptersConfig: import_smithy_client._json,
    Document: /* @__PURE__ */ __name((_) => se_Document(_, context), "Document"),
    FeatureTypes: import_smithy_client._json,
    HumanLoopConfig: import_smithy_client._json,
    QueriesConfig: import_smithy_client._json
  });
}, "se_AnalyzeDocumentRequest");
var se_AnalyzeExpenseRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Document: /* @__PURE__ */ __name((_) => se_Document(_, context), "Document")
  });
}, "se_AnalyzeExpenseRequest");
var se_AnalyzeIDRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    DocumentPages: /* @__PURE__ */ __name((_) => se_DocumentPages(_, context), "DocumentPages")
  });
}, "se_AnalyzeIDRequest");
var se_CreateAdapterRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AdapterName: [],
    AutoUpdate: [],
    ClientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
    Description: [],
    FeatureTypes: import_smithy_client._json,
    Tags: import_smithy_client._json
  });
}, "se_CreateAdapterRequest");
var se_CreateAdapterVersionRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AdapterId: [],
    ClientRequestToken: [true, (_) => _ ?? (0, import_uuid.v4)()],
    DatasetConfig: import_smithy_client._json,
    KMSKeyId: [],
    OutputConfig: import_smithy_client._json,
    Tags: import_smithy_client._json
  });
}, "se_CreateAdapterVersionRequest");
var se_DetectDocumentTextRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Document: /* @__PURE__ */ __name((_) => se_Document(_, context), "Document")
  });
}, "se_DetectDocumentTextRequest");
var se_Document = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    Bytes: context.base64Encoder,
    S3Object: import_smithy_client._json
  });
}, "se_Document");
var se_DocumentPages = /* @__PURE__ */ __name((input, context) => {
  return input.filter((e) => e != null).map((entry) => {
    return se_Document(entry, context);
  });
}, "se_DocumentPages");
var se_ListAdaptersRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AfterCreationTime: /* @__PURE__ */ __name((_) => _.getTime() / 1e3, "AfterCreationTime"),
    BeforeCreationTime: /* @__PURE__ */ __name((_) => _.getTime() / 1e3, "BeforeCreationTime"),
    MaxResults: [],
    NextToken: []
  });
}, "se_ListAdaptersRequest");
var se_ListAdapterVersionsRequest = /* @__PURE__ */ __name((input, context) => {
  return (0, import_smithy_client.take)(input, {
    AdapterId: [],
    AfterCreationTime: /* @__PURE__ */ __name((_) => _.getTime() / 1e3, "AfterCreationTime"),
    BeforeCreationTime: /* @__PURE__ */ __name((_) => _.getTime() / 1e3, "BeforeCreationTime"),
    MaxResults: [],
    NextToken: []
  });
}, "se_ListAdapterVersionsRequest");
var de_AdapterList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_AdapterOverview(entry, context);
  });
  return retVal;
}, "de_AdapterList");
var de_AdapterOverview = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AdapterId: import_smithy_client.expectString,
    AdapterName: import_smithy_client.expectString,
    CreationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationTime"),
    FeatureTypes: import_smithy_client._json
  });
}, "de_AdapterOverview");
var de_AdapterVersionEvaluationMetric = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AdapterVersion: /* @__PURE__ */ __name((_) => de_EvaluationMetric(_, context), "AdapterVersion"),
    Baseline: /* @__PURE__ */ __name((_) => de_EvaluationMetric(_, context), "Baseline"),
    FeatureType: import_smithy_client.expectString
  });
}, "de_AdapterVersionEvaluationMetric");
var de_AdapterVersionEvaluationMetrics = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_AdapterVersionEvaluationMetric(entry, context);
  });
  return retVal;
}, "de_AdapterVersionEvaluationMetrics");
var de_AdapterVersionList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_AdapterVersionOverview(entry, context);
  });
  return retVal;
}, "de_AdapterVersionList");
var de_AdapterVersionOverview = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AdapterId: import_smithy_client.expectString,
    AdapterVersion: import_smithy_client.expectString,
    CreationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationTime"),
    FeatureTypes: import_smithy_client._json,
    Status: import_smithy_client.expectString,
    StatusMessage: import_smithy_client.expectString
  });
}, "de_AdapterVersionOverview");
var de_AnalyzeDocumentResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AnalyzeDocumentModelVersion: import_smithy_client.expectString,
    Blocks: /* @__PURE__ */ __name((_) => de_BlockList(_, context), "Blocks"),
    DocumentMetadata: import_smithy_client._json,
    HumanLoopActivationOutput: /* @__PURE__ */ __name((_) => de_HumanLoopActivationOutput(_, context), "HumanLoopActivationOutput")
  });
}, "de_AnalyzeDocumentResponse");
var de_AnalyzeExpenseResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    DocumentMetadata: import_smithy_client._json,
    ExpenseDocuments: /* @__PURE__ */ __name((_) => de_ExpenseDocumentList(_, context), "ExpenseDocuments")
  });
}, "de_AnalyzeExpenseResponse");
var de_AnalyzeIDDetections = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Confidence: import_smithy_client.limitedParseFloat32,
    NormalizedValue: import_smithy_client._json,
    Text: import_smithy_client.expectString
  });
}, "de_AnalyzeIDDetections");
var de_AnalyzeIDResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AnalyzeIDModelVersion: import_smithy_client.expectString,
    DocumentMetadata: import_smithy_client._json,
    IdentityDocuments: /* @__PURE__ */ __name((_) => de_IdentityDocumentList(_, context), "IdentityDocuments")
  });
}, "de_AnalyzeIDResponse");
var de_Block = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    BlockType: import_smithy_client.expectString,
    ColumnIndex: import_smithy_client.expectInt32,
    ColumnSpan: import_smithy_client.expectInt32,
    Confidence: import_smithy_client.limitedParseFloat32,
    EntityTypes: import_smithy_client._json,
    Geometry: /* @__PURE__ */ __name((_) => de_Geometry(_, context), "Geometry"),
    Id: import_smithy_client.expectString,
    Page: import_smithy_client.expectInt32,
    Query: import_smithy_client._json,
    Relationships: import_smithy_client._json,
    RowIndex: import_smithy_client.expectInt32,
    RowSpan: import_smithy_client.expectInt32,
    SelectionStatus: import_smithy_client.expectString,
    Text: import_smithy_client.expectString,
    TextType: import_smithy_client.expectString
  });
}, "de_Block");
var de_BlockList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Block(entry, context);
  });
  return retVal;
}, "de_BlockList");
var de_BoundingBox = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Height: import_smithy_client.limitedParseFloat32,
    Left: import_smithy_client.limitedParseFloat32,
    Top: import_smithy_client.limitedParseFloat32,
    Width: import_smithy_client.limitedParseFloat32
  });
}, "de_BoundingBox");
var de_DetectDocumentTextResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Blocks: /* @__PURE__ */ __name((_) => de_BlockList(_, context), "Blocks"),
    DetectDocumentTextModelVersion: import_smithy_client.expectString,
    DocumentMetadata: import_smithy_client._json
  });
}, "de_DetectDocumentTextResponse");
var de_EvaluationMetric = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    F1Score: import_smithy_client.limitedParseFloat32,
    Precision: import_smithy_client.limitedParseFloat32,
    Recall: import_smithy_client.limitedParseFloat32
  });
}, "de_EvaluationMetric");
var de_ExpenseCurrency = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Code: import_smithy_client.expectString,
    Confidence: import_smithy_client.limitedParseFloat32
  });
}, "de_ExpenseCurrency");
var de_ExpenseDetection = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Confidence: import_smithy_client.limitedParseFloat32,
    Geometry: /* @__PURE__ */ __name((_) => de_Geometry(_, context), "Geometry"),
    Text: import_smithy_client.expectString
  });
}, "de_ExpenseDetection");
var de_ExpenseDocument = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Blocks: /* @__PURE__ */ __name((_) => de_BlockList(_, context), "Blocks"),
    ExpenseIndex: import_smithy_client.expectInt32,
    LineItemGroups: /* @__PURE__ */ __name((_) => de_LineItemGroupList(_, context), "LineItemGroups"),
    SummaryFields: /* @__PURE__ */ __name((_) => de_ExpenseFieldList(_, context), "SummaryFields")
  });
}, "de_ExpenseDocument");
var de_ExpenseDocumentList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ExpenseDocument(entry, context);
  });
  return retVal;
}, "de_ExpenseDocumentList");
var de_ExpenseField = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Currency: /* @__PURE__ */ __name((_) => de_ExpenseCurrency(_, context), "Currency"),
    GroupProperties: import_smithy_client._json,
    LabelDetection: /* @__PURE__ */ __name((_) => de_ExpenseDetection(_, context), "LabelDetection"),
    PageNumber: import_smithy_client.expectInt32,
    Type: /* @__PURE__ */ __name((_) => de_ExpenseType(_, context), "Type"),
    ValueDetection: /* @__PURE__ */ __name((_) => de_ExpenseDetection(_, context), "ValueDetection")
  });
}, "de_ExpenseField");
var de_ExpenseFieldList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_ExpenseField(entry, context);
  });
  return retVal;
}, "de_ExpenseFieldList");
var de_ExpenseType = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Confidence: import_smithy_client.limitedParseFloat32,
    Text: import_smithy_client.expectString
  });
}, "de_ExpenseType");
var de_Extraction = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    ExpenseDocument: /* @__PURE__ */ __name((_) => de_ExpenseDocument(_, context), "ExpenseDocument"),
    IdentityDocument: /* @__PURE__ */ __name((_) => de_IdentityDocument(_, context), "IdentityDocument"),
    LendingDocument: /* @__PURE__ */ __name((_) => de_LendingDocument(_, context), "LendingDocument")
  });
}, "de_Extraction");
var de_ExtractionList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Extraction(entry, context);
  });
  return retVal;
}, "de_ExtractionList");
var de_Geometry = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    BoundingBox: /* @__PURE__ */ __name((_) => de_BoundingBox(_, context), "BoundingBox"),
    Polygon: /* @__PURE__ */ __name((_) => de_Polygon(_, context), "Polygon"),
    RotationAngle: import_smithy_client.limitedParseFloat32
  });
}, "de_Geometry");
var de_GetAdapterResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AdapterId: import_smithy_client.expectString,
    AdapterName: import_smithy_client.expectString,
    AutoUpdate: import_smithy_client.expectString,
    CreationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationTime"),
    Description: import_smithy_client.expectString,
    FeatureTypes: import_smithy_client._json,
    Tags: import_smithy_client._json
  });
}, "de_GetAdapterResponse");
var de_GetAdapterVersionResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AdapterId: import_smithy_client.expectString,
    AdapterVersion: import_smithy_client.expectString,
    CreationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationTime"),
    DatasetConfig: import_smithy_client._json,
    EvaluationMetrics: /* @__PURE__ */ __name((_) => de_AdapterVersionEvaluationMetrics(_, context), "EvaluationMetrics"),
    FeatureTypes: import_smithy_client._json,
    KMSKeyId: import_smithy_client.expectString,
    OutputConfig: import_smithy_client._json,
    Status: import_smithy_client.expectString,
    StatusMessage: import_smithy_client.expectString,
    Tags: import_smithy_client._json
  });
}, "de_GetAdapterVersionResponse");
var de_GetDocumentAnalysisResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AnalyzeDocumentModelVersion: import_smithy_client.expectString,
    Blocks: /* @__PURE__ */ __name((_) => de_BlockList(_, context), "Blocks"),
    DocumentMetadata: import_smithy_client._json,
    JobStatus: import_smithy_client.expectString,
    NextToken: import_smithy_client.expectString,
    StatusMessage: import_smithy_client.expectString,
    Warnings: import_smithy_client._json
  });
}, "de_GetDocumentAnalysisResponse");
var de_GetDocumentTextDetectionResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Blocks: /* @__PURE__ */ __name((_) => de_BlockList(_, context), "Blocks"),
    DetectDocumentTextModelVersion: import_smithy_client.expectString,
    DocumentMetadata: import_smithy_client._json,
    JobStatus: import_smithy_client.expectString,
    NextToken: import_smithy_client.expectString,
    StatusMessage: import_smithy_client.expectString,
    Warnings: import_smithy_client._json
  });
}, "de_GetDocumentTextDetectionResponse");
var de_GetExpenseAnalysisResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AnalyzeExpenseModelVersion: import_smithy_client.expectString,
    DocumentMetadata: import_smithy_client._json,
    ExpenseDocuments: /* @__PURE__ */ __name((_) => de_ExpenseDocumentList(_, context), "ExpenseDocuments"),
    JobStatus: import_smithy_client.expectString,
    NextToken: import_smithy_client.expectString,
    StatusMessage: import_smithy_client.expectString,
    Warnings: import_smithy_client._json
  });
}, "de_GetExpenseAnalysisResponse");
var de_GetLendingAnalysisResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AnalyzeLendingModelVersion: import_smithy_client.expectString,
    DocumentMetadata: import_smithy_client._json,
    JobStatus: import_smithy_client.expectString,
    NextToken: import_smithy_client.expectString,
    Results: /* @__PURE__ */ __name((_) => de_LendingResultList(_, context), "Results"),
    StatusMessage: import_smithy_client.expectString,
    Warnings: import_smithy_client._json
  });
}, "de_GetLendingAnalysisResponse");
var de_HumanLoopActivationOutput = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    HumanLoopActivationConditionsEvaluationResults: import_smithy_client.LazyJsonString.from,
    HumanLoopActivationReasons: import_smithy_client._json,
    HumanLoopArn: import_smithy_client.expectString
  });
}, "de_HumanLoopActivationOutput");
var de_IdentityDocument = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Blocks: /* @__PURE__ */ __name((_) => de_BlockList(_, context), "Blocks"),
    DocumentIndex: import_smithy_client.expectInt32,
    IdentityDocumentFields: /* @__PURE__ */ __name((_) => de_IdentityDocumentFieldList(_, context), "IdentityDocumentFields")
  });
}, "de_IdentityDocument");
var de_IdentityDocumentField = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Type: /* @__PURE__ */ __name((_) => de_AnalyzeIDDetections(_, context), "Type"),
    ValueDetection: /* @__PURE__ */ __name((_) => de_AnalyzeIDDetections(_, context), "ValueDetection")
  });
}, "de_IdentityDocumentField");
var de_IdentityDocumentFieldList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_IdentityDocumentField(entry, context);
  });
  return retVal;
}, "de_IdentityDocumentFieldList");
var de_IdentityDocumentList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_IdentityDocument(entry, context);
  });
  return retVal;
}, "de_IdentityDocumentList");
var de_LendingDetection = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Confidence: import_smithy_client.limitedParseFloat32,
    Geometry: /* @__PURE__ */ __name((_) => de_Geometry(_, context), "Geometry"),
    SelectionStatus: import_smithy_client.expectString,
    Text: import_smithy_client.expectString
  });
}, "de_LendingDetection");
var de_LendingDetectionList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_LendingDetection(entry, context);
  });
  return retVal;
}, "de_LendingDetectionList");
var de_LendingDocument = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    LendingFields: /* @__PURE__ */ __name((_) => de_LendingFieldList(_, context), "LendingFields"),
    SignatureDetections: /* @__PURE__ */ __name((_) => de_SignatureDetectionList(_, context), "SignatureDetections")
  });
}, "de_LendingDocument");
var de_LendingField = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    KeyDetection: /* @__PURE__ */ __name((_) => de_LendingDetection(_, context), "KeyDetection"),
    Type: import_smithy_client.expectString,
    ValueDetections: /* @__PURE__ */ __name((_) => de_LendingDetectionList(_, context), "ValueDetections")
  });
}, "de_LendingField");
var de_LendingFieldList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_LendingField(entry, context);
  });
  return retVal;
}, "de_LendingFieldList");
var de_LendingResult = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Extractions: /* @__PURE__ */ __name((_) => de_ExtractionList(_, context), "Extractions"),
    Page: import_smithy_client.expectInt32,
    PageClassification: /* @__PURE__ */ __name((_) => de_PageClassification(_, context), "PageClassification")
  });
}, "de_LendingResult");
var de_LendingResultList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_LendingResult(entry, context);
  });
  return retVal;
}, "de_LendingResultList");
var de_LineItemFields = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    LineItemExpenseFields: /* @__PURE__ */ __name((_) => de_ExpenseFieldList(_, context), "LineItemExpenseFields")
  });
}, "de_LineItemFields");
var de_LineItemGroup = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    LineItemGroupIndex: import_smithy_client.expectInt32,
    LineItems: /* @__PURE__ */ __name((_) => de_LineItemList(_, context), "LineItems")
  });
}, "de_LineItemGroup");
var de_LineItemGroupList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_LineItemGroup(entry, context);
  });
  return retVal;
}, "de_LineItemGroupList");
var de_LineItemList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_LineItemFields(entry, context);
  });
  return retVal;
}, "de_LineItemList");
var de_ListAdaptersResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Adapters: /* @__PURE__ */ __name((_) => de_AdapterList(_, context), "Adapters"),
    NextToken: import_smithy_client.expectString
  });
}, "de_ListAdaptersResponse");
var de_ListAdapterVersionsResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AdapterVersions: /* @__PURE__ */ __name((_) => de_AdapterVersionList(_, context), "AdapterVersions"),
    NextToken: import_smithy_client.expectString
  });
}, "de_ListAdapterVersionsResponse");
var de_PageClassification = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    PageNumber: /* @__PURE__ */ __name((_) => de_PredictionList(_, context), "PageNumber"),
    PageType: /* @__PURE__ */ __name((_) => de_PredictionList(_, context), "PageType")
  });
}, "de_PageClassification");
var de_Point = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    X: import_smithy_client.limitedParseFloat32,
    Y: import_smithy_client.limitedParseFloat32
  });
}, "de_Point");
var de_Polygon = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Point(entry, context);
  });
  return retVal;
}, "de_Polygon");
var de_Prediction = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Confidence: import_smithy_client.limitedParseFloat32,
    Value: import_smithy_client.expectString
  });
}, "de_Prediction");
var de_PredictionList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Prediction(entry, context);
  });
  return retVal;
}, "de_PredictionList");
var de_SignatureDetection = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Confidence: import_smithy_client.limitedParseFloat32,
    Geometry: /* @__PURE__ */ __name((_) => de_Geometry(_, context), "Geometry")
  });
}, "de_SignatureDetection");
var de_SignatureDetectionList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_SignatureDetection(entry, context);
  });
  return retVal;
}, "de_SignatureDetectionList");
var de_UpdateAdapterResponse = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    AdapterId: import_smithy_client.expectString,
    AdapterName: import_smithy_client.expectString,
    AutoUpdate: import_smithy_client.expectString,
    CreationTime: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "CreationTime"),
    Description: import_smithy_client.expectString,
    FeatureTypes: import_smithy_client._json
  });
}, "de_UpdateAdapterResponse");
var deserializeMetadata = /* @__PURE__ */ __name((output) => ({
  httpStatusCode: output.statusCode,
  requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
  extendedRequestId: output.headers["x-amz-id-2"],
  cfId: output.headers["x-amz-cf-id"]
}), "deserializeMetadata");
var throwDefaultError = (0, import_smithy_client.withBaseException)(TextractServiceException);
var buildHttpRpcRequest = /* @__PURE__ */ __name(async (context, headers, path, resolvedHostname, body) => {
  const { hostname, protocol = "https", port, path: basePath } = await context.endpoint();
  const contents = {
    protocol,
    hostname,
    port,
    method: "POST",
    path: basePath.endsWith("/") ? basePath.slice(0, -1) + path : basePath + path,
    headers
  };
  if (resolvedHostname !== void 0) {
    contents.hostname = resolvedHostname;
  }
  if (body !== void 0) {
    contents.body = body;
  }
  return new import_protocol_http.HttpRequest(contents);
}, "buildHttpRpcRequest");
function sharedHeaders(operation) {
  return {
    "content-type": "application/x-amz-json-1.1",
    "x-amz-target": `Textract.${operation}`
  };
}
__name(sharedHeaders, "sharedHeaders");

// src/commands/AnalyzeDocumentCommand.ts
var AnalyzeDocumentCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "AnalyzeDocument", {}).n("TextractClient", "AnalyzeDocumentCommand").f(void 0, void 0).ser(se_AnalyzeDocumentCommand).de(de_AnalyzeDocumentCommand).build() {
  static {
    __name(this, "AnalyzeDocumentCommand");
  }
};

// src/commands/AnalyzeExpenseCommand.ts



var AnalyzeExpenseCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "AnalyzeExpense", {}).n("TextractClient", "AnalyzeExpenseCommand").f(void 0, void 0).ser(se_AnalyzeExpenseCommand).de(de_AnalyzeExpenseCommand).build() {
  static {
    __name(this, "AnalyzeExpenseCommand");
  }
};

// src/commands/AnalyzeIDCommand.ts



var AnalyzeIDCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "AnalyzeID", {}).n("TextractClient", "AnalyzeIDCommand").f(void 0, void 0).ser(se_AnalyzeIDCommand).de(de_AnalyzeIDCommand).build() {
  static {
    __name(this, "AnalyzeIDCommand");
  }
};

// src/commands/CreateAdapterCommand.ts



var CreateAdapterCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "CreateAdapter", {}).n("TextractClient", "CreateAdapterCommand").f(void 0, void 0).ser(se_CreateAdapterCommand).de(de_CreateAdapterCommand).build() {
  static {
    __name(this, "CreateAdapterCommand");
  }
};

// src/commands/CreateAdapterVersionCommand.ts



var CreateAdapterVersionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "CreateAdapterVersion", {}).n("TextractClient", "CreateAdapterVersionCommand").f(void 0, void 0).ser(se_CreateAdapterVersionCommand).de(de_CreateAdapterVersionCommand).build() {
  static {
    __name(this, "CreateAdapterVersionCommand");
  }
};

// src/commands/DeleteAdapterCommand.ts



var DeleteAdapterCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "DeleteAdapter", {}).n("TextractClient", "DeleteAdapterCommand").f(void 0, void 0).ser(se_DeleteAdapterCommand).de(de_DeleteAdapterCommand).build() {
  static {
    __name(this, "DeleteAdapterCommand");
  }
};

// src/commands/DeleteAdapterVersionCommand.ts



var DeleteAdapterVersionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "DeleteAdapterVersion", {}).n("TextractClient", "DeleteAdapterVersionCommand").f(void 0, void 0).ser(se_DeleteAdapterVersionCommand).de(de_DeleteAdapterVersionCommand).build() {
  static {
    __name(this, "DeleteAdapterVersionCommand");
  }
};

// src/commands/DetectDocumentTextCommand.ts



var DetectDocumentTextCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "DetectDocumentText", {}).n("TextractClient", "DetectDocumentTextCommand").f(void 0, void 0).ser(se_DetectDocumentTextCommand).de(de_DetectDocumentTextCommand).build() {
  static {
    __name(this, "DetectDocumentTextCommand");
  }
};

// src/commands/GetAdapterCommand.ts



var GetAdapterCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "GetAdapter", {}).n("TextractClient", "GetAdapterCommand").f(void 0, void 0).ser(se_GetAdapterCommand).de(de_GetAdapterCommand).build() {
  static {
    __name(this, "GetAdapterCommand");
  }
};

// src/commands/GetAdapterVersionCommand.ts



var GetAdapterVersionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "GetAdapterVersion", {}).n("TextractClient", "GetAdapterVersionCommand").f(void 0, void 0).ser(se_GetAdapterVersionCommand).de(de_GetAdapterVersionCommand).build() {
  static {
    __name(this, "GetAdapterVersionCommand");
  }
};

// src/commands/GetDocumentAnalysisCommand.ts



var GetDocumentAnalysisCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "GetDocumentAnalysis", {}).n("TextractClient", "GetDocumentAnalysisCommand").f(void 0, void 0).ser(se_GetDocumentAnalysisCommand).de(de_GetDocumentAnalysisCommand).build() {
  static {
    __name(this, "GetDocumentAnalysisCommand");
  }
};

// src/commands/GetDocumentTextDetectionCommand.ts



var GetDocumentTextDetectionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "GetDocumentTextDetection", {}).n("TextractClient", "GetDocumentTextDetectionCommand").f(void 0, void 0).ser(se_GetDocumentTextDetectionCommand).de(de_GetDocumentTextDetectionCommand).build() {
  static {
    __name(this, "GetDocumentTextDetectionCommand");
  }
};

// src/commands/GetExpenseAnalysisCommand.ts



var GetExpenseAnalysisCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "GetExpenseAnalysis", {}).n("TextractClient", "GetExpenseAnalysisCommand").f(void 0, void 0).ser(se_GetExpenseAnalysisCommand).de(de_GetExpenseAnalysisCommand).build() {
  static {
    __name(this, "GetExpenseAnalysisCommand");
  }
};

// src/commands/GetLendingAnalysisCommand.ts



var GetLendingAnalysisCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "GetLendingAnalysis", {}).n("TextractClient", "GetLendingAnalysisCommand").f(void 0, void 0).ser(se_GetLendingAnalysisCommand).de(de_GetLendingAnalysisCommand).build() {
  static {
    __name(this, "GetLendingAnalysisCommand");
  }
};

// src/commands/GetLendingAnalysisSummaryCommand.ts



var GetLendingAnalysisSummaryCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "GetLendingAnalysisSummary", {}).n("TextractClient", "GetLendingAnalysisSummaryCommand").f(void 0, void 0).ser(se_GetLendingAnalysisSummaryCommand).de(de_GetLendingAnalysisSummaryCommand).build() {
  static {
    __name(this, "GetLendingAnalysisSummaryCommand");
  }
};

// src/commands/ListAdaptersCommand.ts



var ListAdaptersCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "ListAdapters", {}).n("TextractClient", "ListAdaptersCommand").f(void 0, void 0).ser(se_ListAdaptersCommand).de(de_ListAdaptersCommand).build() {
  static {
    __name(this, "ListAdaptersCommand");
  }
};

// src/commands/ListAdapterVersionsCommand.ts



var ListAdapterVersionsCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "ListAdapterVersions", {}).n("TextractClient", "ListAdapterVersionsCommand").f(void 0, void 0).ser(se_ListAdapterVersionsCommand).de(de_ListAdapterVersionsCommand).build() {
  static {
    __name(this, "ListAdapterVersionsCommand");
  }
};

// src/commands/ListTagsForResourceCommand.ts



var ListTagsForResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "ListTagsForResource", {}).n("TextractClient", "ListTagsForResourceCommand").f(void 0, void 0).ser(se_ListTagsForResourceCommand).de(de_ListTagsForResourceCommand).build() {
  static {
    __name(this, "ListTagsForResourceCommand");
  }
};

// src/commands/StartDocumentAnalysisCommand.ts



var StartDocumentAnalysisCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "StartDocumentAnalysis", {}).n("TextractClient", "StartDocumentAnalysisCommand").f(void 0, void 0).ser(se_StartDocumentAnalysisCommand).de(de_StartDocumentAnalysisCommand).build() {
  static {
    __name(this, "StartDocumentAnalysisCommand");
  }
};

// src/commands/StartDocumentTextDetectionCommand.ts



var StartDocumentTextDetectionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "StartDocumentTextDetection", {}).n("TextractClient", "StartDocumentTextDetectionCommand").f(void 0, void 0).ser(se_StartDocumentTextDetectionCommand).de(de_StartDocumentTextDetectionCommand).build() {
  static {
    __name(this, "StartDocumentTextDetectionCommand");
  }
};

// src/commands/StartExpenseAnalysisCommand.ts



var StartExpenseAnalysisCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "StartExpenseAnalysis", {}).n("TextractClient", "StartExpenseAnalysisCommand").f(void 0, void 0).ser(se_StartExpenseAnalysisCommand).de(de_StartExpenseAnalysisCommand).build() {
  static {
    __name(this, "StartExpenseAnalysisCommand");
  }
};

// src/commands/StartLendingAnalysisCommand.ts



var StartLendingAnalysisCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "StartLendingAnalysis", {}).n("TextractClient", "StartLendingAnalysisCommand").f(void 0, void 0).ser(se_StartLendingAnalysisCommand).de(de_StartLendingAnalysisCommand).build() {
  static {
    __name(this, "StartLendingAnalysisCommand");
  }
};

// src/commands/TagResourceCommand.ts



var TagResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "TagResource", {}).n("TextractClient", "TagResourceCommand").f(void 0, void 0).ser(se_TagResourceCommand).de(de_TagResourceCommand).build() {
  static {
    __name(this, "TagResourceCommand");
  }
};

// src/commands/UntagResourceCommand.ts



var UntagResourceCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "UntagResource", {}).n("TextractClient", "UntagResourceCommand").f(void 0, void 0).ser(se_UntagResourceCommand).de(de_UntagResourceCommand).build() {
  static {
    __name(this, "UntagResourceCommand");
  }
};

// src/commands/UpdateAdapterCommand.ts



var UpdateAdapterCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Textract", "UpdateAdapter", {}).n("TextractClient", "UpdateAdapterCommand").f(void 0, void 0).ser(se_UpdateAdapterCommand).de(de_UpdateAdapterCommand).build() {
  static {
    __name(this, "UpdateAdapterCommand");
  }
};

// src/Textract.ts
var commands = {
  AnalyzeDocumentCommand,
  AnalyzeExpenseCommand,
  AnalyzeIDCommand,
  CreateAdapterCommand,
  CreateAdapterVersionCommand,
  DeleteAdapterCommand,
  DeleteAdapterVersionCommand,
  DetectDocumentTextCommand,
  GetAdapterCommand,
  GetAdapterVersionCommand,
  GetDocumentAnalysisCommand,
  GetDocumentTextDetectionCommand,
  GetExpenseAnalysisCommand,
  GetLendingAnalysisCommand,
  GetLendingAnalysisSummaryCommand,
  ListAdaptersCommand,
  ListAdapterVersionsCommand,
  ListTagsForResourceCommand,
  StartDocumentAnalysisCommand,
  StartDocumentTextDetectionCommand,
  StartExpenseAnalysisCommand,
  StartLendingAnalysisCommand,
  TagResourceCommand,
  UntagResourceCommand,
  UpdateAdapterCommand
};
var Textract = class extends TextractClient {
  static {
    __name(this, "Textract");
  }
};
(0, import_smithy_client.createAggregatedClient)(commands, Textract);

// src/pagination/ListAdapterVersionsPaginator.ts

var paginateListAdapterVersions = (0, import_core.createPaginator)(TextractClient, ListAdapterVersionsCommand, "NextToken", "NextToken", "MaxResults");

// src/pagination/ListAdaptersPaginator.ts

var paginateListAdapters = (0, import_core.createPaginator)(TextractClient, ListAdaptersCommand, "NextToken", "NextToken", "MaxResults");
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  TextractServiceException,
  __Client,
  TextractClient,
  Textract,
  $Command,
  AnalyzeDocumentCommand,
  AnalyzeExpenseCommand,
  AnalyzeIDCommand,
  CreateAdapterCommand,
  CreateAdapterVersionCommand,
  DeleteAdapterCommand,
  DeleteAdapterVersionCommand,
  DetectDocumentTextCommand,
  GetAdapterCommand,
  GetAdapterVersionCommand,
  GetDocumentAnalysisCommand,
  GetDocumentTextDetectionCommand,
  GetExpenseAnalysisCommand,
  GetLendingAnalysisCommand,
  GetLendingAnalysisSummaryCommand,
  ListAdapterVersionsCommand,
  ListAdaptersCommand,
  ListTagsForResourceCommand,
  StartDocumentAnalysisCommand,
  StartDocumentTextDetectionCommand,
  StartExpenseAnalysisCommand,
  StartLendingAnalysisCommand,
  TagResourceCommand,
  UntagResourceCommand,
  UpdateAdapterCommand,
  paginateListAdapterVersions,
  paginateListAdapters,
  AccessDeniedException,
  FeatureType,
  AdapterVersionStatus,
  ContentClassifier,
  BlockType,
  EntityType,
  RelationshipType,
  SelectionStatus,
  TextType,
  BadDocumentException,
  DocumentTooLargeException,
  HumanLoopQuotaExceededException,
  InternalServerError,
  InvalidParameterException,
  InvalidS3ObjectException,
  ProvisionedThroughputExceededException,
  ThrottlingException,
  UnsupportedDocumentException,
  ValueType,
  AutoUpdate,
  ConflictException,
  IdempotentParameterMismatchException,
  LimitExceededException,
  ServiceQuotaExceededException,
  ValidationException,
  InvalidKMSKeyException,
  ResourceNotFoundException,
  JobStatus,
  InvalidJobIdException
});

