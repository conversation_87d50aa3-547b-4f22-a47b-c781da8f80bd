import {
  DeserializeMiddleware,
  Pluggable,
  RelativeMiddlewareOptions,
} from "@smithy/types";
interface PreviouslyResolved {}
export declare const s3ExpiresMiddleware: (
  config: PreviouslyResolved
) => DeserializeMiddleware<any, any>;
export declare const s3ExpiresMiddlewareOptions: RelativeMiddlewareOptions;
export declare const getS3ExpiresMiddlewarePlugin: (
  clientConfig: PreviouslyResolved
) => Pluggable<any, any>;
export {};
