/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * The granularity of the timestamps in the transcription. 'word' provides word-level timestamps and 'character' provides character-level timestamps per word.
 */
export type SpeechToTextConvertRequestTimestampsGranularity = "none" | "word" | "character";
export declare const SpeechToTextConvertRequestTimestampsGranularity: {
    readonly None: "none";
    readonly Word: "word";
    readonly Character: "character";
};
