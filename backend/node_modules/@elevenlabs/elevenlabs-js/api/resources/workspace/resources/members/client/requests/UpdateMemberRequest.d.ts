/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../../../../index";
/**
 * @example
 *     {
 *         email: "email"
 *     }
 */
export interface UpdateMemberRequest {
    /** Email of the target user. */
    email: string;
    /** Whether to lock or unlock the user account. */
    isLocked?: boolean;
    /** Role dictating permissions in the workspace. */
    workspaceRole?: ElevenLabs.workspace.BodyUpdateMemberV1WorkspaceMembersPostWorkspaceRole;
}
