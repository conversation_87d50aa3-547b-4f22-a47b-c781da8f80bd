/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface ResourceMetadataResponseModel {
    /** The ID of the resource. */
    resourceId: string;
    /** The type of the resource. */
    resourceType: ElevenLabs.WorkspaceResourceType;
    /** The ID of the user who created the resource. */
    creatorUserId?: string;
    /** A mapping of roles to group IDs. When the resource is shared with a user, the group id is the user's id. */
    roleToGroupIds: Record<string, string[]>;
    /** List of options for sharing the resource further in the workspace. These are users who don't have access to the resource yet. */
    shareOptions: ElevenLabs.ShareOptionResponseModel[];
}
