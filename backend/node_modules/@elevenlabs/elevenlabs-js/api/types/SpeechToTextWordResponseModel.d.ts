/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * Word-level detail of the transcription with timing information.
 */
export interface SpeechToTextWordResponseModel {
    /** The word or sound that was transcribed. */
    text: string;
    /** The start time of the word or sound in seconds. */
    start?: number;
    /** The end time of the word or sound in seconds. */
    end?: number;
    /** The type of the word or sound. 'audio_event' is used for non-word sounds like laughter or footsteps. */
    type: ElevenLabs.SpeechToTextWordResponseModelType;
    /** Unique identifier for the speaker of this word. */
    speakerId?: string;
    /** The log of the probability with which this word was predicted. Logprobs are in range [-infinity, 0], higher logprobs indicate a higher confidence the model has in its predictions. */
    logprob: number;
    /** The characters that make up the word and their timing information. */
    characters?: ElevenLabs.SpeechToTextCharacterResponseModel[];
}
