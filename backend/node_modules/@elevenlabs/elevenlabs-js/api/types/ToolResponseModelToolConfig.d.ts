/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * The type of tool
 */
export type ToolResponseModelToolConfig = ElevenLabs.ToolResponseModelToolConfig.Client | ElevenLabs.ToolResponseModelToolConfig.Mcp | ElevenLabs.ToolResponseModelToolConfig.System | ElevenLabs.ToolResponseModelToolConfig.Webhook;
export declare namespace ToolResponseModelToolConfig {
    interface Client extends ElevenLabs.ClientToolConfigOutput {
        type: "client";
    }
    interface Mcp extends ElevenLabs.McpToolConfigOutput {
        type: "mcp";
    }
    interface System extends ElevenLabs.SystemToolConfigOutput {
        type: "system";
    }
    interface Webhook extends ElevenLabs.WebhookToolConfigOutput {
        type: "webhook";
    }
}
