/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface VoiceSharingModerationCheckResponseModel {
    /** The date the moderation check was made in Unix time. */
    dateCheckedUnix?: number;
    /** The name value of the voice. */
    nameValue?: string;
    /** Whether the name check was successful. */
    nameCheck?: boolean;
    /** The description value of the voice. */
    descriptionValue?: string;
    /** Whether the description check was successful. */
    descriptionCheck?: boolean;
    /** A list of sample IDs. */
    sampleIds?: string[];
    /** A list of sample checks. */
    sampleChecks?: number[];
    /** A list of captcha IDs. */
    captchaIds?: string[];
    /** A list of CAPTCHA check values. */
    captchaChecks?: number[];
}
