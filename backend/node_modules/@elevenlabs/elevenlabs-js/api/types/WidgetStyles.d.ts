/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface WidgetStyles {
    /** The base background color. */
    base?: string;
    /** The color of the base background when hovered. */
    baseHover?: string;
    /** The color of the base background when active (clicked). */
    baseActive?: string;
    /** The color of the border against the base background. */
    baseBorder?: string;
    /** The color of subtle text against the base background. */
    baseSubtle?: string;
    /** The color of primary text against the base background. */
    basePrimary?: string;
    /** The color of error text against the base background. */
    baseError?: string;
    /** The accent background color. */
    accent?: string;
    /** The color of the accent background when hovered. */
    accentHover?: string;
    /** The color of the accent background when active (clicked). */
    accentActive?: string;
    /** The color of the border against the accent background. */
    accentBorder?: string;
    /** The color of subtle text against the accent background. */
    accentSubtle?: string;
    /** The color of primary text against the accent background. */
    accentPrimary?: string;
    /** The padding around the edges of the viewport. */
    overlayPadding?: number;
    /** The radius of the buttons. */
    buttonRadius?: number;
    /** The radius of the input fields. */
    inputRadius?: number;
    /** The radius of the chat bubbles. */
    bubbleRadius?: number;
    /** The default radius of sheets. */
    sheetRadius?: number;
    /** The radius of the sheet in compact mode. */
    compactSheetRadius?: number;
    /** The radius of the dropdown sheet. */
    dropdownSheetRadius?: number;
}
