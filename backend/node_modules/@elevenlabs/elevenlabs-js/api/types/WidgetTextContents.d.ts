/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface WidgetTextContents {
    /** Call to action displayed inside the compact and full variants. */
    mainLabel?: string;
    /** Text and ARIA label for the start call button. */
    startCall?: string;
    /** Text and ARIA label for the new call button. Displayed when the caller already finished at least one call in order ot start the next one. */
    newCall?: string;
    /** Text and ARIA label for the end call button. */
    endCall?: string;
    /** ARIA label for the mute microphone button. */
    muteMicrophone?: string;
    /** ARIA label for the change language dropdown. */
    changeLanguage?: string;
    /** ARIA label for the collapse button. */
    collapse?: string;
    /** ARIA label for the expand button. */
    expand?: string;
    /** Text displayed when the user copies a value using the copy button. */
    copied?: string;
    /** Text and ARIA label for the accept terms button. */
    acceptTerms?: string;
    /** Text and ARIA label for the cancel terms button. */
    dismissTerms?: string;
    /** Status displayed when the agent is listening. */
    listeningStatus?: string;
    /** Status displayed when the agent is speaking. */
    speakingStatus?: string;
    /** Status displayed when the agent is connecting. */
    connectingStatus?: string;
    /** ARIA label for the text message input. */
    inputLabel?: string;
    /** Placeholder text for the text message input. */
    inputPlaceholder?: string;
    /** Information message displayed when the user ends the conversation. */
    userEndedConversation?: string;
    /** Information message displayed when the agent ends the conversation. */
    agentEndedConversation?: string;
    /** Text label used next to the conversation ID. */
    conversationId?: string;
    /** Text label used when an error occurs. */
    errorOccurred?: string;
    /** Text and ARIA label used for the copy ID button. */
    copyId?: string;
}
