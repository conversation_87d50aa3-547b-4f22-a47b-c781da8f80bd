/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Output format of the generated audio. Formatted as codec_sample_rate_bitrate. So an mp3 with 22.05kHz sample rate at 32kbs is represented as mp3_22050_32. MP3 with 192kbps bitrate requires you to be subscribed to Creator tier or above. PCM with 44.1kHz sample rate requires you to be subscribed to Pro tier or above. Note that the μ-law format (sometimes written mu-law, often approximated as u-law) is commonly used for Twilio audio inputs.
 */
export type TextToSpeechConvertWithTimestampsRequestOutputFormat = "mp3_22050_32" | "mp3_44100_32" | "mp3_44100_64" | "mp3_44100_96" | "mp3_44100_128" | "mp3_44100_192" | "pcm_8000" | "pcm_16000" | "pcm_22050" | "pcm_24000" | "pcm_44100" | "pcm_48000" | "ulaw_8000" | "alaw_8000" | "opus_48000_32" | "opus_48000_64" | "opus_48000_96" | "opus_48000_128" | "opus_48000_192";
export declare const TextToSpeechConvertWithTimestampsRequestOutputFormat: {
    readonly Mp32205032: "mp3_22050_32";
    readonly Mp34410032: "mp3_44100_32";
    readonly Mp34410064: "mp3_44100_64";
    readonly Mp34410096: "mp3_44100_96";
    readonly Mp344100128: "mp3_44100_128";
    readonly Mp344100192: "mp3_44100_192";
    readonly Pcm8000: "pcm_8000";
    readonly Pcm16000: "pcm_16000";
    readonly Pcm22050: "pcm_22050";
    readonly Pcm24000: "pcm_24000";
    readonly Pcm44100: "pcm_44100";
    readonly Pcm48000: "pcm_48000";
    readonly Ulaw8000: "ulaw_8000";
    readonly Alaw8000: "alaw_8000";
    readonly Opus4800032: "opus_48000_32";
    readonly Opus4800064: "opus_48000_64";
    readonly Opus4800096: "opus_48000_96";
    readonly Opus48000128: "opus_48000_128";
    readonly Opus48000192: "opus_48000_192";
};
