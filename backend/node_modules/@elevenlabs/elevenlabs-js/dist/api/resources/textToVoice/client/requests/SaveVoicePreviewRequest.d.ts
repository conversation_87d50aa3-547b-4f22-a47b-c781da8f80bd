/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * @example
 *     {
 *         voiceName: "Sassy squeaky mouse",
 *         voiceDescription: "A sassy squeaky mouse",
 *         generatedVoiceId: "37HceQefKmEi3bGovXjL"
 *     }
 */
export interface SaveVoicePreviewRequest {
    /** Name to use for the created voice. */
    voiceName: string;
    /** Description to use for the created voice. */
    voiceDescription: string;
    /** The generated_voice_id to create, call POST /v1/text-to-voice/create-previews and fetch the generated_voice_id from the response header if don't have one yet. */
    generatedVoiceId: string;
    /** Optional, metadata to add to the created voice. Defaults to None. */
    labels?: Record<string, string | undefined>;
    /** List of voice ids that the user has played but not selected. Used for RLHF. */
    playedNotSelectedVoiceIds?: string[];
}
