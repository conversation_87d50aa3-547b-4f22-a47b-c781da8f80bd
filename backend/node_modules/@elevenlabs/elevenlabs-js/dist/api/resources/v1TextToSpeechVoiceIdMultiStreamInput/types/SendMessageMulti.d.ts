/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../../../index";
/**
 * Send messages to the multi-context WebSocket.
 */
export type SendMessageMulti = ElevenLabs.InitializeConnectionMulti | ElevenLabs.InitialiseContext | ElevenLabs.SendTextMulti | ElevenLabs.FlushContext | ElevenLabs.CloseContext | ElevenLabs.CloseSocket | ElevenLabs.KeepContextAlive;
