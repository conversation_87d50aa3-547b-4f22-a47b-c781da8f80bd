/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface DubbingMetadataResponse {
    /** The ID of the dubbing project. */
    dubbingId: string;
    /** The name of the dubbing project. */
    name: string;
    /** The status of the dubbing project. Either 'dubbed', 'dubbing' or 'failed'. */
    status: string;
    /** The target languages of the dubbing project. */
    targetLanguages: string[];
    /** The media metadata of the dubbing project. */
    mediaMetadata?: ElevenLabs.DubbingMediaMetadata;
    /** Optional error message if the dubbing project failed. */
    error?: string;
}
