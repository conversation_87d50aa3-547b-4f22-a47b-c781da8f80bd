/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface DubbingResource {
    id: string;
    version: number;
    sourceLanguage: string;
    targetLanguages: string[];
    input: ElevenLabs.DubbingMediaReference;
    background: ElevenLabs.DubbingMediaReference;
    foreground: ElevenLabs.DubbingMediaReference;
    speakerTracks: Record<string, ElevenLabs.SpeakerTrack>;
    speakerSegments: Record<string, ElevenLabs.SpeakerSegment>;
    renders: Record<string, ElevenLabs.Render>;
}
