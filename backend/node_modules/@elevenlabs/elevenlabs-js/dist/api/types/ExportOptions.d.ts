/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export type ExportOptions = ElevenLabs.ExportOptions.Docx | ElevenLabs.ExportOptions.Html | ElevenLabs.ExportOptions.Pdf | ElevenLabs.ExportOptions.SegmentedJson | ElevenLabs.ExportOptions.Srt | ElevenLabs.ExportOptions.Txt;
export declare namespace ExportOptions {
    interface Docx extends ElevenLabs.DocxExportOptions {
        format: "docx";
    }
    interface Html extends ElevenLabs.HtmlExportOptions {
        format: "html";
    }
    interface Pdf extends ElevenLabs.PdfExportOptions {
        format: "pdf";
    }
    interface SegmentedJson extends ElevenLabs.SegmentedJsonExportOptions {
        format: "segmented_json";
    }
    interface Srt extends ElevenLabs.SrtExportOptions {
        format: "srt";
    }
    interface Txt extends ElevenLabs.TxtExportOptions {
        format: "txt";
    }
}
