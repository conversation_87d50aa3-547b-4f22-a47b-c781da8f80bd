/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface FeaturesUsageCommonModel {
    languageDetection?: ElevenLabs.FeatureStatusCommonModel;
    transferToAgent?: ElevenLabs.FeatureStatusCommonModel;
    transferToNumber?: ElevenLabs.FeatureStatusCommonModel;
    multivoice?: ElevenLabs.FeatureStatusCommonModel;
    piiZrmWorkspace?: boolean;
    piiZrmAgent?: boolean;
}
