/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface FeedbackItem {
    /** Whether the user liked the generated item. */
    thumbsUp: boolean;
    /** The feedback text provided by the user. */
    feedback: string;
    /** Whether the user provided emotions. */
    emotions: boolean;
    /** Whether the user thinks the clone is inaccurate. */
    inaccurateClone: boolean;
    /** Whether the user thinks there are glitches in the audio. */
    glitches: boolean;
    /** Whether the user thinks the audio quality is good. */
    audioQuality: boolean;
    /** Whether the user provided other feedback. */
    other: boolean;
    /** The review status of the item. Defaults to 'not_reviewed'. */
    reviewStatus?: string;
}
