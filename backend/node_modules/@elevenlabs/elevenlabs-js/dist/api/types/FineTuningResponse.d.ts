/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface FineTuningResponse {
    /** Whether the user is allowed to fine-tune the voice. */
    isAllowedToFineTune?: boolean;
    /** The state of the fine-tuning process for each model. */
    state?: Record<string, ElevenLabs.FineTuningResponseModelStateValue>;
    /** List of verification failures in the fine-tuning process. */
    verificationFailures?: string[];
    /** The number of verification attempts in the fine-tuning process. */
    verificationAttemptsCount?: number;
    /** Whether a manual verification was requested for the fine-tuning process. */
    manualVerificationRequested?: boolean;
    /** The language of the fine-tuning process. */
    language?: string;
    /** The progress of the fine-tuning process. */
    progress?: Record<string, number | undefined>;
    /** The message of the fine-tuning process. */
    message?: Record<string, string | undefined>;
    /** The duration of the dataset in seconds. */
    datasetDurationSeconds?: number;
    /** The number of verification attempts. */
    verificationAttempts?: ElevenLabs.VerificationAttemptResponse[];
    /** List of slice IDs. */
    sliceIds?: string[];
    /** The manual verification of the fine-tuning process. */
    manualVerification?: ElevenLabs.ManualVerificationResponse;
    /** The maximum number of verification attempts. */
    maxVerificationAttempts?: number;
    /** The next maximum verification attempts reset time in Unix milliseconds. */
    nextMaxVerificationAttemptsResetUnixMs?: number;
    finetuningState?: unknown;
}
