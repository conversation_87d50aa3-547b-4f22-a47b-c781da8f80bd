/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Payload to flush the audio buffer for a specific context.
 */
export interface FlushContext {
    /** The context_id to flush. */
    contextId: string;
    /** The text to append to the buffer to be flushed. */
    text?: string;
    /** If true, flushes the audio buffer for the specified context. If false, the context will remain open and the text will be appended to the buffer to be generated. */
    flush: boolean;
}
