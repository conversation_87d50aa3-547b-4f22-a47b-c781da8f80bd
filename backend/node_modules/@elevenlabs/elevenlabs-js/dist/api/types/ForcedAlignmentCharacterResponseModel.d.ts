/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Model representing a single character with its timing information from the aligner.
 */
export interface ForcedAlignmentCharacterResponseModel {
    /** The character that was transcribed. */
    text: string;
    /** The start time of the character in seconds. */
    start: number;
    /** The end time of the character in seconds. */
    end: number;
}
