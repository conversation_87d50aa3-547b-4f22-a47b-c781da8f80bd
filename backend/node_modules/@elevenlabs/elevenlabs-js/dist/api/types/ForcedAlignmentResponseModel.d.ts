/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * Model representing the response from the aligner service.
 */
export interface ForcedAlignmentResponseModel {
    /** List of characters with their timing information. */
    characters: ElevenLabs.ForcedAlignmentCharacterResponseModel[];
    /** List of words with their timing information. */
    words: ElevenLabs.ForcedAlignmentWordResponseModel[];
}
