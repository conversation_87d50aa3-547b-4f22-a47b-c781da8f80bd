/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface GenerateVoiceRequest {
    /** Category code corresponding to the gender of the generated voice. Possible values: female, male. */
    gender: ElevenLabs.BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostGender;
    /** Category code corresponding to the accent of the generated voice. Possible values: british, american, african, australian, indian. */
    accent: string;
    /** Category code corresponding to the age of the generated voice. Possible values: young, middle_aged, old. */
    age: ElevenLabs.BodyGenerateARandomVoiceV1VoiceGenerationGenerateVoicePostAge;
    /** The strength of the accent of the generated voice. Has to be between 0.3 and 2.0. */
    accentStrength: number;
    /** Text to generate, text length has to be between 100 and 1000. */
    text: string;
}
