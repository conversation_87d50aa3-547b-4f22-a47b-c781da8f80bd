/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface GetAgentResponseModel {
    /** The ID of the agent */
    agentId: string;
    /** The name of the agent */
    name: string;
    /** The conversation configuration of the agent */
    conversationConfig: ElevenLabs.ConversationalConfig;
    /** The metadata of the agent */
    metadata: ElevenLabs.AgentMetadataResponseModel;
    /** The platform settings of the agent */
    platformSettings?: ElevenLabs.AgentPlatformSettingsResponseModel;
    /** The phone numbers of the agent */
    phoneNumbers?: ElevenLabs.GetAgentResponseModelPhoneNumbersItem[];
    /** The access information of the agent for the user */
    accessInfo?: ElevenLabs.ResourceAccessInfo;
    /** Agent tags used to categorize the agent */
    tags?: string[];
}
