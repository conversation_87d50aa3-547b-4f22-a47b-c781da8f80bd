/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export type GetAgentResponseModelPhoneNumbersItem = ElevenLabs.GetAgentResponseModelPhoneNumbersItem.SipTrunk | ElevenLabs.GetAgentResponseModelPhoneNumbersItem.Twilio;
export declare namespace GetAgentResponseModelPhoneNumbersItem {
    interface SipTrunk extends ElevenLabs.GetPhoneNumberSipTrunkResponseModel {
        provider: "sip_trunk";
    }
    interface Twilio extends ElevenLabs.GetPhoneNumberTwilioResponseModel {
        provider: "twilio";
    }
}
