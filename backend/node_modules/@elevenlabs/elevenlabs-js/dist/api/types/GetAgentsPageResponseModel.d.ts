/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface GetAgentsPageResponseModel {
    /** A list of agents and their metadata */
    agents: ElevenLabs.AgentSummaryResponseModel[];
    /** The next cursor to paginate through the agents */
    nextCursor?: string;
    /** Whether there are more agents to paginate through */
    hasMore: boolean;
}
