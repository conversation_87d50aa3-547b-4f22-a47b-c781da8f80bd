/**
 * This file was auto-generated by Fe<PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export type GetConvAiDashboardSettingsResponseModelChartsItem = ElevenLabs.GetConvAiDashboardSettingsResponseModelChartsItem.CallSuccess | ElevenLabs.GetConvAiDashboardSettingsResponseModelChartsItem.Criteria | ElevenLabs.GetConvAiDashboardSettingsResponseModelChartsItem.DataCollection;
export declare namespace GetConvAiDashboardSettingsResponseModelChartsItem {
    interface CallSuccess extends ElevenLabs.DashboardCallSuccessChartModel {
        type: "call_success";
    }
    interface Criteria extends ElevenLabs.DashboardCriteriaChartModel {
        type: "criteria";
    }
    interface DataCollection extends ElevenLabs.DashboardDataCollectionChartModel {
        type: "data_collection";
    }
}
