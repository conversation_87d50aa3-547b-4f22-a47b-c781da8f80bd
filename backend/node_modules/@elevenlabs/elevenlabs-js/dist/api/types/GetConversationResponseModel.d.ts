/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface GetConversationResponseModel {
    agentId: string;
    conversationId: string;
    status: ElevenLabs.GetConversationResponseModelStatus;
    transcript: ElevenLabs.ConversationHistoryTranscriptCommonModelOutput[];
    metadata: ElevenLabs.ConversationHistoryMetadataCommonModel;
    analysis?: ElevenLabs.ConversationHistoryAnalysisCommonModel;
    conversationInitiationClientData?: ElevenLabs.ConversationInitiationClientDataRequestOutput;
    hasAudio: boolean;
    hasUserAudio: boolean;
    hasResponseAudio: boolean;
}
