/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export type GetKnowledgeBaseDependentAgentsResponseModelAgentsItem = ElevenLabs.GetKnowledgeBaseDependentAgentsResponseModelAgentsItem.Available | ElevenLabs.GetKnowledgeBaseDependentAgentsResponseModelAgentsItem.Unknown;
export declare namespace GetKnowledgeBaseDependentAgentsResponseModelAgentsItem {
    interface Available extends ElevenLabs.DependentAvailableAgentIdentifier {
        type: "available";
    }
    interface Unknown extends ElevenLabs.DependentUnknownAgentIdentifier {
        type: "unknown";
    }
}
