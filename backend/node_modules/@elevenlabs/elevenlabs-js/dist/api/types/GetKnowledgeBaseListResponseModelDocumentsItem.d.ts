/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export type GetKnowledgeBaseListResponseModelDocumentsItem = ElevenLabs.GetKnowledgeBaseListResponseModelDocumentsItem.File_ | ElevenLabs.GetKnowledgeBaseListResponseModelDocumentsItem.Text | ElevenLabs.GetKnowledgeBaseListResponseModelDocumentsItem.Url;
export declare namespace GetKnowledgeBaseListResponseModelDocumentsItem {
    interface File_ extends ElevenLabs.GetKnowledgeBaseSummaryFileResponseModel {
        type: "file";
    }
    interface Text extends ElevenLabs.GetKnowledgeBaseSummaryTextResponseModel {
        type: "text";
    }
    interface Url extends ElevenLabs.GetKnowledgeBaseSummaryUrlResponseModel {
        type: "url";
    }
}
