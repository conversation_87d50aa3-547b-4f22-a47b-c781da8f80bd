/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export type GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem = ElevenLabs.GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem.Available | ElevenLabs.GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem.Unknown;
export declare namespace GetKnowledgeBaseSummaryFileResponseModelDependentAgentsItem {
    interface Available extends ElevenLabs.DependentAvailableAgentIdentifier {
        type: "available";
    }
    interface Unknown extends ElevenLabs.DependentUnknownAgentIdentifier {
        type: "unknown";
    }
}
