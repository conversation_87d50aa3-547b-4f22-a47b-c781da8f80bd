/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export type GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem = ElevenLabs.GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem.Available | ElevenLabs.GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem.Unknown;
export declare namespace GetKnowledgeBaseSummaryTextResponseModelDependentAgentsItem {
    interface Available extends ElevenLabs.DependentAvailableAgentIdentifier {
        type: "available";
    }
    interface Unknown extends ElevenLabs.DependentUnknownAgentIdentifier {
        type: "unknown";
    }
}
