/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface GetKnowledgeBaseSummaryUrlResponseModel {
    id: string;
    name: string;
    metadata: ElevenLabs.KnowledgeBaseDocumentMetadataResponseModel;
    supportedUsages: ElevenLabs.DocumentUsageModeEnum[];
    accessInfo: ElevenLabs.ResourceAccessInfo;
    dependentAgents: ElevenLabs.GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem[];
    url: string;
}
