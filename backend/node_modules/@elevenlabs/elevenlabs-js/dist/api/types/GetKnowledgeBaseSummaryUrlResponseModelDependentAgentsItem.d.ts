/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export type GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem = ElevenLabs.GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem.Available | ElevenLabs.GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem.Unknown;
export declare namespace GetKnowledgeBaseSummaryUrlResponseModelDependentAgentsItem {
    interface Available extends ElevenLabs.DependentAvailableAgentIdentifier {
        type: "available";
    }
    interface Unknown extends ElevenLabs.DependentUnknownAgentIdentifier {
        type: "unknown";
    }
}
