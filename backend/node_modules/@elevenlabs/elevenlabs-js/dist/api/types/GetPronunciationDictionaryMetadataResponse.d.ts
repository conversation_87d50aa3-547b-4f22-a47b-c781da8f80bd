/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface GetPronunciationDictionaryMetadataResponse {
    /** The ID of the pronunciation dictionary. */
    id: string;
    /** The ID of the latest version of the pronunciation dictionary. */
    latestVersionId: string;
    /** The number of rules in the latest version of the pronunciation dictionary. */
    latestVersionRulesNum: number;
    /** The name of the pronunciation dictionary. */
    name: string;
    /** The permission on the resource of the pronunciation dictionary. */
    permissionOnResource?: ElevenLabs.GetPronunciationDictionaryMetadataResponseModelPermissionOnResource;
    /** The user ID of the creator of the pronunciation dictionary. */
    createdBy: string;
    /** The creation time of the pronunciation dictionary in Unix timestamp. */
    creationTimeUnix: number;
    /** The archive time of the pronunciation dictionary in Unix timestamp. */
    archivedTimeUnix?: number;
    /** The description of the pronunciation dictionary. */
    description?: string;
}
