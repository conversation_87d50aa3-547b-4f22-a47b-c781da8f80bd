/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface GetSpeechHistoryResponse {
    /** A list of speech history items. */
    history: ElevenLabs.SpeechHistoryItemResponse[];
    /** The ID of the last history item. */
    lastHistoryItemId?: string;
    /** Whether there are more history items to fetch. */
    hasMore: boolean;
}
