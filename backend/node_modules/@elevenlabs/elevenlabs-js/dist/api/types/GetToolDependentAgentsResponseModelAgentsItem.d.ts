/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export type GetToolDependentAgentsResponseModelAgentsItem = ElevenLabs.GetToolDependentAgentsResponseModelAgentsItem.Available | ElevenLabs.GetToolDependentAgentsResponseModelAgentsItem.Unknown;
export declare namespace GetToolDependentAgentsResponseModelAgentsItem {
    interface Available extends ElevenLabs.DependentAvailableAgentIdentifier {
        type: "available";
    }
    interface Unknown extends ElevenLabs.DependentUnknownAgentIdentifier {
        type: "unknown";
    }
}
