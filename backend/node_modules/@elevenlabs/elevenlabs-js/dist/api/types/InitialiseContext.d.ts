/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as Eleven<PERSON>abs from "../index";
/**
 * Payload to initialize or re-initialize a TTS context with specific settings and initial text for multi-stream connections.
 */
export interface InitialiseContext {
    /** The initial text to synthesize. Should end with a single space. */
    text: string;
    voiceSettings?: ElevenLabs.RealtimeVoiceSettings;
    generationConfig?: ElevenLabs.GenerationConfig;
    /** Optional list of pronunciation dictionary locators to be used for this context. */
    pronunciationDictionaryLocators?: ElevenLabs.PronunciationDictionaryLocator[];
    /** Your ElevenLabs API key. Required if not provided in the WebSocket connection's header or query parameters. This applies to the (re)initialization of this specific context. */
    xiApiKey?: string;
    /** Your authorization bearer token. Required if not provided in the WebSocket connection's header or query parameters. This applies to the (re)initialization of this specific context. */
    authorization?: string;
    /** An identifier for the text-to-speech context. If omitted, a default context ID may be assigned by the server. If provided, this message will create a new context with this ID or re-initialize an existing one with the new settings and text. */
    contextId?: string;
}
