/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface InitializeConnection {
    /** The initial text that must be sent is a blank space. */
    text: " ";
    voiceSettings?: ElevenLabs.RealtimeVoiceSettings;
    generationConfig?: ElevenLabs.GenerationConfig;
    /**
     * Optional list of pronunciation dictionary locators. If provided, these dictionaries will be used to
     * modify pronunciation of matching text. Must only be provided in the first message.
     *
     * Note: Pronunciation dictionary matches will only be respected within a provided chunk.
     */
    pronunciationDictionaryLocators?: ElevenLabs.PronunciationDictionaryLocator[];
    /** Your ElevenLabs API key. This can only be included in the first message and is not needed if present in the header. */
    xiApiKey?: string;
    /** Your authorization bearer token. This can only be included in the first message and is not needed if present in the header. */
    authorization?: string;
}
