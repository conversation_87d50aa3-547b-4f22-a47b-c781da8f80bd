/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Payload to keep a specific context alive by resetting its inactivity timeout. Empty text is ignored but resets the clock.
 */
export interface KeepContextAlive {
    /** An empty string. This text is ignored by the server but its presence resets the inactivity timeout for the specified context. */
    text: "";
    /** The identifier of the context to keep alive. */
    contextId: string;
}
