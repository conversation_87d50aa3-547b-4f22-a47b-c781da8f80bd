/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface LibraryVoiceResponse {
    /** The public owner id of the voice. */
    publicOwnerId: string;
    /** The id of the voice. */
    voiceId: string;
    /** The date the voice was added to the library in Unix time. */
    dateUnix: number;
    /** The name of the voice. */
    name: string;
    /** The accent of the voice. */
    accent: string;
    /** The gender of the voice. */
    gender: string;
    /** The age of the voice. */
    age: string;
    /** The descriptive of the voice. */
    descriptive: string;
    /** The use case of the voice. */
    useCase: string;
    /** The category of the voice. */
    category: ElevenLabs.LibraryVoiceResponseModelCategory;
    /** The language of the voice. */
    language?: string;
    /** The locale of the voice. */
    locale?: string;
    /** The description of the voice. */
    description?: string;
    /** The preview URL of the voice. */
    previewUrl?: string;
    /** The usage character count of the voice in the last year. */
    usageCharacterCount1Y: number;
    /** The usage character count of the voice in the last 7 days. */
    usageCharacterCount7D: number;
    /** The play API usage character count of the voice in the last year. */
    playApiUsageCharacterCount1Y: number;
    /** The number of times the voice has been cloned. */
    clonedByCount: number;
    /** The rate multiplier of the voice. */
    rate?: number;
    /** The rate of the voice in USD per 1000 credits. null if default */
    fiatRate?: number;
    /** Whether free users are allowed to use the voice. */
    freeUsersAllowed: boolean;
    /** Whether live moderation is enabled for the voice. */
    liveModerationEnabled: boolean;
    /** Whether the voice is featured. */
    featured: boolean;
    /** The verified languages of the voice. */
    verifiedLanguages?: ElevenLabs.VerifiedVoiceLanguageResponseModel[];
    /** The notice period of the voice. */
    noticePeriod?: number;
    /** The Instagram username of the voice. */
    instagramUsername?: string;
    /** The Twitter username of the voice. */
    twitterUsername?: string;
    /** The YouTube username of the voice. */
    youtubeUsername?: string;
    /** The TikTok username of the voice. */
    tiktokUsername?: string;
    /** The image URL of the voice. */
    imageUrl?: string;
    /** Whether the voice was added by the user. */
    isAddedByUser?: boolean;
}
