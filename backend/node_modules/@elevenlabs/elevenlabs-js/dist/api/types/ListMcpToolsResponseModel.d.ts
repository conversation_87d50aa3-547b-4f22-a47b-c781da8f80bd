/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * Response model for testing tools available on an MCP server.
 */
export interface ListMcpToolsResponseModel {
    /** Indicates if the operation was successful. */
    success: boolean;
    /** A list of tools available on the MCP server. */
    tools: ElevenLabs.Tool[];
    /** Error message if the operation was not successful. */
    errorMessage?: string;
}
