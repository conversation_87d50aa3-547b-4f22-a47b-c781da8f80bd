/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type Llm = "gpt-4o-mini" | "gpt-4o" | "gpt-4" | "gpt-4-turbo" | "gpt-4.1" | "gpt-4.1-mini" | "gpt-4.1-nano" | "gpt-3.5-turbo" | "gemini-1.5-pro" | "gemini-1.5-flash" | "gemini-2.0-flash" | "gemini-2.0-flash-lite" | "gemini-2.5-flash" | "claude-sonnet-4" | "claude-3-7-sonnet" | "claude-3-5-sonnet" | "claude-3-5-sonnet-v1" | "claude-3-haiku" | "grok-beta" | "custom-llm" | "gemini-2.5-flash-preview-05-20" | "gemini-2.5-flash-preview-04-17" | "gemini-2.0-flash-lite-001" | "gemini-2.0-flash-001" | "gemini-1.5-flash-002" | "gemini-1.5-flash-001" | "gemini-1.5-pro-002" | "gemini-1.5-pro-001" | "claude-sonnet-4@20250514" | "claude-3-7-sonnet@20250219" | "claude-3-5-sonnet@20240620" | "claude-3-5-sonnet-v2@20241022" | "claude-3-haiku@20240307" | "gpt-4.1-2025-04-14" | "gpt-4.1-mini-2025-04-14" | "gpt-4.1-nano-2025-04-14" | "gpt-4o-mini-2024-07-18" | "gpt-4o-2024-11-20" | "gpt-4o-2024-08-06" | "gpt-4o-2024-05-13" | "gpt-4-0613" | "gpt-4-0314" | "gpt-4-turbo-2024-04-09" | "gpt-3.5-turbo-0125" | "gpt-3.5-turbo-1106";
export declare const Llm: {
    readonly Gpt4OMini: "gpt-4o-mini";
    readonly Gpt4O: "gpt-4o";
    readonly Gpt4: "gpt-4";
    readonly Gpt4Turbo: "gpt-4-turbo";
    readonly Gpt41: "gpt-4.1";
    readonly Gpt41Mini: "gpt-4.1-mini";
    readonly Gpt41Nano: "gpt-4.1-nano";
    readonly Gpt35Turbo: "gpt-3.5-turbo";
    readonly Gemini15Pro: "gemini-1.5-pro";
    readonly Gemini15Flash: "gemini-1.5-flash";
    readonly Gemini20Flash: "gemini-2.0-flash";
    readonly Gemini20FlashLite: "gemini-2.0-flash-lite";
    readonly Gemini25Flash: "gemini-2.5-flash";
    readonly ClaudeSonnet4: "claude-sonnet-4";
    readonly Claude37Sonnet: "claude-3-7-sonnet";
    readonly Claude35Sonnet: "claude-3-5-sonnet";
    readonly Claude35SonnetV1: "claude-3-5-sonnet-v1";
    readonly Claude3Haiku: "claude-3-haiku";
    readonly GrokBeta: "grok-beta";
    readonly CustomLlm: "custom-llm";
    readonly Gemini25FlashPreview0520: "gemini-2.5-flash-preview-05-20";
    readonly Gemini25FlashPreview0417: "gemini-2.5-flash-preview-04-17";
    readonly Gemini20FlashLite001: "gemini-2.0-flash-lite-001";
    readonly Gemini20Flash001: "gemini-2.0-flash-001";
    readonly Gemini15Flash002: "gemini-1.5-flash-002";
    readonly Gemini15Flash001: "gemini-1.5-flash-001";
    readonly Gemini15Pro002: "gemini-1.5-pro-002";
    readonly Gemini15Pro001: "gemini-1.5-pro-001";
    readonly ClaudeSonnet420250514: "claude-sonnet-4@20250514";
    readonly Claude37Sonnet20250219: "claude-3-7-sonnet@20250219";
    readonly Claude35Sonnet20240620: "claude-3-5-sonnet@20240620";
    readonly Claude35SonnetV220241022: "claude-3-5-sonnet-v2@20241022";
    readonly Claude3Haiku20240307: "claude-3-haiku@20240307";
    readonly Gpt4120250414: "gpt-4.1-2025-04-14";
    readonly Gpt41Mini20250414: "gpt-4.1-mini-2025-04-14";
    readonly Gpt41Nano20250414: "gpt-4.1-nano-2025-04-14";
    readonly Gpt4OMini20240718: "gpt-4o-mini-2024-07-18";
    readonly Gpt4O20241120: "gpt-4o-2024-11-20";
    readonly Gpt4O20240806: "gpt-4o-2024-08-06";
    readonly Gpt4O20240513: "gpt-4o-2024-05-13";
    readonly Gpt40613: "gpt-4-0613";
    readonly Gpt40314: "gpt-4-0314";
    readonly Gpt4Turbo20240409: "gpt-4-turbo-2024-04-09";
    readonly Gpt35Turbo0125: "gpt-3.5-turbo-0125";
    readonly Gpt35Turbo1106: "gpt-3.5-turbo-1106";
};
