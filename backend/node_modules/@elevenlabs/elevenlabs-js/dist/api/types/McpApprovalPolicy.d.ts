/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * Defines the MCP server-level approval policy for tool execution.
 */
export type McpApprovalPolicy = "auto_approve_all" | "require_approval_all" | "require_approval_per_tool";
export declare const McpApprovalPolicy: {
    readonly AutoApproveAll: "auto_approve_all";
    readonly RequireApprovalAll: "require_approval_all";
    readonly RequireApprovalPerTool: "require_approval_per_tool";
};
