/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface McpServerConfigOutput {
    approvalPolicy?: ElevenLabs.McpApprovalPolicy;
    /** List of tool approval hashes for per-tool approval when approval_policy is REQUIRE_APPROVAL_PER_TOOL */
    toolApprovalHashes?: ElevenLabs.McpToolApprovalHash[];
    /** The transport type used to connect to the MCP server */
    transport?: ElevenLabs.McpServerTransport;
    /** The URL of the MCP server, if this contains a secret please store as a workspace secret, otherwise store as a plain string. Must use https */
    url: ElevenLabs.McpServerConfigOutputUrl;
    /** The secret token (Authorization header) stored as a workspace secret or in-place secret */
    secretToken?: ElevenLabs.McpServerConfigOutputSecretToken;
    /** The headers included in the request */
    requestHeaders?: Record<string, ElevenLabs.McpServerConfigOutputRequestHeadersValue>;
    name: string;
    description?: string;
}
