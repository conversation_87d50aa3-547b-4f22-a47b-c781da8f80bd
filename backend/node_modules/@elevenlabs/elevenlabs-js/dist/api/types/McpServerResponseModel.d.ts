/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * Response model representing an MCP Server configuration.
 */
export interface McpServerResponseModel {
    id: string;
    config: ElevenLabs.McpServerConfigOutput;
    /** The access information of the MCP Server */
    accessInfo?: ElevenLabs.ResourceAccessInfo;
    /** List of agents that depend on this MCP Server. */
    dependentAgents?: ElevenLabs.McpServerResponseModelDependentAgentsItem[];
    /** The metadata of the MCP Server */
    metadata: ElevenLabs.McpServerMetadataResponseModel;
}
