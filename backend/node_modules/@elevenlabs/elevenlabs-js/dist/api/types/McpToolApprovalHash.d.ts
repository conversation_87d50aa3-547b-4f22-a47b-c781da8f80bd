/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * Model for storing tool approval hashes for per-tool approval.
 */
export interface McpToolApprovalHash {
    /** The name of the MCP tool */
    toolName: string;
    /** SHA256 hash of the tool's parameters and description */
    toolHash: string;
    /** The approval policy for this tool */
    approvalPolicy?: ElevenLabs.McpToolApprovalPolicy;
}
