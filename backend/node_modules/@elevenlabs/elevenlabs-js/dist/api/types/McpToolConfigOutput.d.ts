/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as Eleven<PERSON>abs from "../index";
/**
 * An MCP tool configuration that can be used to call MCP servers
 */
export interface McpToolConfigOutput {
    name: string;
    description: string;
    /** The maximum time in seconds to wait for the tool call to complete. */
    responseTimeoutSecs?: number;
    /** The type of MCP tool */
    integrationType: ElevenLabs.IntegrationType;
    /** Schema for any parameters the LLM needs to provide to the MCP tool. */
    parameters?: ElevenLabs.ObjectJsonSchemaPropertyOutput;
    /** The approval policy for the MCP tool */
    approvalPolicy?: ElevenLabs.McpApprovalPolicy;
    /** The name of the MCP tool to call */
    mcpToolName: string;
    /** The description of the MCP tool to call */
    mcpToolDescription: string;
    /** The id of the MCP server to call */
    mcpServerId: string;
    /** The name of the MCP server to call */
    mcpServerName: string;
    /** Original inputSchema dict for consistent hashing (pure MCP format) */
    mcpInputSchema?: Record<string, unknown>;
}
