/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface Model {
    /** The unique identifier of the model. */
    modelId: string;
    /** The name of the model. */
    name?: string;
    /** Whether the model can be finetuned. */
    canBeFinetuned?: boolean;
    /** Whether the model can do text-to-speech. */
    canDoTextToSpeech?: boolean;
    /** Whether the model can do voice conversion. */
    canDoVoiceConversion?: boolean;
    /** Whether the model can use style. */
    canUseStyle?: boolean;
    /** Whether the model can use speaker boost. */
    canUseSpeakerBoost?: boolean;
    /** Whether the model serves pro voices. */
    servesProVoices?: boolean;
    /** The cost factor for the model. */
    tokenCostFactor?: number;
    /** The description of the model. */
    description?: string;
    /** Whether the model requires alpha access. */
    requiresAlphaAccess?: boolean;
    /** The maximum number of characters that can be requested by a free user. */
    maxCharactersRequestFreeUser?: number;
    /** The maximum number of characters that can be requested by a subscribed user. */
    maxCharactersRequestSubscribedUser?: number;
    /** The maximum length of text that can be requested for this model. */
    maximumTextLengthPerRequest?: number;
    /** The languages supported by the model. */
    languages?: ElevenLabs.LanguageResponse[];
    /** The rates for the model. */
    modelRates?: ElevenLabs.ModelRatesResponseModel;
    /** The concurrency group for the model. */
    concurrencyGroup?: ElevenLabs.ModelResponseModelConcurrencyGroup;
}
