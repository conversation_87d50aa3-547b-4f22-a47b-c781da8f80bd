/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface ModerationStatusResponseModel {
    /** Whether the user is in probation. */
    isInProbation: boolean;
    /** Whether the user's enterprise check nogo voice is enabled. */
    enterpriseCheckNogoVoice: boolean;
    /** Whether the user's enterprise check block nogo voice is enabled. */
    enterpriseCheckBlockNogoVoice: boolean;
    /** Whether the user's never live moderate is enabled. */
    neverLiveModerate: boolean;
    /** The number of similar voice uploads that have been blocked. */
    nogoVoiceSimilarVoiceUploadCount: number;
    /** Whether the user's enterprise background moderation is enabled. */
    enterpriseBackgroundModerationEnabled: boolean;
    /** The safety status of the user. */
    safetyStatus?: ElevenLabs.ModerationStatusResponseModelSafetyStatus;
    /** The warning status of the user. */
    warningStatus?: ElevenLabs.ModerationStatusResponseModelWarningStatus;
    /** Whether the user is on the watchlist. */
    onWatchlist: boolean;
}
