/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type OutputFormat = 
/**
 * Output format, mp3 with 22.05kHz sample rate at 32kbps */
"mp3_22050_32"
/**
 * Output format, mp3 with 44.1kHz sample rate at 32kbps */
 | "mp3_44100_32"
/**
 * Output format, mp3 with 44.1kHz sample rate at 64kbps */
 | "mp3_44100_64"
/**
 * Output format, mp3 with 44.1kHz sample rate at 96kbps */
 | "mp3_44100_96"
/**
 * Default output format, mp3 with 44.1kHz sample rate at 128kbps */
 | "mp3_44100_128"
/**
 * Output format, mp3 with 44.1kHz sample rate at 192kbps. */
 | "mp3_44100_192"
/**
 * PCM format (S16LE) with 16kHz sample rate. */
 | "pcm_16000"
/**
 * PCM format (S16LE) with 22.05kHz sample rate. */
 | "pcm_22050"
/**
 * PCM format (S16LE) with 24kHz sample rate. */
 | "pcm_24000"
/**
 * PCM format (S16LE) with 44.1kHz sample rate. Requires you to be subscribed to Independent Publisher tier or above. */
 | "pcm_44100"
/**
 * μ-law format (sometimes written mu-law, often approximated as u-law) with 8kHz sample rate. Note that this format is commonly used for Twilio audio inputs. */
 | "ulaw_8000";
export declare const OutputFormat: {
    readonly Mp32205032: "mp3_22050_32";
    readonly Mp34410032: "mp3_44100_32";
    readonly Mp34410064: "mp3_44100_64";
    readonly Mp34410096: "mp3_44100_96";
    readonly Mp344100128: "mp3_44100_128";
    readonly Mp344100192: "mp3_44100_192";
    readonly Pcm16000: "pcm_16000";
    readonly Pcm22050: "pcm_22050";
    readonly Pcm24000: "pcm_24000";
    readonly Pcm44100: "pcm_44100";
    readonly Ulaw8000: "ulaw_8000";
};
