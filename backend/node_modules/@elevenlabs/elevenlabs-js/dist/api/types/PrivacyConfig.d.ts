/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface PrivacyConfig {
    /** Whether to record the conversation */
    recordVoice?: boolean;
    /** The number of days to retain the conversation. -1 indicates there is no retention limit */
    retentionDays?: number;
    /** Whether to delete the transcript and PII */
    deleteTranscriptAndPii?: boolean;
    /** Whether to delete the audio */
    deleteAudio?: boolean;
    /** Whether to apply the privacy settings to existing conversations */
    applyToExistingConversations?: boolean;
    /** Whether to enable zero retention mode - no PII data is stored */
    zeroRetentionMode?: boolean;
}
