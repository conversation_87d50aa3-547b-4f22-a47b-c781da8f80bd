/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface ProjectResponse {
    /** The ID of the project. */
    projectId: string;
    /** The name of the project. */
    name: string;
    /** The creation date of the project. */
    createDateUnix: number;
    /** The default title voice ID. */
    defaultTitleVoiceId: string;
    /** The default paragraph voice ID. */
    defaultParagraphVoiceId: string;
    /** The default model ID. */
    defaultModelId: string;
    /** The last conversion date of the project. */
    lastConversionDateUnix?: number;
    /** Whether the project can be downloaded. */
    canBeDownloaded: boolean;
    /** The title of the project. */
    title?: string;
    /** The author of the project. */
    author?: string;
    /** The description of the project. */
    description?: string;
    /** List of genres of the project. */
    genres?: string[];
    /** The cover image URL of the project. */
    coverImageUrl?: string;
    /** The target audience of the project. */
    targetAudience?: ElevenLabs.ProjectResponseModelTargetAudience;
    /** Two-letter language code (ISO 639-1) of the language of the project. */
    language?: string;
    /** The content type of the project, e.g. 'Novel' or 'Short Story' */
    contentType?: string;
    /** The original publication date of the project. */
    originalPublicationDate?: string;
    /** Whether the project contains mature content. */
    matureContent?: boolean;
    /** The ISBN number of the project. */
    isbnNumber?: string;
    /** Whether the project uses volume normalization. */
    volumeNormalization: boolean;
    /** The state of the project. */
    state: ElevenLabs.ProjectState;
    /** The access level of the project. */
    accessLevel: ElevenLabs.ProjectResponseModelAccessLevel;
    /** Whether the project is fiction. */
    fiction?: ElevenLabs.ProjectResponseModelFiction;
    /** Whether quality check is enabled for this project. */
    qualityCheckOn: boolean;
    /** Whether quality check is enabled on the project when bulk converting. */
    qualityCheckOnWhenBulkConvert: boolean;
    /** The creation meta of the project. */
    creationMeta?: ElevenLabs.ProjectCreationMetaResponseModel;
    /** The source type of the project. */
    sourceType?: ElevenLabs.ProjectResponseModelSourceType;
    /** Whether chapters are enabled for the project. */
    chaptersEnabled?: boolean;
}
