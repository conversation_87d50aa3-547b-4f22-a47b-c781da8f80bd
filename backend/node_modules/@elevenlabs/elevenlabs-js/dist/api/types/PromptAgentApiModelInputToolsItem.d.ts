/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * The type of tool
 */
export type PromptAgentApiModelInputToolsItem = ElevenLabs.PromptAgentApiModelInputToolsItem.Client | ElevenLabs.PromptAgentApiModelInputToolsItem.Mcp | ElevenLabs.PromptAgentApiModelInputToolsItem.System | ElevenLabs.PromptAgentApiModelInputToolsItem.Webhook;
export declare namespace PromptAgentApiModelInputToolsItem {
    interface Client extends ElevenLabs.ClientToolConfigInput {
        type: "client";
    }
    interface Mcp extends ElevenLabs.McpToolConfigInput {
        type: "mcp";
    }
    interface System extends ElevenLabs.SystemToolConfigInput {
        type: "system";
    }
    interface Webhook extends ElevenLabs.WebhookToolConfigInput {
        type: "webhook";
    }
}
