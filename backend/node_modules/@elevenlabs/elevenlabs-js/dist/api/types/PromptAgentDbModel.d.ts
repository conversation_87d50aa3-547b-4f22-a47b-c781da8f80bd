/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface PromptAgentDbModel {
    /** The prompt for the agent */
    prompt?: string;
    /** The LLM to query with the prompt and the chat history */
    llm?: ElevenLabs.Llm;
    /** The temperature for the LLM */
    temperature?: number;
    /** If greater than 0, maximum number of tokens the LLM can predict */
    maxTokens?: number;
    /** A list of IDs of tools used by the agent */
    toolIds?: string[];
    /** Built-in system tools to be used by the agent */
    builtInTools?: ElevenLabs.BuiltInToolsInput;
    /** A list of MCP server ids to be used by the agent */
    mcpServerIds?: string[];
    /** A list of Native MCP server ids to be used by the agent */
    nativeMcpServerIds?: string[];
    /** A list of knowledge bases to be used by the agent */
    knowledgeBase?: ElevenLabs.KnowledgeBaseLocator[];
    /** Definition for a custom LLM if LLM field is set to 'CUSTOM_LLM' */
    customLlm?: ElevenLabs.CustomLlm;
    /** Whether to ignore the default personality */
    ignoreDefaultPersonality?: boolean;
    /** Configuration for RAG */
    rag?: ElevenLabs.RagConfig;
    knowledgeBaseDocumentIds?: string[];
    tools?: unknown;
}
