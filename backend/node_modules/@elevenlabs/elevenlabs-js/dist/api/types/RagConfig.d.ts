/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface RagConfig {
    enabled?: boolean;
    embeddingModel?: ElevenLabs.EmbeddingModelEnum;
    /** Maximum vector distance of retrieved chunks. */
    maxVectorDistance?: number;
    /** Maximum total length of document chunks retrieved from RAG. */
    maxDocumentsLength?: number;
    /** Maximum number of RAG document chunks to initially retrieve from the vector store. These are then further filtered by vector distance and total length. */
    maxRetrievedRagChunksCount?: number;
}
