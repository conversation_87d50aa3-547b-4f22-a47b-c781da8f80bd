/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface ResourceAccessInfo {
    /** Whether the user making the request is the creator of the agent */
    isCreator: boolean;
    /** Name of the agent's creator */
    creatorName: string;
    /** Email of the agent's creator */
    creatorEmail: string;
    /** The role of the user making the request */
    role: ElevenLabs.ResourceAccessInfoRole;
}
