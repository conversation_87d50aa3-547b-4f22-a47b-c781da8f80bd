/**
 * This file was auto-generated by <PERSON>rn from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * SIP Trunk configuration details for a phone number
 */
export interface SipTrunkConfigResponseModel {
    /** Hostname or IP the SIP INVITE is sent to */
    address: string;
    /** Protocol to use for SIP transport */
    transport: ElevenLabs.SipTrunkTransportEnum;
    /** Whether or not to encrypt media (data layer). */
    mediaEncryption: ElevenLabs.SipMediaEncryptionEnum;
    /** SIP headers for INVITE request */
    headers?: Record<string, string>;
    /** Whether authentication credentials are configured */
    hasAuthCredentials: boolean;
    /** SIP trunk username (if available) */
    username?: string;
    /** Whether a LiveKit SIP outbound trunk is configured */
    hasOutboundTrunk?: boolean;
}
