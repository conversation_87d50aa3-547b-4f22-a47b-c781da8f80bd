/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * Chunk-level detail of the transcription with timing information.
 */
export interface SpeechToTextChunkResponseModel {
    /** The detected language code (e.g. 'eng' for English). */
    languageCode: string;
    /** The confidence score of the language detection (0 to 1). */
    languageProbability: number;
    /** The raw text of the transcription. */
    text: string;
    /** List of words with their timing information. */
    words: ElevenLabs.SpeechToTextWordResponseModel[];
    /** Requested additional formats of the transcript. */
    additionalFormats?: (ElevenLabs.AdditionalFormatResponseModel | undefined)[];
}
