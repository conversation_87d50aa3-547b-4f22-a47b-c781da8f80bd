/**
 * This file was auto-generated by Fern from our API Definition.
 */
export type SubscriptionStatusType = "trialing" | "active" | "incomplete" | "incomplete_expired" | "past_due" | "free" | "free_disabled" | "canceled";
export declare const SubscriptionStatusType: {
    readonly Trialing: "trialing";
    readonly Active: "active";
    readonly Incomplete: "incomplete";
    readonly IncompleteExpired: "incomplete_expired";
    readonly PastDue: "past_due";
    readonly Free: "free";
    readonly FreeDisabled: "free_disabled";
    readonly Canceled: "canceled";
};
