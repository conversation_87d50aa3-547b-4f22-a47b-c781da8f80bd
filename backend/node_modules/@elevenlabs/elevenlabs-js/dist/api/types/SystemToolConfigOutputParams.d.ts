/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
import * as ElevenLabs from "../index";
export type SystemToolConfigOutputParams = ElevenLabs.SystemToolConfigOutputParams.EndCall | ElevenLabs.SystemToolConfigOutputParams.LanguageDetection | ElevenLabs.SystemToolConfigOutputParams.SkipTurn | ElevenLabs.SystemToolConfigOutputParams.TransferToAgent | ElevenLabs.SystemToolConfigOutputParams.TransferToNumber;
export declare namespace SystemToolConfigOutputParams {
    interface EndCall extends ElevenLabs.EndCallToolConfig {
        systemToolType: "end_call";
    }
    interface LanguageDetection extends ElevenLabs.LanguageDetectionToolConfig {
        systemToolType: "language_detection";
    }
    interface SkipTurn extends ElevenLabs.SkipTurnToolConfig {
        systemToolType: "skip_turn";
    }
    interface TransferToAgent extends ElevenLabs.TransferToAgentToolConfig {
        systemToolType: "transfer_to_agent";
    }
    interface TransferToNumber extends ElevenLabs.TransferToNumberToolConfig {
        systemToolType: "transfer_to_number";
    }
}
