/**
 * This file was auto-generated by Fern from our API Definition.
 */
/**
 * This parameter controls text normalization with three modes - 'auto', 'on', and 'off'. When set to 'auto', the system will automatically decide whether to apply text normalization (e.g., spelling out numbers). With 'on', text normalization will always be applied, while with 'off', it will be skipped. Cannot be turned on for 'eleven_turbo_v2_5' or 'eleven_flash_v2_5' models. Defaults to 'auto'.
 */
export type TextToSpeechApplyTextNormalizationEnum = "auto" | "on" | "off";
export declare const TextToSpeechApplyTextNormalizationEnum: {
    readonly Auto: "auto";
    readonly On: "on";
    readonly Off: "off";
};
