/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface VoiceSettings {
    /** Determines how stable the voice is and the randomness between each generation. Lower values introduce broader emotional range for the voice. Higher values can result in a monotonous voice with limited emotion. */
    stability?: number;
    /** This setting boosts the similarity to the original speaker. Using this setting requires a slightly higher computational load, which in turn increases latency. */
    useSpeakerBoost?: boolean;
    /** Determines how closely the AI should adhere to the original voice when attempting to replicate it. */
    similarityBoost?: number;
    /** Determines the style exaggeration of the voice. This setting attempts to amplify the style of the original speaker. It does consume additional computational resources and might increase latency if set to anything other than 0. */
    style?: number;
    /** Adjusts the speed of the voice. A value of 1.0 is the default speed, while values less than 1.0 slow down the speech, and values greater than 1.0 speed it up. */
    speed?: number;
}
