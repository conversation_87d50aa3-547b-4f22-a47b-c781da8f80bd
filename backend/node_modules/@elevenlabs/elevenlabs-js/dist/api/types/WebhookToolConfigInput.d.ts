/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
/**
 * A webhook tool is a tool that calls an external webhook from our server
 */
export interface WebhookToolConfigInput {
    name: string;
    description: string;
    /** The maximum time in seconds to wait for the tool call to complete. Must be between 5 and 120 seconds (inclusive). */
    responseTimeoutSecs?: number;
    /** The schema for the outgoing webhoook, including parameters and URL specification */
    apiSchema: ElevenLabs.WebhookToolApiSchemaConfigInput;
    /** Configuration for dynamic variables */
    dynamicVariables?: ElevenLabs.DynamicVariablesConfig;
}
