/**
 * This file was auto-generated by <PERSON><PERSON> from our API Definition.
 */
export type WorkspaceGroupPermission = "text_to_speech" | "speech_to_speech" | "voice_lab" | "sound_effects" | "projects" | "voiceover_studio" | "dubbing" | "audio_native" | "conversational_ai" | "voice_isolator" | "ai_speech_classifier" | "add_voice_from_voice_library" | "create_instant_voice_clone" | "create_professional_voice_clone" | "create_user_api_key" | "publish_studio_project";
export declare const WorkspaceGroupPermission: {
    readonly TextToSpeech: "text_to_speech";
    readonly SpeechToSpeech: "speech_to_speech";
    readonly VoiceLab: "voice_lab";
    readonly SoundEffects: "sound_effects";
    readonly Projects: "projects";
    readonly VoiceoverStudio: "voiceover_studio";
    readonly Dubbing: "dubbing";
    readonly AudioNative: "audio_native";
    readonly ConversationalAi: "conversational_ai";
    readonly VoiceIsolator: "voice_isolator";
    readonly AiSpeechClassifier: "ai_speech_classifier";
    readonly AddVoiceFromVoiceLibrary: "add_voice_from_voice_library";
    readonly CreateInstantVoiceClone: "create_instant_voice_clone";
    readonly CreateProfessionalVoiceClone: "create_professional_voice_clone";
    readonly CreateUserApiKey: "create_user_api_key";
    readonly PublishStudioProject: "publish_studio_project";
};
