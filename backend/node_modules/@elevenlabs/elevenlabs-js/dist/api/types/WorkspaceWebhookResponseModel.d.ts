/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as ElevenLabs from "../index";
export interface WorkspaceWebhookResponseModel {
    /** The display name for this webhook. */
    name: string;
    /** The unique ID for this webhook. */
    webhookId: string;
    /** The HTTPS callback URL that is called when this webhook is triggered in the platform. */
    webhookUrl: string;
    /** Whether the webhook has been manually disabled by a user. */
    isDisabled: boolean;
    /** Whether the webhook has been automatically disabled due to repeated consecutive failures over a long period of time. */
    isAutoDisabled: boolean;
    /** Original creation time of the webhook. */
    createdAtUnix: number;
    /** The authentication mode used to secure the webhook. */
    authType: ElevenLabs.WebhookAuthMethodType;
    /** The list of products that are currently configured to trigger this webhook. */
    usage?: ElevenLabs.WorkspaceWebhookUsageResponseModel[];
    /** The most recent error code returned from the callback URL. */
    mostRecentFailureErrorCode?: number;
    /** The most recent time the webhook failed, failures are any non-200 codes returned by the callback URL. */
    mostRecentFailureTimestamp?: number;
}
