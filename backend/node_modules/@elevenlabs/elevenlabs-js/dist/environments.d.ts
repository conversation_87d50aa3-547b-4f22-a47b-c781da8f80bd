/**
 * This file was auto-generated by Fern from our API Definition.
 */
export interface ElevenLabsEnvironmentUrls {
    base: string;
    wss: string;
}
export declare const ElevenLabsEnvironment: {
    readonly Production: {
        readonly base: "https://api.elevenlabs.io";
        readonly wss: "wss://api.elevenlabs.io";
    };
    readonly ProductionUs: {
        readonly base: "https://api.us.elevenlabs.io";
        readonly wss: "wss://api.elevenlabs.io";
    };
    readonly ProductionEu: {
        readonly base: "https://api.eu.residency.elevenlabs.io";
        readonly wss: "wss://api.elevenlabs.io";
    };
};
export type ElevenLabsEnvironment = typeof ElevenLabsEnvironment.Production | typeof ElevenLabsEnvironment.ProductionUs | typeof ElevenLabsEnvironment.ProductionEu;
