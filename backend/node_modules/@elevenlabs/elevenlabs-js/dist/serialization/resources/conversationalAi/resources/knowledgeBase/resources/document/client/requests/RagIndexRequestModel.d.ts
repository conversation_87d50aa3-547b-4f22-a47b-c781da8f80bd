/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../../../../index";
import * as ElevenLabs from "../../../../../../../../../api/index";
import * as core from "../../../../../../../../../core";
import { EmbeddingModelEnum } from "../../../../../../../../types/EmbeddingModelEnum";
export declare const RagIndexRequestModel: core.serialization.Schema<serializers.conversationalAi.knowledgeBase.RagIndexRequestModel.Raw, ElevenLabs.conversationalAi.knowledgeBase.RagIndexRequestModel>;
export declare namespace RagIndexRequestModel {
    interface Raw {
        model: EmbeddingModelEnum.Raw;
    }
}
