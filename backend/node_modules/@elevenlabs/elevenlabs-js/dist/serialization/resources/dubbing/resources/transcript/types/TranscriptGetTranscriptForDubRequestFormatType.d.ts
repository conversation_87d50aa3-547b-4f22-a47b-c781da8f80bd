/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../../index";
import * as ElevenLabs from "../../../../../../api/index";
import * as core from "../../../../../../core";
export declare const TranscriptGetTranscriptForDubRequestFormatType: core.serialization.Schema<serializers.dubbing.TranscriptGetTranscriptForDubRequestFormatType.Raw, ElevenLabs.dubbing.TranscriptGetTranscriptForDubRequestFormatType>;
export declare namespace TranscriptGetTranscriptForDubRequestFormatType {
    type Raw = "srt" | "webvtt";
}
