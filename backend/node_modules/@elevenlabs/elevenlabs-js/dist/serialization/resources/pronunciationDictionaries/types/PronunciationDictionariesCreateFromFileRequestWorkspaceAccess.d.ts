/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
export declare const PronunciationDictionariesCreateFromFileRequestWorkspaceAccess: core.serialization.Schema<serializers.PronunciationDictionariesCreateFromFileRequestWorkspaceAccess.Raw, ElevenLabs.PronunciationDictionariesCreateFromFileRequestWorkspaceAccess>;
export declare namespace PronunciationDictionariesCreateFromFileRequestWorkspaceAccess {
    type Raw = "admin" | "editor" | "viewer";
}
