/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
export declare const PronunciationDictionariesListRequestSort: core.serialization.Schema<serializers.PronunciationDictionariesListRequestSort.Raw, ElevenLabs.PronunciationDictionariesListRequestSort>;
export declare namespace PronunciationDictionariesListRequestSort {
    type Raw = "creation_time_unix" | "name";
}
