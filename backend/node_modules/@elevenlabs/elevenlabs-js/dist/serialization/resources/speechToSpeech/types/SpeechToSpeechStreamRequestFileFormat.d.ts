/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../index";
import * as ElevenLabs from "../../../../api/index";
import * as core from "../../../../core";
export declare const SpeechToSpeechStreamRequestFileFormat: core.serialization.Schema<serializers.SpeechToSpeechStreamRequestFileFormat.Raw, ElevenLabs.SpeechToSpeechStreamRequestFileFormat>;
export declare namespace SpeechToSpeechStreamRequestFileFormat {
    type Raw = "pcm_s16le_16" | "other";
}
