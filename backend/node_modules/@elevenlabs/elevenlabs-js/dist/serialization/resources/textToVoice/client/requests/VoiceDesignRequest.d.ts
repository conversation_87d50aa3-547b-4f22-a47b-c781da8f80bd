/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../index";
import * as ElevenLabs from "../../../../../api/index";
import * as core from "../../../../../core";
export declare const VoiceDesignRequest: core.serialization.Schema<serializers.VoiceDesignRequest.Raw, Omit<ElevenLabs.VoiceDesignRequest, "outputFormat">>;
export declare namespace VoiceDesignRequest {
    interface Raw {
        voice_description: string;
        text?: string | null;
        auto_generate_text?: boolean | null;
        loudness?: number | null;
        quality?: number | null;
        seed?: number | null;
        guidance_scale?: number | null;
    }
}
