/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../../../../index";
import * as ElevenLabs from "../../../../../api/index";
import * as core from "../../../../../core";
import { VoiceDesignRequestModelModelId } from "../../types/VoiceDesignRequestModelModelId";
export declare const VoiceDesignRequestModel: core.serialization.Schema<serializers.VoiceDesignRequestModel.Raw, Omit<ElevenLabs.VoiceDesignRequestModel, "outputFormat">>;
export declare namespace VoiceDesignRequestModel {
    interface Raw {
        voice_description: string;
        model_id?: VoiceDesignRequestModelModelId.Raw | null;
        text?: string | null;
        auto_generate_text?: boolean | null;
        loudness?: number | null;
        seed?: number | null;
        guidance_scale?: number | null;
        quality?: number | null;
        reference_audio_base64?: string | null;
        prompt_strength?: number | null;
    }
}
