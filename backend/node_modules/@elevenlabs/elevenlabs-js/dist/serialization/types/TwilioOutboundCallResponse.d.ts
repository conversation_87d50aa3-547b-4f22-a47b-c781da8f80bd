/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const TwilioOutboundCallResponse: core.serialization.ObjectSchema<serializers.TwilioOutboundCallResponse.Raw, ElevenLabs.TwilioOutboundCallResponse>;
export declare namespace TwilioOutboundCallResponse {
    interface Raw {
        success: boolean;
        message: string;
        conversation_id?: string | null;
        callSid?: string | null;
    }
}
