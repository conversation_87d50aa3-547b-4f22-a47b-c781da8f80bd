/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { PronunciationDictionaryVersionLocator } from "./PronunciationDictionaryVersionLocator";
export declare const UpdatePronunciationDictionariesRequest: core.serialization.ObjectSchema<serializers.UpdatePronunciationDictionariesRequest.Raw, ElevenLabs.UpdatePronunciationDictionariesRequest>;
export declare namespace UpdatePronunciationDictionariesRequest {
    interface Raw {
        pronunciation_dictionary_locators: PronunciationDictionaryVersionLocator.Raw[];
        invalidate_affected_text?: boolean | null;
    }
}
