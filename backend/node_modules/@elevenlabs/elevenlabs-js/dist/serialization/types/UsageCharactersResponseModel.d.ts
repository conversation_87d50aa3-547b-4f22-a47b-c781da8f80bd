/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const UsageCharactersResponseModel: core.serialization.ObjectSchema<serializers.UsageCharactersResponseModel.Raw, ElevenLabs.UsageCharactersResponseModel>;
export declare namespace UsageCharactersResponseModel {
    interface Raw {
        time: number[];
        usage: Record<string, number[]>;
    }
}
