/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { RecordingResponse } from "./RecordingResponse";
export declare const VerificationAttemptResponse: core.serialization.ObjectSchema<serializers.VerificationAttemptResponse.Raw, ElevenLabs.VerificationAttemptResponse>;
export declare namespace VerificationAttemptResponse {
    interface Raw {
        text: string;
        date_unix: number;
        accepted: boolean;
        similarity: number;
        levenshtein_distance: number;
        recording?: RecordingResponse.Raw | null;
    }
}
