/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VerifyPvcVoiceCaptchaResponseModel: core.serialization.ObjectSchema<serializers.VerifyPvcVoiceCaptchaResponseModel.Raw, ElevenLabs.VerifyPvcVoiceCaptchaResponseModel>;
export declare namespace VerifyPvcVoiceCaptchaResponseModel {
    interface Raw {
        status: string;
    }
}
