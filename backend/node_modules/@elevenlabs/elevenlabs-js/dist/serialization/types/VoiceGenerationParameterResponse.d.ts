/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { VoiceGenerationParameterOptionResponse } from "./VoiceGenerationParameterOptionResponse";
export declare const VoiceGenerationParameterResponse: core.serialization.ObjectSchema<serializers.VoiceGenerationParameterResponse.Raw, ElevenLabs.VoiceGenerationParameterResponse>;
export declare namespace VoiceGenerationParameterResponse {
    interface Raw {
        genders: VoiceGenerationParameterOptionResponse.Raw[];
        accents: VoiceGenerationParameterOptionResponse.Raw[];
        ages: VoiceGenerationParameterOptionResponse.Raw[];
        minimum_characters: number;
        maximum_characters: number;
        minimum_accent_strength: number;
        maximum_accent_strength: number;
    }
}
