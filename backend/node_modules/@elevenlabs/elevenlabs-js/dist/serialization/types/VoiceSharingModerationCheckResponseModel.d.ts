/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const VoiceSharingModerationCheckResponseModel: core.serialization.ObjectSchema<serializers.VoiceSharingModerationCheckResponseModel.Raw, ElevenLabs.VoiceSharingModerationCheckResponseModel>;
export declare namespace VoiceSharingModerationCheckResponseModel {
    interface Raw {
        date_checked_unix?: number | null;
        name_value?: string | null;
        name_check?: boolean | null;
        description_value?: string | null;
        description_check?: boolean | null;
        sample_ids?: string[] | null;
        sample_checks?: number[] | null;
        captcha_ids?: string[] | null;
        captcha_checks?: number[] | null;
    }
}
