/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WebhookToolApiSchemaConfigInputMethod } from "./WebhookToolApiSchemaConfigInputMethod";
import { LiteralJsonSchemaProperty } from "./LiteralJsonSchemaProperty";
import { QueryParamsJsonSchema } from "./QueryParamsJsonSchema";
import { WebhookToolApiSchemaConfigInputRequestHeadersValue } from "./WebhookToolApiSchemaConfigInputRequestHeadersValue";
export declare const WebhookToolApiSchemaConfigInput: core.serialization.ObjectSchema<serializers.WebhookToolApiSchemaConfigInput.Raw, ElevenLabs.WebhookToolApiSchemaConfigInput>;
export declare namespace WebhookToolApiSchemaConfigInput {
    interface Raw {
        url: string;
        method?: WebhookToolApiSchemaConfigInputMethod.Raw | null;
        path_params_schema?: Record<string, LiteralJsonSchemaProperty.Raw> | null;
        query_params_schema?: QueryParamsJsonSchema.Raw | null;
        request_body_schema?: serializers.ObjectJsonSchemaPropertyInput.Raw | null;
        request_headers?: Record<string, WebhookToolApiSchemaConfigInputRequestHeadersValue.Raw> | null;
    }
}
