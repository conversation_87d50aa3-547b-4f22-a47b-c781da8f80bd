/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WebhookToolApiSchemaConfigOutputMethod } from "./WebhookToolApiSchemaConfigOutputMethod";
import { LiteralJsonSchemaProperty } from "./LiteralJsonSchemaProperty";
import { QueryParamsJsonSchema } from "./QueryParamsJsonSchema";
import { WebhookToolApiSchemaConfigOutputRequestHeadersValue } from "./WebhookToolApiSchemaConfigOutputRequestHeadersValue";
export declare const WebhookToolApiSchemaConfigOutput: core.serialization.ObjectSchema<serializers.WebhookToolApiSchemaConfigOutput.Raw, ElevenLabs.WebhookToolApiSchemaConfigOutput>;
export declare namespace WebhookToolApiSchemaConfigOutput {
    interface Raw {
        url: string;
        method?: WebhookToolApiSchemaConfigOutputMethod.Raw | null;
        path_params_schema?: Record<string, LiteralJsonSchemaProperty.Raw> | null;
        query_params_schema?: QueryParamsJsonSchema.Raw | null;
        request_body_schema?: serializers.ObjectJsonSchemaPropertyOutput.Raw | null;
        request_headers?: Record<string, WebhookToolApiSchemaConfigOutputRequestHeadersValue.Raw> | null;
    }
}
