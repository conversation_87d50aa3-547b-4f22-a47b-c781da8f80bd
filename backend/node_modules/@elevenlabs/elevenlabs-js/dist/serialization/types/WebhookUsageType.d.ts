/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WebhookUsageType: core.serialization.Schema<serializers.WebhookUsageType.Raw, ElevenLabs.WebhookUsageType>;
export declare namespace WebhookUsageType {
    type Raw = "ConvAI Agent Settings" | "ConvAI Settings" | "Voice Library Removal Notices" | "Speech to Text";
}
