/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { NormalizedAlignment } from "./NormalizedAlignment";
import { Alignment } from "./Alignment";
export declare const WebsocketTtsServerMessageMulti: core.serialization.ObjectSchema<serializers.WebsocketTtsServerMessageMulti.Raw, ElevenLabs.WebsocketTtsServerMessageMulti>;
export declare namespace WebsocketTtsServerMessageMulti {
    interface Raw {
        audio?: string | null;
        is_final?: boolean | null;
        normalizedAlignment?: NormalizedAlignment.Raw | null;
        alignment?: Alignment.Raw | null;
        contextId?: string | null;
    }
}
