/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WidgetPlacement: core.serialization.Schema<serializers.WidgetPlacement.Raw, ElevenLabs.WidgetPlacement>;
export declare namespace WidgetPlacement {
    type Raw = "top-left" | "top" | "top-right" | "bottom-left" | "bottom" | "bottom-right";
}
