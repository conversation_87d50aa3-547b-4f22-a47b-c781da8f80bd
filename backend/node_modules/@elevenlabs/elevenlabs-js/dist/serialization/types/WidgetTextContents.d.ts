/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WidgetTextContents: core.serialization.ObjectSchema<serializers.WidgetTextContents.Raw, ElevenLabs.WidgetTextContents>;
export declare namespace WidgetTextContents {
    interface Raw {
        main_label?: string | null;
        start_call?: string | null;
        new_call?: string | null;
        end_call?: string | null;
        mute_microphone?: string | null;
        change_language?: string | null;
        collapse?: string | null;
        expand?: string | null;
        copied?: string | null;
        accept_terms?: string | null;
        dismiss_terms?: string | null;
        listening_status?: string | null;
        speaking_status?: string | null;
        connecting_status?: string | null;
        input_label?: string | null;
        input_placeholder?: string | null;
        user_ended_conversation?: string | null;
        agent_ended_conversation?: string | null;
        conversation_id?: string | null;
        error_occurred?: string | null;
        copy_id?: string | null;
    }
}
