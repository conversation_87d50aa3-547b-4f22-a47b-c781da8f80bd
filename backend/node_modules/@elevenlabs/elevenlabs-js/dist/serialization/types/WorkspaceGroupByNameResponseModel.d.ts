/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WorkspaceGroupByNameResponseModel: core.serialization.ObjectSchema<serializers.WorkspaceGroupByNameResponseModel.Raw, ElevenLabs.WorkspaceGroupByNameResponseModel>;
export declare namespace WorkspaceGroupByNameResponseModel {
    interface Raw {
        name: string;
        id: string;
        members_emails: string[];
    }
}
