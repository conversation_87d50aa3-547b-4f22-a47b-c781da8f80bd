/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
export declare const WorkspaceGroupPermission: core.serialization.Schema<serializers.WorkspaceGroupPermission.Raw, ElevenLabs.WorkspaceGroupPermission>;
export declare namespace WorkspaceGroupPermission {
    type Raw = "text_to_speech" | "speech_to_speech" | "voice_lab" | "sound_effects" | "projects" | "voiceover_studio" | "dubbing" | "audio_native" | "conversational_ai" | "voice_isolator" | "ai_speech_classifier" | "add_voice_from_voice_library" | "create_instant_voice_clone" | "create_professional_voice_clone" | "create_user_api_key" | "publish_studio_project";
}
