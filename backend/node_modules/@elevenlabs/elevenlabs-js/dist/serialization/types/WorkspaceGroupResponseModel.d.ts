/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WorkspaceGroupPermission } from "./WorkspaceGroupPermission";
export declare const WorkspaceGroupResponseModel: core.serialization.ObjectSchema<serializers.WorkspaceGroupResponseModel.Raw, ElevenLabs.WorkspaceGroupResponseModel>;
export declare namespace WorkspaceGroupResponseModel {
    interface Raw {
        name: string;
        id: string;
        members: string[];
        permissions?: WorkspaceGroupPermission.Raw[] | null;
    }
}
