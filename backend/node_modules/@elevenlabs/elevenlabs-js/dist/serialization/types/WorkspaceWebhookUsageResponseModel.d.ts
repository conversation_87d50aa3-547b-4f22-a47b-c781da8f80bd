/**
 * This file was auto-generated by Fern from our API Definition.
 */
import * as serializers from "../index";
import * as ElevenLabs from "../../api/index";
import * as core from "../../core";
import { WebhookUsageType } from "./WebhookUsageType";
export declare const WorkspaceWebhookUsageResponseModel: core.serialization.ObjectSchema<serializers.WorkspaceWebhookUsageResponseModel.Raw, ElevenLabs.WorkspaceWebhookUsageResponseModel>;
export declare namespace WorkspaceWebhookUsageResponseModel {
    interface Raw {
        usage_type: WebhookUsageType.Raw;
    }
}
