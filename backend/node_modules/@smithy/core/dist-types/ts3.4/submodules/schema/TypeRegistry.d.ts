import { Schema as ISchema } from "@smithy/types";
import { ErrorSchema } from "./schemas/ErrorSchema";
/**
 * A way to look up schema by their ShapeId values.
 *
 * @alpha
 */
export declare class TypeRegistry {
    readonly namespace: string;
    private schemas;
    static readonly registries: Map<string, TypeRegistry>;
    private constructor();
    /**
     * @param namespace - specifier.
     * @returns the schema for that namespace, creating it if necessary.
     */
    static for(namespace: string): TypeRegistry;
    /**
     * Adds the given schema to a type registry with the same namespace.
     *
     * @param shapeId - to be registered.
     * @param schema - to be registered.
     */
    register(shapeId: string, schema: ISchema): void;
    /**
     * @param shapeId - query.
     * @returns the schema.
     */
    getSchema(shapeId: string): ISchema;
    /**
     * The smithy-typescript code generator generates a synthetic (i.e. unmodeled) base exception,
     * because generated SDKs before the introduction of schemas have the notion of a ServiceBaseException, which
     * is unique per service/model.
     *
     * This is generated under a unique prefix that is combined with the service namespace, and this
     * method is used to retrieve it.
     *
     * The base exception synthetic schema is used when an error is returned by a service, but we cannot
     * determine what existing schema to use to deserialize it.
     *
     * @returns the synthetic base exception of the service namespace associated with this registry instance.
     */
    getBaseException(): ErrorSchema | undefined;
    /**
     * @param predicate - criterion.
     * @returns a schema in this registry matching the predicate.
     */
    find(predicate: (schema: ISchema) => boolean): ISchema | undefined;
    /**
     * Unloads the current TypeRegistry.
     */
    destroy(): void;
    private normalizeShapeId;
    private getNamespace;
}
