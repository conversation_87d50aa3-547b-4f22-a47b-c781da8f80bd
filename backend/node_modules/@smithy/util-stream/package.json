{"name": "@smithy/util-stream", "version": "4.2.2", "scripts": {"build": "concurrently 'yarn:build:cjs' 'yarn:build:es' 'yarn:build:types && yarn build:types:downlevel'", "build:cjs": "node ../../scripts/inline util-stream", "build:es": "yarn g:tsc -p tsconfig.es.json", "build:types": "yarn g:tsc -p tsconfig.types.json", "build:types:downlevel": "rimraf dist-types/ts3.4 && downlevel-dts dist-types dist-types/ts3.4", "stage-release": "rimraf ./.release && yarn pack && mkdir ./.release && tar zxvf ./package.tgz --directory ./.release && rm ./package.tgz", "clean": "rimraf ./dist-* && rimraf *.tsbuildinfo || exit 0", "lint": "eslint -c ../../.eslintrc.js \"src/**/*.ts\"", "format": "prettier --config ../../prettier.config.js --ignore-path ../../.prettierignore --write \"**/*.{ts,md,json}\"", "extract:docs": "api-extractor run --local", "test": "yarn g:vitest run && yarn test:browser", "test:integration": "yarn g:vitest run -c vitest.config.integ.ts", "test:watch": "yarn g:vitest watch", "test:integration:watch": "yarn g:vitest watch -c vitest.config.integ.ts", "test:browser": "yarn g:vitest run -c vitest.config.browser.ts", "test:browser:watch": "yarn g:vitest watch -c vitest.config.browser.ts"}, "main": "./dist-cjs/index.js", "module": "./dist-es/index.js", "types": "./dist-types/index.d.ts", "author": {"name": "AWS SDK for JavaScript Team", "url": "https://aws.amazon.com/javascript/"}, "license": "Apache-2.0", "dependencies": {"@smithy/fetch-http-handler": "^5.0.4", "@smithy/node-http-handler": "^4.0.6", "@smithy/types": "^4.3.1", "@smithy/util-base64": "^4.0.0", "@smithy/util-buffer-from": "^4.0.0", "@smithy/util-hex-encoding": "^4.0.0", "@smithy/util-utf8": "^4.0.0", "tslib": "^2.6.2"}, "devDependencies": {"@smithy/util-test": "^0.2.8", "@types/node": "^18.11.9", "concurrently": "7.0.0", "downlevel-dts": "0.10.1", "rimraf": "3.0.2", "typedoc": "0.23.23"}, "engines": {"node": ">=18.0.0"}, "typesVersions": {"<4.0": {"dist-types/*": ["dist-types/ts3.4/*"]}}, "files": ["dist-*/**"], "browser": {"./dist-es/checksum/ChecksumStream": "./dist-es/checksum/ChecksumStream.browser", "./dist-es/checksum/createChecksumStream": "./dist-es/checksum/createChecksumStream.browser", "./dist-es/createBufferedReadable": "./dist-es/createBufferedReadableStream", "./dist-es/getAwsChunkedEncodingStream": "./dist-es/getAwsChunkedEncodingStream.browser", "./dist-es/headStream": "./dist-es/headStream.browser", "./dist-es/sdk-stream-mixin": "./dist-es/sdk-stream-mixin.browser", "./dist-es/splitStream": "./dist-es/splitStream.browser"}, "react-native": {"./dist-es/checksum/createChecksumStream": "./dist-es/checksum/createChecksumStream.browser", "./dist-es/checksum/ChecksumStream": "./dist-es/checksum/ChecksumStream.browser", "./dist-es/getAwsChunkedEncodingStream": "./dist-es/getAwsChunkedEncodingStream.browser", "./dist-es/sdk-stream-mixin": "./dist-es/sdk-stream-mixin.browser", "./dist-es/headStream": "./dist-es/headStream.browser", "./dist-es/splitStream": "./dist-es/splitStream.browser", "./dist-es/createBufferedReadable": "./dist-es/createBufferedReadableStream", "./dist-cjs/checksum/createChecksumStream": "./dist-cjs/checksum/createChecksumStream.browser", "./dist-cjs/checksum/ChecksumStream": "./dist-cjs/checksum/ChecksumStream.browser", "./dist-cjs/getAwsChunkedEncodingStream": "./dist-cjs/getAwsChunkedEncodingStream.browser", "./dist-cjs/sdk-stream-mixin": "./dist-cjs/sdk-stream-mixin.browser", "./dist-cjs/headStream": "./dist-cjs/headStream.browser", "./dist-cjs/splitStream": "./dist-cjs/splitStream.browser", "./dist-cjs/createBufferedReadable": "./dist-cjs/createBufferedReadableStream"}, "homepage": "https://github.com/smithy-lang/smithy-typescript/tree/main/packages/util-stream", "repository": {"type": "git", "url": "https://github.com/smithy-lang/smithy-typescript.git", "directory": "packages/util-stream"}, "typedoc": {"entryPoint": "src/index.ts"}, "publishConfig": {"directory": ".release/package"}}