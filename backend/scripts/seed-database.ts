import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { SupabaseService } from '../src/config/supabase.service';
import * as bcrypt from 'bcrypt';

interface SeedUser {
  email: string;
  password: string;
  name: string;
  role: 'patient' | 'doctor' | 'hospital' | 'admin' | 'insurance';
  avatar?: string;
  profile?: any;
}

interface SeedMedicine {
  name: string;
  dosage: string;
  frequency: string;
  duration: number;
  instructions: string;
  sideEffects?: string;
  startDate: Date;
  endDate: Date;
  patientEmail: string;
  prescriptionId: string;
}

interface SeedPrescription {
  patientEmail: string;
  doctorEmail?: string;
  filename: string;
  fileUrl: string;
  status: 'processing' | 'completed' | 'failed';
  extractedText?: string;
}

async function bootstrap() {
  const app = await NestFactory.createApplicationContext(AppModule);

  const supabaseService = app.get(SupabaseService);
  const supabase = supabaseService.getAdminClient();

  console.log('🌱 Starting database seeding...');

  try {
    // Seed Users
    console.log('👥 Seeding users...');
    const seedUsers: SeedUser[] = [
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'John Doe',
        role: 'patient',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=john',
        profile: {
          date_of_birth: '1985-06-15',
          emergency_contact: '******-0102',
        },
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Jane Smith',
        role: 'patient',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=jane',
        profile: {
          date_of_birth: '1990-03-22',
          emergency_contact: '******-0104',
        },
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Dr. Sarah Wilson',
        role: 'doctor',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=sarah',
        profile: {
          specialization: 'Internal Medicine',
          license_number: 'MD123456',
        },
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'Dr. Michael Chen',
        role: 'doctor',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=michael',
        profile: {
          specialization: 'Cardiology',
          license_number: 'MD789012',
        },
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'City General Hospital',
        role: 'hospital',
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=CGH',
        profile: {
          address: '789 Hospital Blvd, Medical District, USA',
          phone: '******-0301',
          website: 'https://citygeneral.com',
        },
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'HealthGuard Insurance',
        role: 'insurance',
        avatar: 'https://api.dicebear.com/7.x/initials/svg?seed=HGI',
        profile: {
          company_name: 'HealthGuard Insurance Co.',
          address: '123 Insurance Plaza, Business District, USA',
          phone: '******-0401',
          website: 'https://healthguard.com',
        },
      },
      {
        email: '<EMAIL>',
        password: 'password123',
        name: 'System Administrator',
        role: 'admin',
        avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
      },
    ];

    const createdUsers = new Map<string, any>();

    for (const userData of seedUsers) {
      try {
        // Check if user already exists
        const { data: existingUser } = await supabase
          .from('users')
          .select('*')
          .eq('email', userData.email)
          .single();

        if (existingUser) {
          console.log(`⚠️  User ${userData.email} already exists, skipping...`);
          createdUsers.set(userData.email, existingUser);
          continue;
        }

        // Create user
        const { data: user, error: userError } = await supabase
          .from('users')
          .insert({
            email: userData.email,
            name: userData.name,
            role: userData.role,
            avatar: userData.avatar,
          })
          .select()
          .single();

        if (userError) {
          console.log(`❌ Error creating user ${userData.email}:`, userError.message);
          continue;
        }

        // Create role-specific record if profile data exists
        if (userData.profile && userData.role !== 'admin') {
          const tableName = userData.role === 'patient' ? 'patients' :
                           userData.role === 'doctor' ? 'doctors' :
                           userData.role === 'hospital' ? 'hospitals' :
                           userData.role === 'insurance' ? 'insurance_providers' : null;

          if (tableName) {
            const profileData = { id: user.id, ...userData.profile };
            const { error: profileError } = await supabase
              .from(tableName)
              .insert(profileData);

            if (profileError) {
              console.log(`⚠️  Error creating ${userData.role} profile for ${userData.email}:`, profileError.message);
            }
          }
        }

        createdUsers.set(userData.email, user);
        console.log(`✅ Created user: ${userData.name} (${userData.email})`);
      } catch (error) {
        console.log(`❌ Error processing user ${userData.email}:`, error.message);
      }
    }

    // Seed Achievements
    console.log('🏆 Seeding achievements...');
    const seedAchievements = [
      {
        name: 'First Step',
        description: 'Complete your first medication dose',
        icon: 'trophy',
        category: 'milestone' as const,
        requirement: 1,
        points: 10,
      },
      {
        name: 'Week Warrior',
        description: 'Maintain a 7-day streak',
        icon: 'flame',
        category: 'streak' as const,
        requirement: 7,
        points: 50,
      },
      {
        name: 'Perfect Week',
        description: 'Complete all medications for a week',
        icon: 'star',
        category: 'consistency' as const,
        requirement: 100,
        points: 75,
      },
      {
        name: 'Month Master',
        description: 'Maintain a 30-day streak',
        icon: 'crown',
        category: 'streak' as const,
        requirement: 30,
        points: 200,
      },
      {
        name: 'Consistency Champion',
        description: 'Achieve 95% adherence rate',
        icon: 'medal',
        category: 'consistency' as const,
        requirement: 95,
        points: 150,
      },
      {
        name: 'Early Bird',
        description: 'Take morning medications on time for 7 days',
        icon: 'sunrise',
        category: 'consistency' as const,
        requirement: 7,
        points: 30,
      },
      {
        name: 'Night Owl',
        description: 'Take evening medications on time for 7 days',
        icon: 'moon',
        category: 'consistency' as const,
        requirement: 7,
        points: 30,
      },
      {
        name: 'Streak Master',
        description: 'Maintain a 100-day streak',
        icon: 'fire',
        category: 'streak' as const,
        requirement: 100,
        points: 500,
      },
    ];

    for (const achievement of seedAchievements) {
      try {
        // Check if achievement already exists
        const { data: existingAchievement } = await supabase
          .from('achievements')
          .select('*')
          .eq('name', achievement.name)
          .single();

        if (existingAchievement) {
          console.log(`⚠️  Achievement ${achievement.name} already exists, skipping...`);
          continue;
        }

        const { error } = await supabase
          .from('achievements')
          .insert(achievement);

        if (error) {
          console.log(`❌ Error creating achievement ${achievement.name}:`, error.message);
        } else {
          console.log(`✅ Created achievement: ${achievement.name}`);
        }
      } catch (error) {
        console.log(`❌ Error processing achievement ${achievement.name}:`, error.message);
      }
    }

    // Seed Prescriptions
    console.log('📋 Seeding prescriptions...');
    const seedPrescriptions: SeedPrescription[] = [
      {
        patientEmail: '<EMAIL>',
        doctorEmail: '<EMAIL>',
        filename: 'prescription-diabetes-jan-2024.pdf',
        fileUrl: '/uploads/prescription-diabetes-jan-2024.pdf',
        status: 'completed',
        extractedText: 'Metformin 850mg twice daily with meals, Lisinopril 10mg once daily in morning',
      },
      {
        patientEmail: '<EMAIL>',
        doctorEmail: '<EMAIL>',
        filename: 'prescription-gastro-jan-2024.pdf',
        fileUrl: '/uploads/prescription-gastro-jan-2024.pdf',
        status: 'completed',
        extractedText: 'Omeprazole 20mg once daily before breakfast for 14 days',
      },
      {
        patientEmail: '<EMAIL>',
        doctorEmail: '<EMAIL>',
        filename: 'prescription-hypertension-jan-2024.pdf',
        fileUrl: '/uploads/prescription-hypertension-jan-2024.pdf',
        status: 'completed',
        extractedText: 'Amlodipine 5mg once daily, Atorvastatin 20mg once daily at bedtime',
      },
    ];

    const createdPrescriptions = new Map<string, any>();
    
    for (const prescriptionData of seedPrescriptions) {
      try {
        const patient = createdUsers.get(prescriptionData.patientEmail);
        const doctor = prescriptionData.doctorEmail ? createdUsers.get(prescriptionData.doctorEmail) : null;

        if (patient) {
          // Check if prescription already exists
          const { data: existingPrescription } = await supabase
            .from('prescriptions')
            .select('*')
            .eq('filename', prescriptionData.filename)
            .single();

          if (existingPrescription) {
            console.log(`⚠️  Prescription ${prescriptionData.filename} already exists, skipping...`);
            createdPrescriptions.set(prescriptionData.filename, existingPrescription);
            continue;
          }

          const { data: prescription, error } = await supabase
            .from('prescriptions')
            .insert({
              patient_id: patient.id,
              doctor_id: doctor?.id,
              filename: prescriptionData.filename,
              file_url: prescriptionData.fileUrl,
              status: prescriptionData.status,
              extracted_text: prescriptionData.extractedText,
              uploaded_at: new Date().toISOString(),
            })
            .select()
            .single();

          if (error) {
            console.log(`❌ Error creating prescription ${prescriptionData.filename}:`, error.message);
          } else {
            createdPrescriptions.set(prescriptionData.filename, prescription);
            console.log(`✅ Created prescription: ${prescriptionData.filename}`);
          }
        }
      } catch (error) {
        console.log(`❌ Error processing prescription ${prescriptionData.filename}:`, error.message);
      }
    }

    // Seed Medicines (after prescriptions are created)
    console.log('💊 Seeding medicines...');
    const seedMedicines: SeedMedicine[] = [
      {
        name: 'Metformin',
        dosage: '850mg',
        frequency: 'Twice daily',
        duration: 30,
        instructions: 'Take with meals to reduce stomach upset',
        sideEffects: 'Nausea, diarrhea, metallic taste',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        patientEmail: '<EMAIL>',
        prescriptionId: 'prescription-diabetes-jan-2024.pdf',
      },
      {
        name: 'Lisinopril',
        dosage: '10mg',
        frequency: 'Once daily',
        duration: 30,
        instructions: 'Take in the morning, preferably at the same time each day',
        sideEffects: 'Dry cough, dizziness, hyperkalemia',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        patientEmail: '<EMAIL>',
        prescriptionId: 'prescription-diabetes-jan-2024.pdf',
      },
      {
        name: 'Omeprazole',
        dosage: '20mg',
        frequency: 'Once daily',
        duration: 14,
        instructions: 'Take 30 minutes before breakfast',
        sideEffects: 'Headache, nausea, abdominal pain',
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-01-29'),
        patientEmail: '<EMAIL>',
        prescriptionId: 'prescription-gastro-jan-2024.pdf',
      },
      {
        name: 'Amlodipine',
        dosage: '5mg',
        frequency: 'Once daily',
        duration: 30,
        instructions: 'Take at the same time each day, with or without food',
        sideEffects: 'Swelling of ankles, dizziness, flushing',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        patientEmail: '<EMAIL>',
        prescriptionId: 'prescription-hypertension-jan-2024.pdf',
      },
      {
        name: 'Atorvastatin',
        dosage: '20mg',
        frequency: 'Once daily',
        duration: 30,
        instructions: 'Take at bedtime for maximum effectiveness',
        sideEffects: 'Muscle pain, liver problems, memory issues',
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-01-31'),
        patientEmail: '<EMAIL>',
        prescriptionId: 'prescription-hypertension-jan-2024.pdf',
      },
    ];

    const createdMedicines = new Map<string, any>();

    for (const medicineData of seedMedicines) {
      try {
        const patient = createdUsers.get(medicineData.patientEmail);
        const prescription = createdPrescriptions.get(medicineData.prescriptionId);

        if (patient && prescription) {
          // Check if medicine already exists
          const { data: existingMedicine } = await supabase
            .from('medicines')
            .select('*')
            .eq('name', medicineData.name)
            .eq('patient_id', patient.id)
            .single();

          if (existingMedicine) {
            console.log(`⚠️  Medicine ${medicineData.name} for ${patient.name} already exists, skipping...`);
            createdMedicines.set(`${medicineData.name}-${medicineData.patientEmail}`, existingMedicine);
            continue;
          }

          const { data: medicine, error } = await supabase
            .from('medicines')
            .insert({
              name: medicineData.name,
              dosage: medicineData.dosage,
              frequency: medicineData.frequency,
              duration: medicineData.duration,
              instructions: medicineData.instructions,
              side_effects: medicineData.sideEffects,
              start_date: medicineData.startDate.toISOString().split('T')[0],
              end_date: medicineData.endDate.toISOString().split('T')[0],
              prescription_id: prescription.id,
              patient_id: patient.id,
            })
            .select()
            .single();

          if (error) {
            console.log(`❌ Error creating medicine ${medicineData.name}:`, error.message);
          } else {
            createdMedicines.set(`${medicineData.name}-${medicineData.patientEmail}`, medicine);
            console.log(`✅ Created medicine: ${medicineData.name} for ${patient.name}`);
          }
        } else {
          console.log(`⚠️  Missing patient or prescription for medicine ${medicineData.name}`);
        }
      } catch (error) {
        console.log(`❌ Error processing medicine ${medicineData.name}:`, error.message);
      }
    }

    // Seed some adherence records and reminders
    console.log('📊 Seeding adherence records...');
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    for (const [key, medicine] of createdMedicines) {
      try {
        // Create some historical adherence records
        for (let i = 0; i < 7; i++) {
          const recordDate = new Date(oneWeekAgo.getTime() + i * 24 * 60 * 60 * 1000);
          const status = Math.random() > 0.2 ? 'taken' : (Math.random() > 0.5 ? 'missed' : 'skipped');

          const { error: adherenceError } = await supabase
            .from('adherence_records')
            .insert({
              patient_id: medicine.patient_id,
              medicine_id: medicine.id,
              scheduled_time: recordDate.toISOString(),
              taken_time: status === 'taken' ? recordDate.toISOString() : null,
              status,
              notes: status === 'taken' ? 'Taken as prescribed' :
                     status === 'missed' ? 'Forgot to take' : 'Skipped due to side effects',
            });

          if (adherenceError) {
            console.log(`⚠️  Error creating adherence record:`, adherenceError.message);
          }
        }

        // Create upcoming reminders
        const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000);
        const { error: reminderError } = await supabase
          .from('reminders')
          .insert({
            medicine_id: medicine.id,
            patient_id: medicine.patient_id,
            scheduled_time: tomorrow.toISOString(),
            reminder_type: 'notification',
            status: 'pending',
          });

        if (reminderError) {
          console.log(`⚠️  Error creating reminder:`, reminderError.message);
        }

        console.log(`✅ Created adherence records and reminders for ${medicine.name}`);
      } catch (error) {
        console.log(`❌ Error creating adherence data for ${medicine.name}:`, error.message);
      }
    }

    // Initialize gamification stats for patients
    console.log('🎮 Initializing gamification stats...');
    for (const [email, user] of createdUsers) {
      if (user.role === 'patient') {
        try {
          // Check if gamification stats already exist
          const { data: existingStats } = await supabase
            .from('gamification_stats')
            .select('*')
            .eq('patient_id', user.id)
            .single();

          if (existingStats) {
            console.log(`⚠️  Gamification stats for ${user.name} already exist, skipping...`);
            continue;
          }

          const { error } = await supabase
            .from('gamification_stats')
            .insert({
              patient_id: user.id,
              current_streak: 0,
              longest_streak: 0,
              total_points: 0,
              completion_rate: 0.00,
              weekly_progress: [0, 0, 0, 0, 0, 0, 0],
              monthly_progress: Array(30).fill(0),
            });

          if (error) {
            console.log(`❌ Error initializing gamification for ${user.name}:`, error.message);
          } else {
            console.log(`✅ Initialized gamification stats for ${user.name}`);
          }
        } catch (error) {
          console.log(`❌ Error processing gamification for ${user.name}:`, error.message);
        }
      }
    }

    console.log('✅ Database seeding completed successfully!');
    console.log('\n📊 Seeding Summary:');
    console.log(`👥 Users: ${createdUsers.size}`);
    console.log(`🏆 Achievements: ${seedAchievements.length}`);
    console.log(`📋 Prescriptions: ${createdPrescriptions.size}`);
    console.log(`💊 Medicines: ${createdMedicines.size}`);
    console.log(`📊 Adherence records: ${createdMedicines.size * 7}`);
    console.log(`⏰ Reminders: ${createdMedicines.size}`);
    
  } catch (error) {
    console.error('❌ Error during seeding:', error);
  } finally {
    await app.close();
  }
}

bootstrap();
