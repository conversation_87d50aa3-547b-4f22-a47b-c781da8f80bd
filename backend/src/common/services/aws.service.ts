import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TextractClient, DetectDocumentTextCommand, AnalyzeDocumentCommand } from '@aws-sdk/client-textract';
import { S3Client, PutObjectCommand, GetObjectCommand } from '@aws-sdk/client-s3';
import { OpenAiService } from './openai.service';

@Injectable()
export class AwsService {
  private readonly logger = new Logger(AwsService.name);
  private readonly textractClient: TextractClient;
  private readonly s3Client: S3Client;
  private readonly bucketName: string;

  constructor(
    private readonly configService: ConfigService,
    private readonly openAiService: OpenAiService
  ) {
    const region = this.configService.get<string>('AWS_REGION', 'us-east-1');
    const accessKeyId = this.configService.get<string>('AWS_ACCESS_KEY_ID');
    const secretAccessKey = this.configService.get<string>('AWS_SECRET_ACCESS_KEY');

    if (!accessKeyId || !secretAccessKey) {
      throw new Error('AWS credentials are required. Please set AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY environment variables.');
    }

    this.textractClient = new TextractClient({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });

    this.s3Client = new S3Client({
      region,
      credentials: {
        accessKeyId,
        secretAccessKey,
      },
    });

    this.bucketName = this.configService.get<string>('AWS_S3_BUCKET_NAME', 'medcare-prescriptions');
  }

  async uploadFileToS3(file: Buffer, fileName: string, contentType: string): Promise<string> {
    try {
      const key = `prescriptions/${Date.now()}-${fileName}`;
      
      const command = new PutObjectCommand({
        Bucket: this.bucketName,
        Key: key,
        Body: file,
        ContentType: contentType,
      });

      await this.s3Client.send(command);
      
      // Return the S3 URL
      const region = this.configService.get<string>('AWS_REGION', 'us-east-1');
      return `https://${this.bucketName}.s3.${region}.amazonaws.com/${key}`;
    } catch (error) {
      this.logger.error('Failed to upload file to S3:', error);
      throw new Error('Failed to upload file to S3');
    }
  }

  async extractTextFromPrescription(fileBuffer: Buffer, mimeType?: string): Promise<{
    extractedText: string;
    confidence: number;
    medicineInfo: any[];
    aiExtractedMedicines: string[];
    aiExtractionSuccess: boolean;
  }> {
    try {
      this.logger.log(`Starting text extraction for file type: ${mimeType || 'unknown'}, size: ${fileBuffer.length} bytes`);

      // Check file size (AWS Textract has limits)
      const maxSize = 10 * 1024 * 1024; // 10MB
      if (fileBuffer.length > maxSize) {
        throw new Error(`File too large for Textract processing. Maximum size: ${maxSize / (1024 * 1024)}MB`);
      }

      // Try AnalyzeDocument first for better medical document analysis
      let response;
      try {
        const analyzeCommand = new AnalyzeDocumentCommand({
          Document: {
            Bytes: fileBuffer,
          },
          FeatureTypes: ['FORMS', 'TABLES'], // Extract forms and tables for better prescription parsing
        });

        response = await this.textractClient.send(analyzeCommand);
        this.logger.log('Successfully used AnalyzeDocument command');
      } catch (analyzeError: any) {
        this.logger.warn('AnalyzeDocument failed, trying DetectDocumentText:', analyzeError.message);

        // Fallback to simpler DetectDocumentText
        const detectCommand = new DetectDocumentTextCommand({
          Document: {
            Bytes: fileBuffer,
          },
        });

        response = await this.textractClient.send(detectCommand);
        this.logger.log('Successfully used DetectDocumentText command');
      }

      if (!response.Blocks || response.Blocks.length === 0) {
        throw new Error('No text blocks found in document - document may be empty or unreadable');
      }

      // Extract text from blocks
      const textBlocks = response.Blocks.filter(block => block.BlockType === 'LINE');
      const extractedText = textBlocks
        .map(block => block.Text)
        .filter(text => text && text.trim())
        .join('\n');

      if (!extractedText || extractedText.trim().length === 0) {
        throw new Error('No readable text found in document');
      }

      // Calculate average confidence
      const confidenceScores = textBlocks
        .map(block => block.Confidence || 0)
        .filter(confidence => confidence > 0);

      const averageConfidence = confidenceScores.length > 0
        ? confidenceScores.reduce((sum, conf) => sum + conf, 0) / confidenceScores.length
        : 0;

      // Extract potential medicine information using forms and key-value pairs
      const medicineInfo = this.extractMedicineInformation(response.Blocks);

      // Use OpenAI to extract medicine names from the extracted text
      const aiMedicineExtraction = await this.openAiService.extractMedicineNames(extractedText);

      this.logger.log(`Text extraction successful: ${extractedText.length} characters, ${averageConfidence.toFixed(2)}% confidence`);

      if (aiMedicineExtraction.success) {
        this.logger.log(`AI extracted ${aiMedicineExtraction.medicines.length} medicine names: ${aiMedicineExtraction.medicines.join(', ')}`);
      } else {
        this.logger.warn(`AI medicine extraction failed: ${aiMedicineExtraction.error}`);
      }

      return {
        extractedText,
        confidence: averageConfidence,
        medicineInfo,
        aiExtractedMedicines: aiMedicineExtraction.medicines,
        aiExtractionSuccess: aiMedicineExtraction.success,
      };
    } catch (error: any) {
      this.logger.error('Failed to extract text from prescription:', error);

      // Provide more specific error messages
      if (error.name === 'UnsupportedDocumentException') {
        throw new Error(`Unsupported document format. AWS Textract supports: JPEG, PNG, TIFF, BMP images and single-page PDF files. Error: ${error.message}`);
      } else if (error.name === 'InvalidParameterException') {
        throw new Error(`Invalid document parameters. Please ensure the file is a valid image or PDF. Error: ${error.message}`);
      } else if (error.name === 'DocumentTooLargeException') {
        throw new Error(`Document too large for processing. Maximum size is 10MB. Error: ${error.message}`);
      } else if (error.message && error.message.includes('text')) {
        throw new Error(error.message);
      } else {
        throw new Error(`Failed to extract text from prescription: ${error.message || 'Unknown error'}`);
      }
    }
  }

  private extractMedicineInformation(blocks: any[]): any[] {
    const medicines: any[] = [];
    
    try {
      // Look for key-value pairs that might contain medicine information
      const keyValueBlocks = blocks.filter(block => block.BlockType === 'KEY_VALUE_SET');
      const keyBlocks = keyValueBlocks.filter(block => block.EntityTypes?.includes('KEY'));
      const valueBlocks = keyValueBlocks.filter(block => block.EntityTypes?.includes('VALUE'));

      // Create a map of key-value relationships
      const keyValueMap = new Map();
      
      keyBlocks.forEach(keyBlock => {
        if (keyBlock.Relationships) {
          const valueRelationship = keyBlock.Relationships.find(rel => rel.Type === 'VALUE');
          if (valueRelationship && valueRelationship.Ids) {
            const valueBlock = valueBlocks.find(vb => valueRelationship.Ids.includes(vb.Id));
            if (valueBlock) {
              const keyText = this.getTextFromBlock(keyBlock, blocks);
              const valueText = this.getTextFromBlock(valueBlock, blocks);
              keyValueMap.set(keyText.toLowerCase(), valueText);
            }
          }
        }
      });

      // Look for common prescription patterns
      const medicineKeywords = [
        'medicine', 'medication', 'drug', 'tablet', 'capsule', 'syrup',
        'dosage', 'dose', 'frequency', 'duration', 'instructions'
      ];

      keyValueMap.forEach((value, key) => {
        if (medicineKeywords.some(keyword => key.includes(keyword))) {
          medicines.push({
            field: key,
            value: value,
            type: this.categorizeField(key)
          });
        }
      });

      // Also extract from table data if available
      const tableBlocks = blocks.filter(block => block.BlockType === 'TABLE');
      tableBlocks.forEach(table => {
        const tableData = this.extractTableData(table, blocks);
        if (tableData.length > 0) {
          medicines.push({
            type: 'table',
            data: tableData
          });
        }
      });

    } catch (error) {
      this.logger.warn('Failed to extract structured medicine information:', error);
    }

    return medicines;
  }

  private getTextFromBlock(block: any, allBlocks: any[]): string {
    if (block.Text) {
      return block.Text;
    }

    if (block.Relationships) {
      const childRelationship = block.Relationships.find(rel => rel.Type === 'CHILD');
      if (childRelationship && childRelationship.Ids) {
        const childTexts = childRelationship.Ids
          .map(id => allBlocks.find(b => b.Id === id))
          .filter(b => b && b.Text)
          .map(b => b.Text);
        return childTexts.join(' ');
      }
    }

    return '';
  }

  private categorizeField(fieldName: string): string {
    const lowerField = fieldName.toLowerCase();
    
    if (lowerField.includes('medicine') || lowerField.includes('medication') || lowerField.includes('drug')) {
      return 'medicine_name';
    }
    if (lowerField.includes('dosage') || lowerField.includes('dose')) {
      return 'dosage';
    }
    if (lowerField.includes('frequency')) {
      return 'frequency';
    }
    if (lowerField.includes('duration')) {
      return 'duration';
    }
    if (lowerField.includes('instruction')) {
      return 'instructions';
    }
    
    return 'other';
  }

  private extractTableData(tableBlock: any, allBlocks: any[]): any[] {
    const tableData: any[] = [];
    
    try {
      if (tableBlock.Relationships) {
        const cellRelationship = tableBlock.Relationships.find(rel => rel.Type === 'CHILD');
        if (cellRelationship && cellRelationship.Ids) {
          const cells = cellRelationship.Ids
            .map(id => allBlocks.find(b => b.Id === id))
            .filter(b => b && b.BlockType === 'CELL');

          // Group cells by row
          const rowMap = new Map();
          cells.forEach(cell => {
            const rowIndex = cell.RowIndex || 0;
            if (!rowMap.has(rowIndex)) {
              rowMap.set(rowIndex, []);
            }
            rowMap.get(rowIndex).push({
              column: cell.ColumnIndex || 0,
              text: this.getTextFromBlock(cell, allBlocks)
            });
          });

          // Convert to array format
          rowMap.forEach((cells, rowIndex) => {
            const sortedCells = cells.sort((a, b) => a.column - b.column);
            tableData.push({
              row: rowIndex,
              cells: sortedCells.map(cell => cell.text)
            });
          });
        }
      }
    } catch (error) {
      this.logger.warn('Failed to extract table data:', error);
    }

    return tableData;
  }
}
