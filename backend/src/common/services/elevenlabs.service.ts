import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ElevenLabsClient } from '@elevenlabs/elevenlabs-js';

export interface ConversationalAgentOptions {
  agentId: string;
  phoneNumber: string;
  patientName: string;
  medicineName: string;
  dosage: string;
}

export interface TextToSpeechOptions {
  text: string;
  voiceId?: string;
  modelId?: string;
}

export interface ConversationalCallResponse {
  success: boolean;
  callId?: string;
  error?: string;
}

export interface TextToSpeechResponse {
  success: boolean;
  audioBuffer?: Buffer;
  error?: string;
}

@Injectable()
export class ElevenLabsService {
  private readonly logger = new Logger(ElevenLabsService.name);
  private readonly elevenLabsClient: ElevenLabsClient;
  private readonly isEnabled: boolean;
  private readonly defaultVoiceId: string;
  private readonly agentId: string;
  private readonly agentPhoneNumberId: string;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('ELEVENLABS_API_KEY');
    this.defaultVoiceId = this.configService.get<string>('ELEVENLABS_VOICE_ID', 'pNInz6obpgDQGcFmaJgB'); // Default to Adam voice
    this.agentId = this.configService.get<string>('ELEVENLABS_AGENT_ID', '');
    this.agentPhoneNumberId = this.configService.get<string>('ELEVENLABS_AGENT_PHONE_NUMBER_ID', '');
    
    // Check if ElevenLabs is properly configured
    this.isEnabled = !!(apiKey);
    
    if (this.isEnabled) {
      this.elevenLabsClient = new ElevenLabsClient({
        apiKey: apiKey,
      });
      this.logger.log('ElevenLabs service initialized successfully');
    } else {
      this.logger.warn('ElevenLabs service disabled - missing configuration (ELEVENLABS_API_KEY)');
    }
  }

  async makeConversationalCall(options: ConversationalAgentOptions): Promise<ConversationalCallResponse> {
    if (!this.isEnabled) {
      this.logger.warn('ElevenLabs not configured - conversational call not made');
      return { success: false, error: 'ElevenLabs not configured' };
    }

    if (!this.agentId || !this.agentPhoneNumberId) {
      this.logger.warn('ElevenLabs agent not configured - conversational call not made');
      return { success: false, error: 'ElevenLabs agent not configured' };
    }

    try {
      // Make outbound call via ElevenLabs conversational AI
      const response = await this.elevenLabsClient.conversationalAi.twilio.outboundCall({
        agentId: this.agentId,
        agentPhoneNumberId: this.agentPhoneNumberId,
        toNumber: options.phoneNumber,
        // Note: customLlmExtraBody may not be available in current API version
        // customLlmExtraBody: {
        //   context: `You are calling ${options.patientName} to remind them about their medication: ${options.medicineName} (${options.dosage}). Be friendly, helpful, and professional. Keep the call brief but ensure they understand the importance of taking their medication as prescribed.`,
        // },
      });

      this.logger.log(`Conversational call initiated successfully to ${options.phoneNumber}`);
      return { success: true, callId: response.callSid };
    } catch (error) {
      this.logger.error(`Failed to make conversational call to ${options.phoneNumber}:`, error);
      return { success: false, error: error.message };
    }
  }

  async generateSpeech(options: TextToSpeechOptions): Promise<TextToSpeechResponse> {
    if (!this.isEnabled) {
      this.logger.warn('ElevenLabs not configured - speech generation skipped');
      return { success: false, error: 'ElevenLabs not configured' };
    }

    try {
      const audio = await this.elevenLabsClient.textToSpeech.convert(
        options.voiceId || this.defaultVoiceId,
        {
          text: options.text,
          modelId: options.modelId || 'eleven_monolingual_v1',
          voiceSettings: {
            stability: 0.5,
            similarityBoost: 0.75,
          },
        }
      );

      // Convert audio stream to buffer
      const chunks: Buffer[] = [];
      for await (const chunk of audio) {
        chunks.push(Buffer.from(chunk));
      }
      const audioBuffer = Buffer.concat(chunks);

      this.logger.log(`Speech generated successfully for text: "${options.text.substring(0, 50)}..."`);
      return { success: true, audioBuffer };
    } catch (error) {
      this.logger.error(`Failed to generate speech:`, error);
      return { success: false, error: error.message };
    }
  }

  async generateMedicationReminderSpeech(
    patientName: string,
    medicineName: string,
    dosage: string,
    voiceId?: string
  ): Promise<TextToSpeechResponse> {
    const message = this.generateMedicationReminderMessage(patientName, medicineName, dosage);
    
    return this.generateSpeech({
      text: message,
      voiceId: voiceId || this.defaultVoiceId,
    });
  }

  private generateMedicationReminderMessage(
    patientName: string,
    medicineName: string,
    dosage: string
  ): string {
    return `Hello ${patientName}, this is your friendly medication reminder from MedCare. It's time to take your ${medicineName}, ${dosage}. Please remember to take it as prescribed by your doctor. Taking your medication on time is important for your health and recovery. If you have any questions about your medication, please consult with your healthcare provider. Thank you for trusting MedCare with your health journey. Have a wonderful day!`;
  }

  // Get available voices
  async getAvailableVoices(): Promise<any[]> {
    if (!this.isEnabled) {
      return [];
    }

    try {
      const voices = await this.elevenLabsClient.voices.getAll();
      return voices.voices || [];
    } catch (error) {
      this.logger.error('Failed to fetch available voices:', error);
      return [];
    }
  }

  // Get service status
  getServiceStatus(): { 
    enabled: boolean; 
    configured: boolean; 
    agentConfigured: boolean;
    voiceId: string;
  } {
    return {
      enabled: this.isEnabled,
      configured: !!this.configService.get('ELEVENLABS_API_KEY'),
      agentConfigured: !!(this.agentId && this.agentPhoneNumberId),
      voiceId: this.defaultVoiceId,
    };
  }

  // Create a conversational agent (if needed)
  async createAgent(name: string, prompt: string): Promise<any> {
    if (!this.isEnabled) {
      throw new Error('ElevenLabs not configured');
    }

    try {
      const agent = await this.elevenLabsClient.conversationalAi.agents.create({
        name: name,
        conversationConfig: {
          agent: {
            prompt: {
              prompt: prompt || 'You are a helpful medical assistant for medication reminders.',
            },
          },
        },
      });

      this.logger.log(`Agent created successfully: ${agent.agentId}`);
      return agent;
    } catch (error) {
      this.logger.error('Failed to create agent:', error);
      throw error;
    }
  }

  // Batch generate speech for multiple messages
  async generateBulkSpeech(
    messages: Array<{ text: string; voiceId?: string; fileName?: string }>
  ): Promise<Array<TextToSpeechResponse & { fileName?: string }>> {
    if (!this.isEnabled) {
      return messages.map(m => ({ 
        fileName: m.fileName,
        success: false, 
        error: 'ElevenLabs not configured' 
      }));
    }

    const results: Array<TextToSpeechResponse & { fileName: string | undefined }> = [];

    for (const message of messages) {
      const result = await this.generateSpeech({
        text: message.text,
        voiceId: message.voiceId,
      });

      results.push({
        fileName: message.fileName,
        ...result,
      });

      // Add small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    return results;
  }
}
