export type UserRole = 'patient' | 'doctor' | 'hospital' | 'admin' | 'insurance';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  avatar?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Patient extends User {
  role: 'patient';
  date_of_birth?: Date;
  emergency_contact?: string;
  assigned_doctor_id?: string;
  insurance_id?: string;
}

export interface Doctor extends User {
  role: 'doctor';
  specialization: string;
  license_number: string;
  hospital_id?: string;
}

export interface Hospital extends User {
  role: 'hospital';
  address: string;
  phone: string;
  website?: string;
}

export interface Insurance extends User {
  role: 'insurance';
  company_name: string;
  address: string;
  phone: string;
  website?: string;
  policy_types: string[];
  coverage_areas: string[];
}

export interface Medicine {
  id: string;
  name: string;
  dosage: string;
  frequency: string;
  duration: number; // in days
  instructions: string;
  side_effects?: string;
  start_date: Date;
  end_date: Date;
  prescription_id: string;
  patient_id: string;
  created_at: Date;
  updated_at: Date;
}

export interface Prescription {
  id: string;
  patient_id: string;
  doctor_id?: string;
  uploaded_at: Date;
  filename: string;
  file_url: string;
  status: 'processing' | 'completed' | 'failed';
  extracted_text?: string;
  textract_confidence?: number;
  medicine_info?: any[];
  error_message?: string;
  created_at: Date;
  updated_at: Date;
}



export interface AdherenceRecord {
  id: string;
  patient_id: string;
  medicine_id: string;
  scheduled_time: Date;
  taken_time?: Date;
  status: 'taken' | 'missed' | 'skipped';
  notes?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Reminder {
  id: string;
  patient_id: string;
  medicine_id: string;
  reminder_type: 'sms' | 'call' | 'both';
  scheduled_time: string;
  message?: string;
  is_recurring: boolean;
  recurrence_pattern?: 'daily' | 'weekly' | 'monthly';
  recurrence_interval?: number;
  recurrence_days?: string[];
  end_date?: string;
  status: 'pending' | 'sent' | 'failed';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface ReminderLog {
  id: string;
  reminder_id: string;
  status: 'sent' | 'failed' | 'delivered' | 'read';
  message?: string;
  error_message?: string;
  external_id?: string;
  created_at: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  category: 'streak' | 'consistency' | 'completion' | 'milestone';
  requirement: number;
  points: number;
  created_at: Date;
  updated_at: Date;
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  unlocked_at: Date;
  created_at: Date;
  updated_at: Date;
}

export interface GamificationStats {
  id: string;
  patient_id: string;
  current_streak: number;
  longest_streak: number;
  total_points: number;
  completion_rate: number;
  weekly_progress: number[];
  monthly_progress: number[];
  created_at: Date;
  updated_at: Date;
}

export interface Claim {
  id: string;
  patient_id: string;
  insurance_id: string;
  hospital_id?: string;
  doctor_id?: string;
  claim_number: string;
  submission_date: Date;
  service_date: Date;
  amount: number;
  status: 'pending' | 'approved' | 'rejected' | 'review';
  type: 'medication' | 'procedure' | 'consultation' | 'hospitalization' | 'other';
  description: string;
  documents: string[];
  notes?: string;
  approved_amount?: number;
  approval_date?: Date;
  rejection_reason?: string;
  created_at: Date;
  updated_at: Date;
}

export interface Notification {
  id: string;
  user_id: string;
  title: string;
  message: string;
  type: 'info' | 'success' | 'warning' | 'error';
  read: boolean;
  data?: any;
  created_at: Date;
  updated_at: Date;
}
