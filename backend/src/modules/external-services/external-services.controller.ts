import { Controller, Get, Post, Body, UseGuards, Request } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { ExternalServicesService } from './external-services.service';

export class TestSMSDto {
  phoneNumber: string;
  message: string;
}

export class TestCallDto {
  phoneNumber: string;
  message: string;
}

export class TestElevenLabsDto {
  phoneNumber: string;
  patientName: string;
  medicineName: string;
  dosage: string;
}

@ApiTags('External Services')
@Controller('external-services')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class ExternalServicesController {
  constructor(private readonly externalServicesService: ExternalServicesService) {}

  @Get('status')
  @ApiOperation({ summary: 'Get status of all external services' })
  @ApiResponse({ status: 200, description: 'External services status retrieved successfully' })
  async getServicesStatus() {
    return this.externalServicesService.getServicesStatus();
  }

  @Post('test/sms')
  @ApiOperation({ summary: 'Test SMS functionality' })
  @ApiResponse({ status: 200, description: 'SMS test completed' })
  async testSMS(@Body() testSMSDto: TestSMSDto, @Request() req: any) {
    // Only allow admins to test external services
    if (req.user.role !== 'admin') {
      throw new Error('Only admins can test external services');
    }
    
    return this.externalServicesService.testSMS(testSMSDto.phoneNumber, testSMSDto.message);
  }

  @Post('test/call')
  @ApiOperation({ summary: 'Test voice call functionality' })
  @ApiResponse({ status: 200, description: 'Call test completed' })
  async testCall(@Body() testCallDto: TestCallDto, @Request() req: any) {
    // Only allow admins to test external services
    if (req.user.role !== 'admin') {
      throw new Error('Only admins can test external services');
    }
    
    return this.externalServicesService.testCall(testCallDto.phoneNumber, testCallDto.message);
  }

  @Post('test/elevenlabs')
  @ApiOperation({ summary: 'Test ElevenLabs conversational AI' })
  @ApiResponse({ status: 200, description: 'ElevenLabs test completed' })
  async testElevenLabs(@Body() testElevenLabsDto: TestElevenLabsDto, @Request() req: any) {
    // Only allow admins to test external services
    if (req.user.role !== 'admin') {
      throw new Error('Only admins can test external services');
    }
    
    return this.externalServicesService.testElevenLabsCall(
      testElevenLabsDto.phoneNumber,
      testElevenLabsDto.patientName,
      testElevenLabsDto.medicineName,
      testElevenLabsDto.dosage
    );
  }

  @Get('aws/status')
  @ApiOperation({ summary: 'Get AWS services status' })
  @ApiResponse({ status: 200, description: 'AWS services status retrieved successfully' })
  async getAWSStatus() {
    return this.externalServicesService.getAWSStatus();
  }

  @Post('test/textract')
  @ApiOperation({ summary: 'Test AWS Textract functionality' })
  @ApiResponse({ status: 200, description: 'Textract test completed' })
  async testTextract(@Request() req: any) {
    // Only allow admins to test external services
    if (req.user.role !== 'admin') {
      throw new Error('Only admins can test external services');
    }
    
    return this.externalServicesService.testTextract();
  }
}
