import {
  Controller,
  Get,
  Post,
  Query,
  Param,
  Body,
  UseGuards,
  Request,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { HospitalService } from './hospital.service';
import {
  HospitalPatientsQueryDto,
  HospitalAdherenceReportQueryDto,
  HospitalBulkAdherenceQueryDto,
  HospitalDepartmentAnalysisQueryDto,
  HospitalPatient,
  HospitalAdherenceReport,
  HospitalBulkAdherenceReport,
  HospitalDepartmentAnalysis,
  HospitalDashboardSummary,
} from './dto';

@ApiTags('Hospital')
@Controller('hospital')
@UseGuards(JwtAuthGuard, RolesGuard)
@ApiBearerAuth()
export class HospitalController {
  constructor(private readonly hospitalService: HospitalService) {}

  @Get('patients')
  @Roles('hospital', 'admin')
  @ApiOperation({
    summary: 'Get all patients associated with the hospital',
    description: 'Retrieve a paginated list of patients connected to the hospital through assigned doctors',
  })
  @ApiResponse({
    status: 200,
    description: 'List of hospital patients retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              patient_id: { type: 'string' },
              patient_name: { type: 'string' },
              patient_email: { type: 'string' },
              date_of_birth: { type: 'string', format: 'date' },
              emergency_contact: { type: 'string' },
              assigned_doctor: {
                type: 'object',
                properties: {
                  id: { type: 'string' },
                  name: { type: 'string' },
                  specialization: { type: 'string' },
                },
              },
              current_medications_count: { type: 'number' },
              last_adherence_check: { type: 'string', format: 'date-time' },
            },
          },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
      },
    },
  })
  @ApiResponse({ status: 403, description: 'Access denied - Hospital role required' })
  async getHospitalPatients(
    @Query() query: HospitalPatientsQueryDto,
    @Request() req: any,
  ): Promise<{ data: HospitalPatient[]; total: number; page: number; limit: number }> {
    return this.hospitalService.getHospitalPatients(query, req.user);
  }

  @Get('adherence-report/:patient_id')
  @Roles('hospital', 'admin')
  @ApiOperation({
    summary: 'Get detailed adherence report for a specific patient',
    description: 'Retrieve comprehensive medication adherence analysis for a patient under hospital care',
  })
  @ApiParam({
    name: 'patient_id',
    description: 'ID of the patient to get adherence report for',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Patient adherence report retrieved successfully',
    type: Object,
  })
  @ApiResponse({ status: 403, description: 'Access denied - Hospital does not have access to this patient' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  async getPatientAdherenceReport(
    @Param('patient_id') patientId: string,
    @Query() query: HospitalAdherenceReportQueryDto,
    @Request() req: any,
  ): Promise<HospitalAdherenceReport> {
    return this.hospitalService.getPatientAdherenceReport(patientId, query, req.user);
  }

  @Post('bulk-adherence-report')
  @Roles('hospital', 'admin')
  @ApiOperation({
    summary: 'Get bulk adherence report for multiple patients',
    description: 'Retrieve adherence analysis for multiple patients with summary statistics and department breakdown',
  })
  @ApiResponse({
    status: 200,
    description: 'Bulk adherence report generated successfully',
    type: Object,
  })
  @ApiResponse({ status: 403, description: 'Access denied - Hospital role required' })
  async getBulkAdherenceReport(
    @Body() query: HospitalBulkAdherenceQueryDto,
    @Request() req: any,
  ): Promise<HospitalBulkAdherenceReport> {
    return this.hospitalService.getBulkAdherenceReport(query, req.user);
  }

  @Get('department-analysis')
  @Roles('hospital', 'admin')
  @ApiOperation({
    summary: 'Get department-wise adherence analysis',
    description: 'Analyze medication adherence performance across different hospital departments and specializations',
  })
  @ApiResponse({
    status: 200,
    description: 'Department analysis completed successfully',
    type: Object,
  })
  @ApiResponse({ status: 403, description: 'Access denied - Hospital role required' })
  async getDepartmentAnalysis(
    @Query() query: HospitalDepartmentAnalysisQueryDto,
    @Request() req: any,
  ): Promise<HospitalDepartmentAnalysis> {
    return this.hospitalService.getDepartmentAnalysis(query, req.user);
  }

  @Get('dashboard-summary')
  @Roles('hospital', 'admin')
  @ApiOperation({
    summary: 'Get hospital dashboard summary',
    description: 'Retrieve key metrics and highlights for hospital administration dashboard',
  })
  @ApiResponse({
    status: 200,
    description: 'Dashboard summary retrieved successfully',
    type: Object,
  })
  @ApiResponse({ status: 403, description: 'Access denied - Hospital role required' })
  async getDashboardSummary(@Request() req: any): Promise<HospitalDashboardSummary> {
    return this.hospitalService.getDashboardSummary(req.user);
  }

  @Get('patient/:patient_id/summary')
  @Roles('hospital', 'admin')
  @ApiOperation({
    summary: 'Get quick patient summary',
    description: 'Retrieve essential patient information and recent adherence status for quick reference',
  })
  @ApiParam({
    name: 'patient_id',
    description: 'ID of the patient to get summary for',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Patient summary retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        patient_id: { type: 'string' },
        patient_name: { type: 'string' },
        hospital_id: { type: 'string' },
        assigned_doctor: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            name: { type: 'string' },
            specialization: { type: 'string' },
          },
        },
        current_adherence_rate: { type: 'number' },
        current_streak: { type: 'number' },
        risk_level: { type: 'string', enum: ['low', 'medium', 'high', 'critical'] },
        active_medications: { type: 'number' },
        last_dose_taken: { type: 'string', format: 'date-time' },
        next_scheduled_dose: { type: 'string', format: 'date-time' },
        recent_alerts: {
          type: 'array',
          items: { type: 'string' },
        },
      },
    },
  })
  @ApiResponse({ status: 403, description: 'Access denied - Hospital does not have access to this patient' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  async getPatientSummary(
    @Param('patient_id') patientId: string,
    @Request() req: any,
  ): Promise<any> {
    // Get basic adherence report for quick summary
    const report = await this.hospitalService.getPatientAdherenceReport(
      patientId,
      { days: 7, include_medications: false, include_gamification: false },
      req.user,
    );

    return {
      patient_id: report.patient_id,
      patient_name: report.patient_name,
      hospital_id: report.hospital_id,
      assigned_doctor: report.assigned_doctor,
      current_adherence_rate: report.overall_adherence.adherence_rate,
      current_streak: report.overall_adherence.current_streak,
      risk_level: report.risk_assessment.risk_level,
      active_medications: report.overall_adherence.total_doses > 0 ? 1 : 0, // Simplified
      last_dose_taken: new Date(), // Placeholder
      next_scheduled_dose: new Date(Date.now() + 24 * 60 * 60 * 1000), // Placeholder
      recent_alerts: report.risk_assessment.risk_factors,
    };
  }
}
