import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { MedicinesService } from './medicines.service';
import { CreateMedicineDto, UpdateMedicineDto, MedicineQueryDto } from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../../common/types';

@ApiTags('medicines')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('medicines')
export class MedicinesController {
  constructor(private readonly medicinesService: MedicinesService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new medicine' })
  @ApiResponse({ status: 201, description: 'Medicine created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('doctor', 'hospital', 'admin')
  async create(
    @Body() createMedicineDto: CreateMedicineDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.create(createMedicineDto, currentUser);
  }

  @Get()
  @ApiOperation({ summary: 'Get all medicines' })
  @ApiQuery({ name: 'patient_id', required: false, description: 'Filter by patient ID' })
  @ApiQuery({ name: 'prescription_id', required: false, description: 'Filter by prescription ID' })
  @ApiQuery({ name: 'name', required: false, description: 'Filter by medicine name' })
  @ApiQuery({ name: 'status', required: false, enum: ['active', 'completed', 'all'], description: 'Filter by status' })
  @ApiResponse({ status: 200, description: 'Medicines retrieved successfully' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findAll(
    @Query() query: MedicineQueryDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.findAll(query, currentUser);
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Get medicines for a specific patient' })
  @ApiResponse({ status: 200, description: 'Patient medicines retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findByPatient(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.findByPatient(patientId, currentUser);
  }

  @Get('prescription/:prescriptionId')
  @ApiOperation({ summary: 'Get medicines for a specific prescription' })
  @ApiResponse({ status: 200, description: 'Prescription medicines retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findByPrescription(
    @Param('prescriptionId', ParseUUIDPipe) prescriptionId: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.findByPrescription(prescriptionId, currentUser);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get medicine by ID' })
  @ApiResponse({ status: 200, description: 'Medicine retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.findOne(id, currentUser);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update medicine' })
  @ApiResponse({ status: 200, description: 'Medicine updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('doctor', 'hospital', 'admin')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateMedicineDto: UpdateMedicineDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.medicinesService.update(id, updateMedicineDto, currentUser);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete medicine' })
  @ApiResponse({ status: 200, description: 'Medicine deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Medicine not found' })
  @Roles('doctor', 'hospital', 'admin')
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    await this.medicinesService.remove(id, currentUser);
    return { message: 'Medicine deleted successfully' };
  }
}
