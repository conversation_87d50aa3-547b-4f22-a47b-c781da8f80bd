import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsUUID, IsEnum, IsOptional, IsUrl } from 'class-validator';

export class CreatePrescriptionDto {
  @ApiProperty({ example: 'uuid-of-patient' })
  @IsUUID()
  patient_id: string;

  @ApiPropertyOptional({ example: 'uuid-of-doctor' })
  @IsOptional()
  @IsUUID()
  doctor_id?: string;

  @ApiProperty({ example: 'prescription_scan.pdf' })
  @IsString()
  filename: string;

  @ApiProperty({ example: 'https://example.com/uploads/prescription_scan.pdf' })
  @IsUrl()
  file_url: string;

  @ApiPropertyOptional({ example: 'Extracted text from prescription...' })
  @IsOptional()
  @IsString()
  extracted_text?: string;
}

export class UpdatePrescriptionDto {
  @ApiPropertyOptional({ example: 'uuid-of-doctor' })
  @IsOptional()
  @IsUUID()
  doctor_id?: string;

  @ApiPropertyOptional({ example: 'processing', enum: ['processing', 'completed', 'failed'] })
  @IsOptional()
  @IsEnum(['processing', 'completed', 'failed'])
  status?: 'processing' | 'completed' | 'failed';

  @ApiPropertyOptional({ example: 'Updated extracted text from prescription...' })
  @IsOptional()
  @IsString()
  extracted_text?: string;
}

export class PrescriptionQueryDto {
  @ApiPropertyOptional({ example: 'uuid-of-patient' })
  @IsOptional()
  @IsUUID()
  patient_id?: string;

  @ApiPropertyOptional({ example: 'uuid-of-doctor' })
  @IsOptional()
  @IsUUID()
  doctor_id?: string;

  @ApiPropertyOptional({ example: 'processing', enum: ['processing', 'completed', 'failed', 'all'] })
  @IsOptional()
  @IsString()
  status?: 'processing' | 'completed' | 'failed' | 'all';

  @ApiPropertyOptional({ example: 'prescription' })
  @IsOptional()
  @IsString()
  filename?: string;
}

export class ProcessPrescriptionDto {
  @ApiProperty({ example: 'processing', enum: ['processing', 'completed', 'failed'] })
  @IsEnum(['processing', 'completed', 'failed'])
  status: 'processing' | 'completed' | 'failed';

  @ApiPropertyOptional({ example: 'Extracted text from AI processing...' })
  @IsOptional()
  @IsString()
  extracted_text?: string;
}
