import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { PrescriptionsService } from './prescriptions.service';
import { CreatePrescriptionDto, UpdatePrescriptionDto, PrescriptionQueryDto, ProcessPrescriptionDto } from './dto';
import { JwtAuthGuard } from '../../common/guards/jwt-auth.guard';
import { RolesGuard } from '../../common/guards/roles.guard';
import { Roles } from '../../common/decorators/roles.decorator';
import { CurrentUser } from '../../common/decorators/current-user.decorator';
import { User } from '../../common/types';

@ApiTags('prescriptions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('prescriptions')
export class PrescriptionsController {
  constructor(private readonly prescriptionsService: PrescriptionsService) {}

  @Post()
  @ApiOperation({ summary: 'Create a new prescription' })
  @ApiResponse({ status: 201, description: 'Prescription created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @Roles('patient', 'doctor', 'hospital', 'admin')
  async create(
    @Body() createPrescriptionDto: CreatePrescriptionDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.create(createPrescriptionDto, currentUser);
  }

  @Get()
  @ApiOperation({ summary: 'Get all prescriptions' })
  @ApiQuery({ name: 'patient_id', required: false, description: 'Filter by patient ID' })
  @ApiQuery({ name: 'doctor_id', required: false, description: 'Filter by doctor ID' })
  @ApiQuery({ name: 'status', required: false, enum: ['processing', 'completed', 'failed', 'all'], description: 'Filter by status' })
  @ApiQuery({ name: 'filename', required: false, description: 'Filter by filename' })
  @ApiResponse({ status: 200, description: 'Prescriptions retrieved successfully' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findAll(
    @Query() query: PrescriptionQueryDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.findAll(query, currentUser);
  }

  @Get('patient/:patientId')
  @ApiOperation({ summary: 'Get prescriptions for a specific patient' })
  @ApiResponse({ status: 200, description: 'Patient prescriptions retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Patient not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findByPatient(
    @Param('patientId', ParseUUIDPipe) patientId: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.findByPatient(patientId, currentUser);
  }

  @Get('doctor/:doctorId')
  @ApiOperation({ summary: 'Get prescriptions for a specific doctor' })
  @ApiResponse({ status: 200, description: 'Doctor prescriptions retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Doctor not found' })
  @Roles('doctor', 'hospital', 'admin', 'insurance')
  async findByDoctor(
    @Param('doctorId', ParseUUIDPipe) doctorId: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.findByDoctor(doctorId, currentUser);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get prescription by ID' })
  @ApiResponse({ status: 200, description: 'Prescription retrieved successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('patient', 'doctor', 'hospital', 'admin', 'insurance')
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.findOne(id, currentUser);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Update prescription' })
  @ApiResponse({ status: 200, description: 'Prescription updated successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('doctor', 'hospital', 'admin')
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updatePrescriptionDto: UpdatePrescriptionDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.update(id, updatePrescriptionDto, currentUser);
  }

  @Patch(':id/process')
  @ApiOperation({ summary: 'Process prescription (update status and extracted text)' })
  @ApiResponse({ status: 200, description: 'Prescription processed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('doctor', 'hospital', 'admin')
  async processPrescription(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() processPrescriptionDto: ProcessPrescriptionDto,
    @CurrentUser() currentUser: User,
  ) {
    return this.prescriptionsService.processPrescription(id, processPrescriptionDto, currentUser);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete prescription' })
  @ApiResponse({ status: 200, description: 'Prescription deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden - Only administrators can delete prescriptions' })
  @ApiResponse({ status: 404, description: 'Prescription not found' })
  @Roles('admin')
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentUser() currentUser: User,
  ) {
    await this.prescriptionsService.remove(id, currentUser);
    return { message: 'Prescription deleted successfully' };
  }
}
