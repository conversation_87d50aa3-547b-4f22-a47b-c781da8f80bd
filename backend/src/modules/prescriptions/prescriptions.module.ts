import { Module } from '@nestjs/common';
import { PrescriptionsService } from './prescriptions.service';
import { PrescriptionsController } from './prescriptions.controller';
import { SupabaseService } from '../../config/supabase.service';

@Module({
  controllers: [PrescriptionsController],
  providers: [PrescriptionsService, SupabaseService],
  exports: [PrescriptionsService],
})
export class PrescriptionsModule {}
