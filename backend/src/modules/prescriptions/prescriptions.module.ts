import { Modu<PERSON> } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { PrescriptionsService } from './prescriptions.service';
import { PrescriptionsController } from './prescriptions.controller';
import { SupabaseService } from '../../config/supabase.service';
import { AwsService } from '../../common/services/aws.service';
import { FileUploadService } from '../../common/services/file-upload.service';
import { OpenAiService } from '../../common/services/openai.service';

@Module({
  imports: [
    MulterModule.registerAsync({
      useFactory: () => ({
        storage: require('multer').memoryStorage(),
        limits: {
          fileSize: 10 * 1024 * 1024, // 10MB
        },
      }),
    }),
  ],
  controllers: [PrescriptionsController],
  providers: [PrescriptionsService, SupabaseService, AwsService, FileUploadService, OpenAiService],
  exports: [PrescriptionsService],
})
export class PrescriptionsModule {}
