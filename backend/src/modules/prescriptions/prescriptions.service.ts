import { Injectable, NotFoundException, BadRequestException, ForbiddenException, Logger } from '@nestjs/common';
import { SupabaseService } from '../../config/supabase.service';
import { AwsService } from '../../common/services/aws.service';
import { FileUploadService } from '../../common/services/file-upload.service';
import { User, Prescription } from '../../common/types';
import { CreatePrescriptionDto, UpdatePrescriptionDto, PrescriptionQueryDto, ProcessPrescriptionDto } from './dto';

@Injectable()
export class PrescriptionsService {
  private readonly logger = new Logger(PrescriptionsService.name);

  constructor(
    private readonly supabaseService: SupabaseService,
    private readonly awsService: AwsService,
    private readonly fileUploadService: FileUploadService,
  ) {}

  async create(createPrescriptionDto: CreatePrescriptionDto, currentUser: User): Promise<Prescription> {
    // Patients, doctors, hospitals, and admins can create prescriptions
    if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only patients, doctors, hospitals, and admins can create prescriptions');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Verify patient exists
    const { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('id, assigned_doctor_id')
      .eq('id', createPrescriptionDto.patient_id)
      .single();

    if (patientError || !patient) {
      throw new NotFoundException('Patient not found');
    }

    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== createPrescriptionDto.patient_id) {
      throw new ForbiddenException('You can only create prescriptions for yourself');
    }

    // If doctor_id is provided, verify the doctor exists
    if (createPrescriptionDto.doctor_id) {
      const { data: doctor, error: doctorError } = await supabase
        .from('doctors')
        .select('id')
        .eq('id', createPrescriptionDto.doctor_id)
        .single();

      if (doctorError || !doctor) {
        throw new NotFoundException('Doctor not found');
      }

      // Check if doctor has permission to create prescription for this patient
      if (currentUser.role === 'doctor' && currentUser.id !== createPrescriptionDto.doctor_id) {
        throw new ForbiddenException('You can only create prescriptions as yourself');
      }
    }

    const { data, error } = await supabase
      .from('prescriptions')
      .insert({
        patient_id: createPrescriptionDto.patient_id,
        doctor_id: createPrescriptionDto.doctor_id,
        filename: createPrescriptionDto.filename,
        file_url: createPrescriptionDto.file_url,
        extracted_text: createPrescriptionDto.extracted_text,
        status: 'processing',
      })
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to create prescription: ${error.message}`);
    }

    return data;
  }

  async findAll(query: PrescriptionQueryDto, currentUser: User): Promise<Prescription[]> {
    const supabase = this.supabaseService.getClient();
    
    let dbQuery = supabase
      .from('prescriptions')
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email)),
        doctor:doctors(id, users!doctors_id_fkey(name, email), specialization),
        medicines(id, name, dosage, frequency, duration, start_date, end_date, instructions, side_effects)
      `);

    // Apply role-based filtering
    if (currentUser.role === 'patient') {
      dbQuery = dbQuery.eq('patient_id', currentUser.id);
    } else if (currentUser.role === 'doctor') {
      // Doctors can see prescriptions they created or for their assigned patients
      const { data: patientIds } = await supabase
        .from('patients')
        .select('id')
        .eq('assigned_doctor_id', currentUser.id);

      const ids = patientIds?.map(p => p.id) || [];
      if (ids.length > 0) {
        dbQuery = dbQuery.or(`doctor_id.eq.${currentUser.id},patient_id.in.(${ids.join(',')})`);
      } else {
        // Only show prescriptions created by this doctor
        dbQuery = dbQuery.eq('doctor_id', currentUser.id);
      }
    }

    // Apply query filters
    if (query.patient_id) {
      // Check if user has permission to view this patient's prescriptions
      if (currentUser.role === 'patient' && query.patient_id !== currentUser.id) {
        throw new ForbiddenException('You can only view your own prescriptions');
      }
      dbQuery = dbQuery.eq('patient_id', query.patient_id);
    }

    if (query.doctor_id) {
      dbQuery = dbQuery.eq('doctor_id', query.doctor_id);
    }

    if (query.filename) {
      dbQuery = dbQuery.ilike('filename', `%${query.filename}%`);
    }

    if (query.status && query.status !== 'all') {
      dbQuery = dbQuery.eq('status', query.status);
    }

    const { data, error } = await dbQuery.order('created_at', { ascending: false });

    if (error) {
      throw new BadRequestException(`Failed to fetch prescriptions: ${error.message}`);
    }

    return data || [];
  }

  async findOne(id: string, currentUser: User): Promise<Prescription> {
    const supabase = this.supabaseService.getClient();

    const { data, error } = await supabase
      .from('prescriptions')
      .select(`
        *,
        patient:patients(id, users!patients_id_fkey(name, email), assigned_doctor_id),
        doctor:doctors(id, users!doctors_id_fkey(name, email), specialization),
        medicines(id, name, dosage, frequency, duration, start_date, end_date, instructions, side_effects)
      `)
      .eq('id', id)
      .single();

    if (error || !data) {
      throw new NotFoundException(`Prescription with ID ${id} not found`);
    }

    // Check permissions
    if (currentUser.role === 'patient' && data.patient_id !== currentUser.id) {
      throw new ForbiddenException('You can only view your own prescriptions');
    } else if (currentUser.role === 'doctor') {
      const hasAccess = data.doctor_id === currentUser.id || 
                       data.patient?.assigned_doctor_id === currentUser.id;
      if (!hasAccess) {
        throw new ForbiddenException('You can only view prescriptions for your patients');
      }
    }

    return data;
  }

  async update(id: string, updatePrescriptionDto: UpdatePrescriptionDto, currentUser: User): Promise<Prescription> {
    // Only doctors, hospitals, and admins can update prescriptions
    if (!['doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only doctors, hospitals, and admins can update prescriptions');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Get existing prescription with permission check
    const existingPrescription = await this.findOne(id, currentUser);

    // If doctor_id is being updated, verify the new doctor exists
    if (updatePrescriptionDto.doctor_id) {
      const { data: doctor, error: doctorError } = await supabase
        .from('doctors')
        .select('id')
        .eq('id', updatePrescriptionDto.doctor_id)
        .single();

      if (doctorError || !doctor) {
        throw new NotFoundException('Doctor not found');
      }
    }

    const { data, error } = await supabase
      .from('prescriptions')
      .update({
        ...(updatePrescriptionDto.doctor_id !== undefined && { doctor_id: updatePrescriptionDto.doctor_id }),
        ...(updatePrescriptionDto.status && { status: updatePrescriptionDto.status }),
        ...(updatePrescriptionDto.extracted_text !== undefined && { extracted_text: updatePrescriptionDto.extracted_text }),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to update prescription: ${error.message}`);
    }

    return data;
  }

  async remove(id: string, currentUser: User): Promise<void> {
    // Only admins can delete prescriptions (due to medical record importance)
    if (currentUser.role !== 'admin') {
      throw new ForbiddenException('Only administrators can delete prescriptions');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Get existing prescription with permission check
    await this.findOne(id, currentUser);

    const { error } = await supabase
      .from('prescriptions')
      .delete()
      .eq('id', id);

    if (error) {
      throw new BadRequestException(`Failed to delete prescription: ${error.message}`);
    }
  }

  async processPrescription(id: string, processPrescriptionDto: ProcessPrescriptionDto, currentUser: User): Promise<Prescription> {
    // Only doctors, hospitals, and admins can process prescriptions
    if (!['doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only doctors, hospitals, and admins can process prescriptions');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Get existing prescription with permission check
    await this.findOne(id, currentUser);

    const { data, error } = await supabase
      .from('prescriptions')
      .update({
        status: processPrescriptionDto.status,
        extracted_text: processPrescriptionDto.extracted_text,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      throw new BadRequestException(`Failed to process prescription: ${error.message}`);
    }

    return data;
  }

  async findByPatient(patientId: string, currentUser: User): Promise<Prescription[]> {
    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== patientId) {
      throw new ForbiddenException('You can only view your own prescriptions');
    }

    return this.findAll({ patient_id: patientId }, currentUser);
  }

  async findByDoctor(doctorId: string, currentUser: User): Promise<Prescription[]> {
    // Check permissions
    if (currentUser.role === 'doctor' && currentUser.id !== doctorId) {
      throw new ForbiddenException('You can only view your own prescriptions');
    }

    return this.findAll({ doctor_id: doctorId }, currentUser);
  }

  async uploadAndProcessPrescription(
    file: Express.Multer.File,
    patientId: string,
    doctorId: string,
    currentUser: User,
  ): Promise<Prescription> {
    // Validate file
    this.fileUploadService.validateFile(file);

    // Check permissions (same as create method)
    if (!['patient', 'doctor', 'hospital', 'admin'].includes(currentUser.role)) {
      throw new ForbiddenException('Only patients, doctors, hospitals, and admins can upload prescriptions');
    }

    const supabase = this.supabaseService.getAdminClient();

    // Verify patient exists, or create if current user is a patient
    let { data: patient, error: patientError } = await supabase
      .from('patients')
      .select('id, assigned_doctor_id')
      .eq('id', patientId)
      .single();

    if (patientError || !patient) {
      // If patient not found and current user is a patient with matching ID, create patient record
      if (currentUser.role === 'patient' && currentUser.id === patientId) {
        const { data: newPatient, error: createError } = await supabase
          .from('patients')
          .insert({
            id: patientId,
            date_of_birth: null, // Can be updated later
            emergency_contact: null, // Can be updated later
          })
          .select('id, assigned_doctor_id')
          .single();

        if (createError || !newPatient) {
          this.logger.error('Failed to create patient record:', createError);
          throw new NotFoundException('Failed to create patient record');
        }

        patient = newPatient;
        this.logger.log(`Created patient record for user ${patientId}`);
      } else {
        throw new NotFoundException('Patient not found');
      }
    }

    // Check permissions
    if (currentUser.role === 'patient' && currentUser.id !== patientId) {
      throw new ForbiddenException('You can only upload prescriptions for yourself');
    }

    try {
      // Upload file to S3
      const fileName = this.fileUploadService.generateFileName(file.originalname);
      const fileUrl = await this.awsService.uploadFileToS3(file.buffer, fileName, file.mimetype);

      // Extract text using AWS Textract
      const extractionResult = await this.awsService.extractTextFromPrescription(file.buffer, file.mimetype);

      // Create prescription record
      const { data, error } = await supabase
        .from('prescriptions')
        .insert({
          patient_id: patientId,
          doctor_id: doctorId || null,
          prescription_date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
          filename: fileName,
          file_url: fileUrl,
          extracted_text: extractionResult.extractedText,
          status: 'completed',
          textract_confidence: extractionResult.confidence,
          medicine_info: extractionResult.medicineInfo,
          ai_extracted_medicines: extractionResult.aiExtractedMedicines,
          ai_extraction_success: extractionResult.aiExtractionSuccess,
        })
        .select()
        .single();

      if (error) {
        throw new BadRequestException(`Failed to create prescription: ${error.message}`);
      }

      return data;
    } catch (error) {
      // If AWS processing fails, still create the prescription but mark as failed
      const fileName = this.fileUploadService.generateFileName(file.originalname);

      try {
        const fileUrl = await this.awsService.uploadFileToS3(file.buffer, fileName, file.mimetype);

        const { data, error: dbError } = await supabase
          .from('prescriptions')
          .insert({
            patient_id: patientId,
            doctor_id: doctorId || null,
            prescription_date: new Date().toISOString().split('T')[0], // Current date in YYYY-MM-DD format
            filename: fileName,
            file_url: fileUrl,
            extracted_text: null,
            status: 'failed',
            textract_confidence: 0,
            error_message: error.message,
          })
          .select()
          .single();

        if (dbError) {
          throw new BadRequestException(`Failed to create prescription: ${dbError.message}`);
        }

        return data;
      } catch (uploadError) {
        throw new BadRequestException(`Failed to upload and process prescription: ${uploadError.message}`);
      }
    }
  }
}
