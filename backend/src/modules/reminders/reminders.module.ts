import { <PERSON>du<PERSON> } from '@nestjs/common';
import { RemindersService } from './reminders.service';
import { RemindersController } from './reminders.controller';
import { SupabaseService } from '../../config/supabase.service';
import { TwilioService } from '../../common/services/twilio.service';
import { ElevenLabsService } from '../../common/services/elevenlabs.service';

@Module({
  controllers: [RemindersController],
  providers: [RemindersService, SupabaseService, TwilioService, ElevenLabsService],
  exports: [RemindersService],
})
export class RemindersModule {}
