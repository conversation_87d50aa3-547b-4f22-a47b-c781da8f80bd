const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

async function testPrescriptionAPI() {
  try {
    console.log('🧪 Testing prescription API...\n');

    // First, let's test the prescriptions GET endpoint
    console.log('1. Testing GET /api/v1/prescriptions');
    try {
      const response = await axios.get('http://localhost:3001/api/v1/prescriptions');
      console.log('✅ GET prescriptions successful');
      console.log('📊 Response:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ GET prescriptions failed:', error.response?.data || error.message);
    }

    console.log('\n2. Testing GET /api/v1/prescriptions/patient/[patient-id]');
    // Try with a sample patient ID
    try {
      const response = await axios.get('http://localhost:3001/api/v1/prescriptions/patient/sample-patient-id');
      console.log('✅ GET patient prescriptions successful');
      console.log('📊 Response:', JSON.stringify(response.data, null, 2));
    } catch (error) {
      console.log('❌ GET patient prescriptions failed:', error.response?.data || error.message);
    }

    console.log('\n3. Checking database schema');
    // Let's also check what columns exist in the prescriptions table
    const { createClient } = require('@supabase/supabase-js');
    require('dotenv').config();

    const supabase = createClient(
      process.env.SUPABASE_URL,
      process.env.SUPABASE_ANON_KEY
    );

    // Try to get table schema information
    const { data, error } = await supabase
      .from('prescriptions')
      .select('*')
      .limit(0); // Get no rows, just schema

    if (error) {
      console.log('❌ Schema check failed:', error.message);
    } else {
      console.log('✅ Schema check successful - table exists');
    }

  } catch (err) {
    console.error('❌ Test failed:', err.message);
  }
}

testPrescriptionAPI();
