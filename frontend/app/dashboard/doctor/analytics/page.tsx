'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Adherence<PERSON><PERSON> } from '@/components/charts/adherence-chart';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { ErrorMessage } from '@/components/ui/error-message';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Activity,
  Target,
  Calendar,
  Download,
  Share,
  AlertTriangle,
  CheckCircle,
  Clock,
  Pill,
  Heart,
  Brain,
  Sparkles,
  Zap,
  Star,
  Award
} from 'lucide-react';
import { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, PieChart, Pie, Cell } from 'recharts';
import { useDoctorData } from '@/hooks/use-doctor-data';
import { useDoctorStore } from '@/lib/stores/doctor-store';

export default function AnalyticsPage() {
  // Use doctor data hooks
  const {
    patients,
    isLoading,
    hasError,
    error,
    refreshData,
  } = useDoctorData();

  const {
    analytics,
    isLoadingAnalytics,
    analyticsError,
    fetchAnalytics
  } = useDoctorStore();

  const [timeRange, setTimeRange] = useState('6months');
  const [selectedMetric, setSelectedMetric] = useState('adherence');

  // Load analytics on component mount
  useEffect(() => {
    fetchAnalytics();
  }, [fetchAnalytics]);

  // Process analytics data for charts
  const chartData = useMemo(() => {
    if (!analytics) {
      return {
        adherenceData: [],
        patientOutcomes: [],
        medicationEffectiveness: [],
        riskDistribution: [],
        weeklyTrends: []
      };
    }

    // Transform analytics data for charts
    return {
      adherenceData: analytics.adherenceData || [],
      patientOutcomes: analytics.patientOutcomes || [],
      medicationEffectiveness: analytics.medicationEffectiveness || [],
      riskDistribution: analytics.riskDistribution || [
        { name: 'Low Risk', value: 65, color: '#10B981' },
        { name: 'Medium Risk', value: 25, color: '#F59E0B' },
        { name: 'High Risk', value: 10, color: '#EF4444' },
      ],
      weeklyTrends: analytics.weeklyTrends || []
    };
  }, [analytics]);

  const getMetricColor = (value: number, type: string) => {
    if (type === 'adherence') {
      if (value >= 90) return 'text-green-600';
      if (value >= 75) return 'text-yellow-600';
      return 'text-red-600';
    }
    return 'text-blue-600';
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
  };

  // Show loading state
  if (isLoading || isLoadingAnalytics) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <LoadingSpinner size="lg" text="Loading analytics..." />
        </div>
      </DashboardLayout>
    );
  }

  // Show error state
  if (hasError || analyticsError) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <ErrorMessage
            message={error || analyticsError || 'Failed to load analytics data'}
            onRetry={refreshData}
          />
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/doctor' },
            { label: 'Analytics', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Practice Analytics</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Comprehensive insights into patient outcomes and medication adherence
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Share className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Share Report</span>
              <span className="sm:hidden">Share</span>
            </Button>
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export Data</span>
              <span className="sm:hidden">Export</span>
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Target className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">89%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Avg Adherence</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">+4% vs last month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-blue-600">247</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Patients</p>
                <div className="flex items-center mt-1">
                  <Star className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">12 new this month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Heart className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">76%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Improved Outcomes</p>
                <div className="flex items-center mt-1">
                  <Award className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">Above average</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-orange-600">8</p>
                <p className="text-xs sm:text-sm text-muted-foreground">High Risk Patients</p>
                <div className="flex items-center mt-1">
                  <Zap className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">Needs attention</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Charts */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Adherence Trends */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <span>Adherence Trends by Condition</span>
                  </CardTitle>
                  <Select value={timeRange} onValueChange={setTimeRange}>
                    <SelectTrigger className="w-32 bg-muted/30 border-border/50">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3months">3 Months</SelectItem>
                      <SelectItem value="6months">6 Months</SelectItem>
                      <SelectItem value="1year">1 Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={chartData.adherenceData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                    <XAxis dataKey="month" stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <YAxis domain={[70, 100]} stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      }}
                    />
                    <Line type="monotone" dataKey="overall" stroke="#3B82F6" strokeWidth={3} name="Overall" />
                    <Line type="monotone" dataKey="diabetes" stroke="#10B981" strokeWidth={2} name="Diabetes" />
                    <Line type="monotone" dataKey="hypertension" stroke="#F59E0B" strokeWidth={2} name="Hypertension" />
                    <Line type="monotone" dataKey="asthma" stroke="#8B5CF6" strokeWidth={2} name="Asthma" />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Patient Outcomes */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                    <Heart className="h-5 w-5 text-white" />
                  </div>
                  <span>Patient Outcomes by Condition</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {chartData.patientOutcomes.map((outcome, index) => (
                    <div key={index} className="modern-card p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                      <div className="flex justify-between items-center mb-3">
                        <h3 className="font-semibold text-purple-900 dark:text-purple-100">{outcome.condition}</h3>
                        <div className="flex space-x-2">
                          <Badge variant="outline" className="text-xs bg-green-100 text-green-800 border-green-200">
                            {outcome.improved}% improved
                          </Badge>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <div className="text-center p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                          <div className="text-lg font-bold text-green-600">{outcome.improved}%</div>
                          <div className="text-xs text-green-700 dark:text-green-300">Improved</div>
                        </div>
                        <div className="text-center p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                          <div className="text-lg font-bold text-blue-600">{outcome.stable}%</div>
                          <div className="text-xs text-blue-700 dark:text-blue-300">Stable</div>
                        </div>
                        <div className="text-center p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                          <div className="text-lg font-bold text-red-600">{outcome.declined}%</div>
                          <div className="text-xs text-red-700 dark:text-red-300">Declined</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Medication Effectiveness */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Pill className="h-5 w-5 text-white" />
                  </div>
                  <span>Medication Effectiveness</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {chartData.medicationEffectiveness.map((med, index) => (
                    <div key={index} className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                      <div className="flex justify-between items-center mb-3">
                        <div>
                          <h3 className="font-semibold text-blue-900 dark:text-blue-100">{med.name}</h3>
                          <p className="text-sm text-blue-700 dark:text-blue-300">{med.patients} patients</p>
                        </div>
                        <div className="flex space-x-2">
                          <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800 border-blue-200">
                            {med.adherence}% adherence
                          </Badge>
                          <Badge variant="outline" className="text-xs bg-green-100 text-green-800 border-green-200">
                            {med.effectiveness}% effective
                          </Badge>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-blue-700 dark:text-blue-300">Adherence</span>
                            <span className="font-medium text-blue-900 dark:text-blue-100">{med.adherence}%</span>
                          </div>
                          <Progress value={med.adherence} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-blue-700 dark:text-blue-300">Effectiveness</span>
                            <span className="font-medium text-blue-900 dark:text-blue-100">{med.effectiveness}%</span>
                          </div>
                          <Progress value={med.effectiveness} className="h-2" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Risk Distribution */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-white" />
                  </div>
                  <span>Risk Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={chartData.riskDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {chartData.riskDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="space-y-2 mt-4">
                  {chartData.riskDistribution.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: item.color }}
                        />
                        <span className="text-sm">{item.name}</span>
                      </div>
                      <span className="text-sm font-medium">{item.value}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Weekly Trends */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Calendar className="h-4 w-4 text-white" />
                  </div>
                  <span>Weekly Trends</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <BarChart data={chartData.weeklyTrends}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                    <XAxis dataKey="day" stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px',
                      }}
                    />
                    <Bar dataKey="adherence" fill="#10B981" radius={[2, 2, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Quick Insights */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <Sparkles className="h-5 w-5 text-yellow-500" />
                  <span>Quick Insights</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="modern-card p-3 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-semibold text-sm text-green-900 dark:text-green-100">Best Performing</span>
                  </div>
                  <p className="text-xs text-green-700 dark:text-green-300">
                    Diabetes patients show 94% adherence rate this month
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="font-semibold text-sm text-yellow-900 dark:text-yellow-100">Needs Attention</span>
                  </div>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300">
                    Weekend adherence drops by 12% on average
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <Brain className="h-4 w-4 text-blue-600" />
                    <span className="font-semibold text-sm text-blue-900 dark:text-blue-100">AI Recommendation</span>
                  </div>
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    Consider reminder optimization for evening medications
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}