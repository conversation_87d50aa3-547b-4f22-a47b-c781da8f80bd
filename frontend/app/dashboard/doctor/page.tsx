'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { StatsCard } from '@/components/ui/stats-card';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  Activity, 
  AlertTriangle, 
  TrendingUp, 
  Calendar,
  FileText,
  Clock,
  CheckCircle,
  Phone,
  MessageSquare,
  Plus,
  Eye,
  MoreHorizontal,
  Stethoscope,
  Sparkles,
  Target,
  Zap,
  Heart
} from 'lucide-react';
import Link from 'next/link';

// Mock data for doctor dashboard
const mockPatients = [
  {
    id: '1',
    name: '<PERSON>',
    age: 45,
    condition: 'Diabetes Type 2',
    adherenceRate: 95,
    lastVisit: '2024-01-15',
    nextAppointment: '2024-02-15',
    status: 'excellent',
    avatar: 'SJ',
    riskLevel: 'low'
  },
  {
    id: '2',
    name: 'Michael Chen',
    age: 62,
    condition: 'Hypertension',
    adherenceRate: 78,
    lastVisit: '2024-01-10',
    nextAppointment: '2024-02-10',
    status: 'good',
    avatar: 'MC',
    riskLevel: 'medium'
  },
  {
    id: '3',
    name: 'Emily Rodriguez',
    age: 34,
    condition: 'Asthma',
    adherenceRate: 88,
    lastVisit: '2024-01-12',
    nextAppointment: '2024-02-12',
    status: 'good',
    avatar: 'ER',
    riskLevel: 'low'
  },
  {
    id: '4',
    name: 'Robert Wilson',
    age: 58,
    condition: 'Heart Disease',
    adherenceRate: 65,
    lastVisit: '2024-01-08',
    nextAppointment: '2024-02-08',
    status: 'needs-attention',
    avatar: 'RW',
    riskLevel: 'high'
  }
];

const mockAlerts = [
  {
    id: '1',
    patient: 'Robert Wilson',
    type: 'missed-dose',
    message: 'Missed 3 consecutive doses of Lisinopril',
    time: '2 hours ago',
    severity: 'high'
  },
  {
    id: '2',
    patient: 'Michael Chen',
    type: 'side-effect',
    message: 'Reported dizziness after taking medication',
    time: '4 hours ago',
    severity: 'medium'
  },
  {
    id: '3',
    patient: 'Sarah Johnson',
    type: 'appointment',
    message: 'Requested to reschedule appointment',
    time: '6 hours ago',
    severity: 'low'
  }
];

const upcomingAppointments = [
  {
    id: '1',
    patient: 'Sarah Johnson',
    time: '09:00 AM',
    type: 'Follow-up',
    duration: '30 min'
  },
  {
    id: '2',
    patient: 'Michael Chen',
    time: '10:30 AM',
    type: 'Consultation',
    duration: '45 min'
  },
  {
    id: '3',
    patient: 'Emily Rodriguez',
    time: '02:00 PM',
    type: 'Check-up',
    duration: '30 min'
  }
];

export default function DoctorDashboard() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'good': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      case 'needs-attention': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      case 'medium': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      case 'low': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/doctor', current: true },
          ]}
        />

        {/* Welcome Section */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Stethoscope className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Welcome back, Dr. Wilson! 👨‍⚕️</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                You have {upcomingAppointments.length} appointments today and {mockAlerts.filter(a => a.severity === 'high').length} urgent alerts
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Link href="/dashboard/doctor/patients">
              <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
                <Users className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">View All Patients</span>
                <span className="sm:hidden">Patients</span>
              </Button>
            </Link>
            <Link href="/dashboard/doctor/schedule">
              <Button className="btn-primary w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">New Appointment</span>
                <span className="sm:hidden">New</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">247</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Patients</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">+12 this month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">87%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Avg Adherence</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">+3% this week</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
                <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-red-600">8</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Alerts</p>
                <div className="flex items-center mt-1">
                  <AlertTriangle className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-xs text-red-600 font-medium">2 urgent</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Calendar className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">12</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Today's Appointments</p>
                <div className="flex items-center mt-1">
                  <Clock className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">3 remaining</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Patient Overview */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                      <Users className="h-5 w-5 text-white" />
                    </div>
                    <span>Recent Patients</span>
                  </CardTitle>
                  <Link href="/dashboard/doctor/patients">
                    <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                      View All
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockPatients.map((patient) => (
                    <div key={patient.id} className={`modern-card p-4 bg-gradient-to-r ${getStatusColor(patient.status)} border hover:scale-[1.01] transition-all duration-300`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 min-w-0 flex-1">
                          <Avatar className="h-10 w-10 sm:h-12 sm:w-12 ring-2 ring-white/50 shadow-lg">
                            <AvatarImage src={`/avatars/${patient.avatar}.jpg`} />
                            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                              {patient.avatar}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="font-semibold truncate text-sm sm:text-base">
                                {patient.name}
                              </h3>
                              <Badge variant="outline" className="text-xs modern-badge">
                                {patient.status.replace('-', ' ')}
                              </Badge>
                            </div>
                            <p className="text-xs sm:text-sm opacity-80 truncate">{patient.condition}</p>
                            <div className="flex items-center space-x-4 mt-2">
                              <div className="flex items-center space-x-1">
                                <span className="text-xs opacity-70">Adherence:</span>
                                <span className="text-xs font-medium">{patient.adherenceRate}%</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <span className="text-xs opacity-70">Risk:</span>
                                <span className={`text-xs font-medium ${getRiskColor(patient.riskLevel)}`}>
                                  {patient.riskLevel}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                            <MessageSquare className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Adherence Analytics */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Activity className="h-5 w-5 text-white" />
                  </div>
                  <span>Patient Adherence Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {mockPatients.map((patient) => (
                    <div key={patient.id} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-foreground">{patient.name}</span>
                        <span className="text-sm text-muted-foreground">{patient.adherenceRate}%</span>
                      </div>
                      <Progress 
                        value={patient.adherenceRate} 
                        className={`h-3 ${
                          patient.adherenceRate >= 90 ? 'text-green-600' :
                          patient.adherenceRate >= 75 ? 'text-yellow-600' : 'text-red-600'
                        }`}
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Today's Schedule */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Calendar className="h-4 w-4 text-white" />
                  </div>
                  <span>Today's Schedule</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {upcomingAppointments.map((appointment) => (
                  <div key={appointment.id} className="modern-card p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm truncate text-blue-900 dark:text-blue-100">{appointment.patient}</p>
                        <p className="text-xs text-blue-700 dark:text-blue-300">{appointment.type}</p>
                        <p className="text-xs text-blue-600 dark:text-blue-400 font-medium">{appointment.time}</p>
                      </div>
                      <Badge variant="outline" className="text-xs modern-badge">
                        {appointment.duration}
                      </Badge>
                    </div>
                  </div>
                ))}
                <Link href="/dashboard/doctor/schedule">
                  <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                    <Calendar className="h-4 w-4 mr-2" />
                    View Full Schedule
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Alerts */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-white" />
                  </div>
                  <span>Patient Alerts</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {mockAlerts.map((alert) => (
                  <div key={alert.id} className={`modern-card p-3 bg-gradient-to-r ${getSeverityColor(alert.severity)} border`}>
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">{alert.patient}</p>
                        <p className="text-xs mt-1">{alert.message}</p>
                        <p className="text-xs opacity-70 mt-1">{alert.time}</p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 hover:bg-white/20">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  View All Alerts
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/dashboard/doctor/prescriptions">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <FileText className="h-4 w-4 mr-2" />
                    New Prescription
                  </Button>
                </Link>
                <Link href="/dashboard/doctor/patients">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Users className="h-4 w-4 mr-2" />
                    Patient Records
                  </Button>
                </Link>
                <Link href="/dashboard/doctor/analytics">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Analytics
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}