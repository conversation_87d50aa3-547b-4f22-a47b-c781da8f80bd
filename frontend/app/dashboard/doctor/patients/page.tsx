'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Users, 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  MessageSquare,
  Phone,
  Calendar,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Heart,
  Pill,
  FileText,
  MoreHorizontal,
  Sparkles,
  Target,
  Zap
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format } from 'date-fns';

// Mock patient data
const mockPatients = [
  {
    id: '1',
    name: '<PERSON>',
    age: 45,
    gender: 'Female',
    email: '<EMAIL>',
    phone: '+****************',
    condition: 'Diabetes Type 2',
    adherenceRate: 95,
    lastVisit: new Date('2024-01-15'),
    nextAppointment: new Date('2024-02-15'),
    status: 'excellent',
    avatar: 'SJ',
    riskLevel: 'low',
    medications: ['Metformin', 'Insulin'],
    notes: 'Patient is very compliant with medication schedule.',
    emergencyContact: 'John Johnson - +****************'
  },
  {
    id: '2',
    name: 'Michael Chen',
    age: 62,
    gender: 'Male',
    email: '<EMAIL>',
    phone: '+****************',
    condition: 'Hypertension',
    adherenceRate: 78,
    lastVisit: new Date('2024-01-10'),
    nextAppointment: new Date('2024-02-10'),
    status: 'good',
    avatar: 'MC',
    riskLevel: 'medium',
    medications: ['Lisinopril', 'Amlodipine'],
    notes: 'Occasionally misses evening doses. Needs reminder system.',
    emergencyContact: 'Lisa Chen - +****************'
  },
  {
    id: '3',
    name: 'Emily Rodriguez',
    age: 34,
    gender: 'Female',
    email: '<EMAIL>',
    phone: '+****************',
    condition: 'Asthma',
    adherenceRate: 88,
    lastVisit: new Date('2024-01-12'),
    nextAppointment: new Date('2024-02-12'),
    status: 'good',
    avatar: 'ER',
    riskLevel: 'low',
    medications: ['Albuterol', 'Fluticasone'],
    notes: 'Good adherence overall. Monitor for seasonal triggers.',
    emergencyContact: 'Carlos Rodriguez - +****************'
  },
  {
    id: '4',
    name: 'Robert Wilson',
    age: 58,
    gender: 'Male',
    email: '<EMAIL>',
    phone: '+****************',
    condition: 'Heart Disease',
    adherenceRate: 65,
    lastVisit: new Date('2024-01-08'),
    nextAppointment: new Date('2024-02-08'),
    status: 'needs-attention',
    avatar: 'RW',
    riskLevel: 'high',
    medications: ['Atorvastatin', 'Metoprolol', 'Aspirin'],
    notes: 'Frequent missed doses. Requires close monitoring.',
    emergencyContact: 'Mary Wilson - +****************'
  },
  {
    id: '5',
    name: 'Jennifer Davis',
    age: 29,
    gender: 'Female',
    email: '<EMAIL>',
    phone: '+****************',
    condition: 'Anxiety',
    adherenceRate: 92,
    lastVisit: new Date('2024-01-20'),
    nextAppointment: new Date('2024-02-20'),
    status: 'excellent',
    avatar: 'JD',
    riskLevel: 'low',
    medications: ['Sertraline'],
    notes: 'Excellent compliance. Responding well to treatment.',
    emergencyContact: 'David Davis - +****************'
  },
  {
    id: '6',
    name: 'Thomas Anderson',
    age: 71,
    gender: 'Male',
    email: '<EMAIL>',
    phone: '+****************',
    condition: 'COPD',
    adherenceRate: 73,
    lastVisit: new Date('2024-01-05'),
    nextAppointment: new Date('2024-02-05'),
    status: 'needs-attention',
    avatar: 'TA',
    riskLevel: 'high',
    medications: ['Tiotropium', 'Albuterol', 'Prednisone'],
    notes: 'Struggles with inhaler technique. Needs education.',
    emergencyContact: 'Helen Anderson - +****************'
  }
];

export default function PatientsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [selectedPatient, setSelectedPatient] = useState<typeof mockPatients[0] | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'good': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      case 'needs-attention': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  // Filter and sort patients
  const filteredPatients = mockPatients
    .filter(patient => {
      const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          patient.condition.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (filterStatus === 'excellent') return matchesSearch && patient.status === 'excellent';
      if (filterStatus === 'good') return matchesSearch && patient.status === 'good';
      if (filterStatus === 'needs-attention') return matchesSearch && patient.status === 'needs-attention';
      
      return matchesSearch;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'adherence':
          return b.adherenceRate - a.adherenceRate;
        case 'last-visit':
          return b.lastVisit.getTime() - a.lastVisit.getTime();
        default:
          return 0;
      }
    });

  const getStatusCounts = () => {
    return {
      total: mockPatients.length,
      excellent: mockPatients.filter(p => p.status === 'excellent').length,
      good: mockPatients.filter(p => p.status === 'good').length,
      needsAttention: mockPatients.filter(p => p.status === 'needs-attention').length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/doctor' },
            { label: 'Patients', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Users className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Patient Management</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Monitor and manage your {mockPatients.length} patients
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <FileText className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export Report</span>
              <span className="sm:hidden">Export</span>
            </Button>
            <Button className="btn-primary w-full sm:w-auto">
              <Plus className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Add Patient</span>
              <span className="sm:hidden">Add</span>
            </Button>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Patients</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">Active cases</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.excellent}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Excellent</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">High adherence</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-yellow-600">{statusCounts.good}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Good</p>
                <div className="flex items-center mt-1">
                  <Zap className="h-3 w-3 text-yellow-500 mr-1" />
                  <span className="text-xs text-yellow-600 font-medium">Stable progress</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
                <AlertTriangle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-red-600">{statusCounts.needsAttention}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Needs Attention</p>
                <div className="flex items-center mt-1">
                  <AlertTriangle className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-xs text-red-600 font-medium">Requires follow-up</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search patients by name or condition..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Patients</SelectItem>
                  <SelectItem value="excellent">Excellent</SelectItem>
                  <SelectItem value="good">Good</SelectItem>
                  <SelectItem value="needs-attention">Needs Attention</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="adherence">Adherence Rate</SelectItem>
                  <SelectItem value="last-visit">Last Visit</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Patients List */}
        <div className="grid gap-4 sm:gap-6">
          {filteredPatients.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <Users className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No patients found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterStatus !== 'all' 
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Add your first patient to get started.'
                  }
                </p>
                <Button className="btn-primary">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Patient
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredPatients.map((patient) => (
              <Card key={patient.id} className={`modern-card p-6 bg-gradient-to-r ${getStatusColor(patient.status)} border hover:scale-[1.01] transition-all duration-300`}>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4 min-w-0 flex-1">
                    <Avatar className="h-12 w-12 sm:h-16 sm:w-16 ring-2 ring-white/50 shadow-lg">
                      <AvatarImage src={`/avatars/${patient.avatar}.jpg`} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold text-lg">
                        {patient.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div className="min-w-0 flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-bold text-lg truncate">
                          {patient.name}
                        </h3>
                        <Badge variant="outline" className="text-xs modern-badge">
                          {patient.status.replace('-', ' ')}
                        </Badge>
                      </div>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 text-sm">
                        <div>
                          <span className="opacity-70">Age:</span>
                          <span className="font-medium ml-1">{patient.age}</span>
                        </div>
                        <div>
                          <span className="opacity-70">Condition:</span>
                          <span className="font-medium ml-1">{patient.condition}</span>
                        </div>
                        <div>
                          <span className="opacity-70">Adherence:</span>
                          <span className="font-medium ml-1">{patient.adherenceRate}%</span>
                        </div>
                        <div>
                          <span className="opacity-70">Risk:</span>
                          <span className={`font-medium ml-1 ${getRiskColor(patient.riskLevel)}`}>
                            {patient.riskLevel}
                          </span>
                        </div>
                      </div>
                      <div className="mt-3">
                        <div className="flex justify-between text-xs mb-1">
                          <span className="opacity-70">Adherence Progress</span>
                          <span className="font-medium">{patient.adherenceRate}%</span>
                        </div>
                        <Progress value={patient.adherenceRate} className="h-2" />
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-10 w-10 p-0 hover:bg-white/20"
                          onClick={() => setSelectedPatient(patient)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="modern-card max-w-2xl">
                        <DialogHeader>
                          <DialogTitle className="flex items-center space-x-3">
                            <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                              <AvatarImage src={`/avatars/${patient.avatar}.jpg`} />
                              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                                {patient.avatar}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <span className="text-xl">{patient.name}</span>
                              <p className="text-sm text-muted-foreground font-normal">{patient.condition}</p>
                            </div>
                          </DialogTitle>
                          <DialogDescription>
                            Complete patient information and medical history
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-6">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Contact Info</h4>
                              <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                                <p>📧 {patient.email}</p>
                                <p>📞 {patient.phone}</p>
                                <p>🆘 {patient.emergencyContact}</p>
                              </div>
                            </div>
                            <div className="modern-card p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Medical Info</h4>
                              <div className="space-y-1 text-sm text-green-700 dark:text-green-300">
                                <p>Age: {patient.age} ({patient.gender})</p>
                                <p>Risk Level: <span className={getRiskColor(patient.riskLevel)}>{patient.riskLevel}</span></p>
                                <p>Last Visit: {format(patient.lastVisit, 'MMM d, yyyy')}</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="modern-card p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                            <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Current Medications</h4>
                            <div className="flex flex-wrap gap-2">
                              {patient.medications.map((med, index) => (
                                <Badge key={index} variant="outline" className="modern-badge">
                                  <Pill className="h-3 w-3 mr-1" />
                                  {med}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          
                          <div className="modern-card p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                            <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Clinical Notes</h4>
                            <p className="text-sm text-yellow-700 dark:text-yellow-300">{patient.notes}</p>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}