'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  FileText, 
  Plus, 
  Search, 
  Filter, 
  Eye, 
  Download,
  Edit,
  Trash2,
  Calendar,
  Clock,
  User,
  Pill,
  Send,
  CheckCircle,
  AlertCircle,
  Sparkles,
  Target,
  Activity,
  TrendingUp
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format } from 'date-fns';

// Mock prescription data
const mockPrescriptions = [
  {
    id: '1',
    patientName: '<PERSON>',
    patientId: '1',
    condition: 'Diabetes Type 2',
    medications: [
      { name: 'Metformin', dosage: '850mg', frequency: 'Twice daily', duration: 30 },
      { name: 'Insulin Glargine', dosage: '20 units', frequency: 'Once daily', duration: 30 }
    ],
    createdAt: new Date('2024-01-15'),
    status: 'active',
    notes: 'Monitor blood glucose levels. Follow up in 2 weeks.',
    nextReview: new Date('2024-02-15')
  },
  {
    id: '2',
    patientName: 'Michael Chen',
    patientId: '2',
    condition: 'Hypertension',
    medications: [
      { name: 'Lisinopril', dosage: '10mg', frequency: 'Once daily', duration: 30 },
      { name: 'Amlodipine', dosage: '5mg', frequency: 'Once daily', duration: 30 }
    ],
    createdAt: new Date('2024-01-10'),
    status: 'active',
    notes: 'Check blood pressure weekly. Reduce sodium intake.',
    nextReview: new Date('2024-02-10')
  },
  {
    id: '3',
    patientName: 'Emily Rodriguez',
    patientId: '3',
    condition: 'Asthma',
    medications: [
      { name: 'Albuterol', dosage: '90mcg', frequency: 'As needed', duration: 30 },
      { name: 'Fluticasone', dosage: '110mcg', frequency: 'Twice daily', duration: 30 }
    ],
    createdAt: new Date('2024-01-12'),
    status: 'completed',
    notes: 'Patient responding well. Continue current regimen.',
    nextReview: new Date('2024-02-12')
  },
  {
    id: '4',
    patientName: 'Robert Wilson',
    patientId: '4',
    condition: 'Heart Disease',
    medications: [
      { name: 'Atorvastatin', dosage: '40mg', frequency: 'Once daily', duration: 30 },
      { name: 'Metoprolol', dosage: '50mg', frequency: 'Twice daily', duration: 30 },
      { name: 'Aspirin', dosage: '81mg', frequency: 'Once daily', duration: 30 }
    ],
    createdAt: new Date('2024-01-08'),
    status: 'needs-review',
    notes: 'Patient missing doses frequently. Consider simplifying regimen.',
    nextReview: new Date('2024-02-08')
  }
];

const mockPatients = [
  { id: '1', name: 'Sarah Johnson' },
  { id: '2', name: 'Michael Chen' },
  { id: '3', name: 'Emily Rodriguez' },
  { id: '4', name: 'Robert Wilson' },
  { id: '5', name: 'Jennifer Davis' },
  { id: '6', name: 'Thomas Anderson' }
];

export default function PrescriptionsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [isNewPrescriptionOpen, setIsNewPrescriptionOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState('');
  const [medications, setMedications] = useState([
    { name: '', dosage: '', frequency: '', duration: '' }
  ]);
  const [notes, setNotes] = useState('');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'completed': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      case 'needs-review': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'completed': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'needs-review': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Filter prescriptions
  const filteredPrescriptions = mockPrescriptions
    .filter(prescription => {
      const matchesSearch = prescription.patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          prescription.condition.toLowerCase().includes(searchTerm.toLowerCase());
      
      if (filterStatus === 'active') return matchesSearch && prescription.status === 'active';
      if (filterStatus === 'completed') return matchesSearch && prescription.status === 'completed';
      if (filterStatus === 'needs-review') return matchesSearch && prescription.status === 'needs-review';
      
      return matchesSearch;
    });

  const addMedication = () => {
    setMedications([...medications, { name: '', dosage: '', frequency: '', duration: '' }]);
  };

  const removeMedication = (index: number) => {
    setMedications(medications.filter((_, i) => i !== index));
  };

  const updateMedication = (index: number, field: string, value: string) => {
    const updated = medications.map((med, i) => 
      i === index ? { ...med, [field]: value } : med
    );
    setMedications(updated);
  };

  const handleCreatePrescription = () => {
    // Handle prescription creation logic here
    setIsNewPrescriptionOpen(false);
    setSelectedPatient('');
    setMedications([{ name: '', dosage: '', frequency: '', duration: '' }]);
    setNotes('');
  };

  const getStatusCounts = () => {
    return {
      total: mockPrescriptions.length,
      active: mockPrescriptions.filter(p => p.status === 'active').length,
      completed: mockPrescriptions.filter(p => p.status === 'completed').length,
      needsReview: mockPrescriptions.filter(p => p.status === 'needs-review').length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/doctor' },
            { label: 'Prescriptions', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Prescription Management</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Create and manage patient prescriptions
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export All</span>
              <span className="sm:hidden">Export</span>
            </Button>
            <Dialog open={isNewPrescriptionOpen} onOpenChange={setIsNewPrescriptionOpen}>
              <DialogTrigger asChild>
                <Button className="btn-primary w-full sm:w-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">New Prescription</span>
                  <span className="sm:hidden">New</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="modern-card max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                      <FileText className="h-5 w-5 text-white" />
                    </div>
                    <span>Create New Prescription</span>
                  </DialogTitle>
                  <DialogDescription>
                    Fill out the prescription details for your patient
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-6">
                  {/* Patient Selection */}
                  <div className="space-y-2">
                    <Label htmlFor="patient">Select Patient</Label>
                    <Select value={selectedPatient} onValueChange={setSelectedPatient}>
                      <SelectTrigger className="bg-muted/30 border-border/50">
                        <SelectValue placeholder="Choose a patient" />
                      </SelectTrigger>
                      <SelectContent>
                        {mockPatients.map((patient) => (
                          <SelectItem key={patient.id} value={patient.id}>
                            {patient.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Medications */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <Label>Medications</Label>
                      <Button type="button" variant="outline" size="sm" onClick={addMedication}>
                        <Plus className="h-4 w-4 mr-2" />
                        Add Medication
                      </Button>
                    </div>
                    {medications.map((medication, index) => (
                      <div key={index} className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                          <div className="space-y-2">
                            <Label>Medication Name</Label>
                            <Input
                              placeholder="e.g., Metformin"
                              value={medication.name}
                              onChange={(e) => updateMedication(index, 'name', e.target.value)}
                              className="bg-white/50 dark:bg-black/20"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Dosage</Label>
                            <Input
                              placeholder="e.g., 850mg"
                              value={medication.dosage}
                              onChange={(e) => updateMedication(index, 'dosage', e.target.value)}
                              className="bg-white/50 dark:bg-black/20"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Frequency</Label>
                            <Input
                              placeholder="e.g., Twice daily"
                              value={medication.frequency}
                              onChange={(e) => updateMedication(index, 'frequency', e.target.value)}
                              className="bg-white/50 dark:bg-black/20"
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Duration (days)</Label>
                            <div className="flex space-x-2">
                              <Input
                                placeholder="30"
                                value={medication.duration}
                                onChange={(e) => updateMedication(index, 'duration', e.target.value)}
                                className="bg-white/50 dark:bg-black/20"
                              />
                              {medications.length > 1 && (
                                <Button
                                  type="button"
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeMedication(index)}
                                  className="px-3"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Notes */}
                  <div className="space-y-2">
                    <Label htmlFor="notes">Clinical Notes</Label>
                    <Textarea
                      id="notes"
                      placeholder="Add any special instructions or notes..."
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      className="bg-muted/30 border-border/50 min-h-[100px]"
                    />
                  </div>

                  {/* Actions */}
                  <div className="flex justify-end space-x-3">
                    <Button variant="outline" onClick={() => setIsNewPrescriptionOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreatePrescription} className="btn-primary">
                      <Send className="h-4 w-4 mr-2" />
                      Create Prescription
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Prescriptions</p>
                <div className="flex items-center mt-1">
                  <Activity className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">All time</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.active}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">Currently prescribed</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{statusCounts.completed}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Completed</p>
                <div className="flex items-center mt-1">
                  <CheckCircle className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">Successfully finished</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <AlertCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-yellow-600">{statusCounts.needsReview}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Needs Review</p>
                <div className="flex items-center mt-1">
                  <AlertCircle className="h-3 w-3 text-yellow-500 mr-1" />
                  <span className="text-xs text-yellow-600 font-medium">Requires attention</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search prescriptions by patient or condition..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Prescriptions</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="needs-review">Needs Review</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Prescriptions List */}
        <div className="grid gap-4 sm:gap-6">
          {filteredPrescriptions.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <FileText className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No prescriptions found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterStatus !== 'all' 
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Create your first prescription to get started.'
                  }
                </p>
                <Button className="btn-primary" onClick={() => setIsNewPrescriptionOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Prescription
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredPrescriptions.map((prescription) => (
              <Card key={prescription.id} className={`modern-card p-6 bg-gradient-to-r ${getStatusColor(prescription.status)} border hover:scale-[1.01] transition-all duration-300`}>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                      <User className="h-6 w-6" />
                    </div>
                    <div>
                      <h3 className="font-bold text-lg">{prescription.patientName}</h3>
                      <p className="opacity-80">{prescription.condition}</p>
                      <p className="text-sm opacity-70">Created {format(prescription.createdAt, 'MMM d, yyyy')}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge variant="outline" className={`modern-badge ${getStatusBadgeColor(prescription.status)}`}>
                      {prescription.status.replace('-', ' ')}
                    </Badge>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                      <Edit className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2 flex items-center">
                      <Pill className="h-4 w-4 mr-2" />
                      Medications ({prescription.medications.length})
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {prescription.medications.map((med, index) => (
                        <div key={index} className="modern-card p-3 bg-white/30 dark:bg-black/20 backdrop-blur-sm">
                          <div className="font-medium">{med.name}</div>
                          <div className="text-sm opacity-80">
                            {med.dosage} • {med.frequency} • {med.duration} days
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {prescription.notes && (
                    <div className="modern-card p-3 bg-white/20 dark:bg-black/10 backdrop-blur-sm">
                      <h4 className="font-semibold mb-1 text-sm">Clinical Notes:</h4>
                      <p className="text-sm opacity-90">{prescription.notes}</p>
                    </div>
                  )}

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1 opacity-70" />
                        <span className="opacity-80">Next Review: {format(prescription.nextReview, 'MMM d, yyyy')}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="hover:bg-white/20">
                        <Send className="h-4 w-4 mr-1" />
                        Send to Patient
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}