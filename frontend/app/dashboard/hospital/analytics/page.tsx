'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Adherence<PERSON><PERSON> } from '@/components/charts/adherence-chart';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown,
  Users, 
  Activity, 
  Target,
  Calendar,
  Download,
  Share,
  AlertTriangle,
  CheckCircle,
  Clock,
  Pill,
  Heart,
  Brain,
  Sparkles,
  Zap,
  Star,
  Award,
  Building2,
  Stethoscope,
  DollarSign
} from 'lucide-react';
import { useState } from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, <PERSON>lt<PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, AreaChart, Area } from 'recharts';

// Mock analytics data
const departmentPerformance = [
  { department: 'Cardiology', patients: 156, adherence: 94, revenue: 245000, doctors: 12, satisfaction: 4.8 },
  { department: 'Endocrinology', patients: 203, adherence: 91, revenue: 189000, doctors: 8, satisfaction: 4.7 },
  { department: 'Pulmonology', patients: 134, adherence: 89, revenue: 167000, doctors: 6, satisfaction: 4.6 },
  { department: 'Neurology', patients: 98, adherence: 85, revenue: 198000, doctors: 10, satisfaction: 4.5 },
  { department: 'Pediatrics', patients: 187, adherence: 96, revenue: 156000, doctors: 9, satisfaction: 4.9 },
  { department: 'Orthopedics', patients: 145, adherence: 88, revenue: 234000, doctors: 7, satisfaction: 4.4 }
];

const monthlyTrends = [
  { month: 'Jan', patients: 678, adherence: 87, revenue: 1200000, appointments: 1245 },
  { month: 'Feb', patients: 712, adherence: 89, revenue: 1350000, appointments: 1356 },
  { month: 'Mar', patients: 745, adherence: 91, revenue: 1420000, appointments: 1423 },
  { month: 'Apr', patients: 789, adherence: 88, revenue: 1380000, appointments: 1398 },
  { month: 'May', patients: 823, adherence: 92, revenue: 1560000, appointments: 1567 },
  { month: 'Jun', patients: 856, adherence: 94, revenue: 1680000, appointments: 1634 },
];

const patientOutcomes = [
  { condition: 'Diabetes', improved: 78, stable: 18, declined: 4, total: 245 },
  { condition: 'Hypertension', improved: 72, stable: 22, declined: 6, total: 198 },
  { condition: 'Asthma', improved: 85, stable: 12, declined: 3, total: 156 },
  { condition: 'Heart Disease', improved: 68, stable: 25, declined: 7, total: 134 },
  { condition: 'COPD', improved: 65, stable: 28, declined: 7, total: 89 },
];

const riskDistribution = [
  { name: 'Low Risk', value: 65, color: '#10B981', patients: 556 },
  { name: 'Medium Risk', value: 25, color: '#F59E0B', patients: 214 },
  { name: 'High Risk', value: 10, color: '#EF4444', patients: 86 },
];

const topMedications = [
  { name: 'Metformin', patients: 234, adherence: 94, effectiveness: 92 },
  { name: 'Lisinopril', patients: 198, adherence: 88, effectiveness: 89 },
  { name: 'Atorvastatin', patients: 167, adherence: 82, effectiveness: 87 },
  { name: 'Albuterol', patients: 145, adherence: 91, effectiveness: 95 },
  { name: 'Omeprazole', patients: 123, adherence: 96, effectiveness: 94 },
];

const doctorPerformance = [
  { name: 'Dr. Sarah Wilson', patients: 156, adherence: 94, rating: 4.9, department: 'Cardiology' },
  { name: 'Dr. Michael Chen', patients: 203, adherence: 91, rating: 4.8, department: 'Endocrinology' },
  { name: 'Dr. Emily Rodriguez', patients: 134, adherence: 89, rating: 4.7, department: 'Pulmonology' },
  { name: 'Dr. Robert Johnson', patients: 98, adherence: 85, rating: 4.6, department: 'Neurology' },
  { name: 'Dr. Amanda Foster', patients: 187, adherence: 96, rating: 4.9, department: 'Pediatrics' },
];

export default function AnalyticsPage() {
  const [timeRange, setTimeRange] = useState('6months');
  const [selectedMetric, setSelectedMetric] = useState('adherence');

  const getMetricColor = (value: number, type: string) => {
    if (type === 'adherence') {
      if (value >= 90) return 'text-green-600';
      if (value >= 75) return 'text-yellow-600';
      return 'text-red-600';
    }
    return 'text-blue-600';
  };

  const getTrendIcon = (current: number, previous: number) => {
    if (current > previous) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (current < previous) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <div className="h-4 w-4 bg-gray-400 rounded-full" />;
  };

  const totalPatients = departmentPerformance.reduce((sum, dept) => sum + dept.patients, 0);
  const avgAdherence = Math.round(departmentPerformance.reduce((sum, dept) => sum + dept.adherence, 0) / departmentPerformance.length);
  const totalRevenue = departmentPerformance.reduce((sum, dept) => sum + dept.revenue, 0);
  const totalDoctors = departmentPerformance.reduce((sum, dept) => sum + dept.doctors, 0);

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/hospital' },
            { label: 'Analytics', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <BarChart3 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Hospital Analytics</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Comprehensive insights across all departments and services
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Share className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Share Report</span>
              <span className="sm:hidden">Share</span>
            </Button>
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export Data</span>
              <span className="sm:hidden">Export</span>
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{totalPatients.toLocaleString()}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Patients</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">+8.2% this month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Target className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{avgAdherence}%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Avg Adherence</p>
                <div className="flex items-center mt-1">
                  <Award className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">+1.5% this week</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-orange-600">${(totalRevenue / 1000000).toFixed(1)}M</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Revenue</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">+12.3% vs last month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Stethoscope className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{totalDoctors}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Doctors</p>
                <div className="flex items-center mt-1">
                  <Star className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">4.7 avg rating</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Charts */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Monthly Trends */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                      <TrendingUp className="h-5 w-5 text-white" />
                    </div>
                    <span>Monthly Performance Trends</span>
                  </CardTitle>
                  <Select value={timeRange} onValueChange={setTimeRange}>
                    <SelectTrigger className="w-32 bg-muted/30 border-border/50">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="3months">3 Months</SelectItem>
                      <SelectItem value="6months">6 Months</SelectItem>
                      <SelectItem value="1year">1 Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={monthlyTrends}>
                    <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                    <XAxis dataKey="month" stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <YAxis stroke="hsl(var(--muted-foreground))" fontSize={12} />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: 'hsl(var(--card))',
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px',
                        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
                      }}
                    />
                    <Area type="monotone" dataKey="adherence" stackId="1" stroke="#3B82F6" fill="#3B82F6" fillOpacity={0.6} />
                    <Area type="monotone" dataKey="patients" stackId="2" stroke="#10B981" fill="#10B981" fillOpacity={0.6} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Department Performance */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Building2 className="h-5 w-5 text-white" />
                  </div>
                  <span>Department Performance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {departmentPerformance.map((dept, index) => (
                    <div key={index} className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                      <div className="flex justify-between items-center mb-3">
                        <div>
                          <h3 className="font-semibold text-blue-900 dark:text-blue-100">{dept.department}</h3>
                          <p className="text-sm text-blue-700 dark:text-blue-300">
                            {dept.patients} patients • {dept.doctors} doctors
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Badge variant="outline" className="text-xs modern-badge bg-green-100 text-green-800 border-green-200">
                            {dept.adherence}% adherence
                          </Badge>
                          <Badge variant="outline" className="text-xs modern-badge bg-yellow-100 text-yellow-800 border-yellow-200">
                            {dept.satisfaction}⭐
                          </Badge>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-blue-700 dark:text-blue-300">Adherence</span>
                            <span className="font-medium text-blue-900 dark:text-blue-100">{dept.adherence}%</span>
                          </div>
                          <Progress value={dept.adherence} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-blue-700 dark:text-blue-300">Revenue</span>
                            <span className="font-medium text-blue-900 dark:text-blue-100">${(dept.revenue / 1000).toFixed(0)}K</span>
                          </div>
                          <Progress value={(dept.revenue / 300000) * 100} className="h-2" />
                        </div>
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-blue-700 dark:text-blue-300">Satisfaction</span>
                            <span className="font-medium text-blue-900 dark:text-blue-100">{dept.satisfaction}/5</span>
                          </div>
                          <Progress value={(dept.satisfaction / 5) * 100} className="h-2" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Patient Outcomes */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                    <Heart className="h-5 w-5 text-white" />
                  </div>
                  <span>Patient Outcomes by Condition</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {patientOutcomes.map((outcome, index) => (
                    <div key={index} className="modern-card p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                      <div className="flex justify-between items-center mb-3">
                        <div>
                          <h3 className="font-semibold text-purple-900 dark:text-purple-100">{outcome.condition}</h3>
                          <p className="text-sm text-purple-700 dark:text-purple-300">{outcome.total} total patients</p>
                        </div>
                        <Badge variant="outline" className="text-xs modern-badge bg-green-100 text-green-800 border-green-200">
                          {outcome.improved}% improved
                        </Badge>
                      </div>
                      <div className="grid grid-cols-3 gap-2">
                        <div className="text-center p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                          <div className="text-lg font-bold text-green-600">{outcome.improved}%</div>
                          <div className="text-xs text-green-700 dark:text-green-300">Improved</div>
                        </div>
                        <div className="text-center p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                          <div className="text-lg font-bold text-blue-600">{outcome.stable}%</div>
                          <div className="text-xs text-blue-700 dark:text-blue-300">Stable</div>
                        </div>
                        <div className="text-center p-2 bg-red-100 dark:bg-red-900/30 rounded-lg">
                          <div className="text-lg font-bold text-red-600">{outcome.declined}%</div>
                          <div className="text-xs text-red-700 dark:text-red-300">Declined</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Risk Distribution */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-white" />
                  </div>
                  <span>Risk Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <PieChart>
                    <Pie
                      data={riskDistribution}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {riskDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
                <div className="space-y-2 mt-4">
                  {riskDistribution.map((item, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div 
                          className="w-3 h-3 rounded-full" 
                          style={{ backgroundColor: item.color }}
                        />
                        <span className="text-sm">{item.name}</span>
                      </div>
                      <div className="text-right">
                        <span className="text-sm font-medium">{item.patients}</span>
                        <span className="text-xs text-muted-foreground ml-1">({item.value}%)</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Medications */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Pill className="h-4 w-4 text-white" />
                  </div>
                  <span>Top Medications</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {topMedications.map((med, index) => (
                  <div key={index} className="modern-card p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="font-semibold text-sm text-blue-900 dark:text-blue-100">{med.name}</h4>
                      <Badge variant="outline" className="text-xs modern-badge">
                        {med.patients} patients
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-blue-700 dark:text-blue-300">Adherence: </span>
                        <span className="font-medium text-blue-900 dark:text-blue-100">{med.adherence}%</span>
                      </div>
                      <div>
                        <span className="text-blue-700 dark:text-blue-300">Effectiveness: </span>
                        <span className="font-medium text-blue-900 dark:text-blue-100">{med.effectiveness}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Top Doctors */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Award className="h-4 w-4 text-white" />
                  </div>
                  <span>Top Performers</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {doctorPerformance.slice(0, 3).map((doctor, index) => (
                  <div key={index} className="modern-card p-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center text-white text-xs font-bold">
                        {index + 1}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-sm text-green-900 dark:text-green-100">{doctor.name}</h4>
                        <p className="text-xs text-green-700 dark:text-green-300">{doctor.department}</p>
                      </div>
                      <Badge variant="outline" className="text-xs modern-badge">
                        {doctor.rating}⭐
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs">
                      <div>
                        <span className="text-green-700 dark:text-green-300">Patients: </span>
                        <span className="font-medium text-green-900 dark:text-green-100">{doctor.patients}</span>
                      </div>
                      <div>
                        <span className="text-green-700 dark:text-green-300">Adherence: </span>
                        <span className="font-medium text-green-900 dark:text-green-100">{doctor.adherence}%</span>
                      </div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Quick Insights */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <Sparkles className="h-5 w-5 text-yellow-500" />
                  <span>Quick Insights</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="modern-card p-3 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-semibold text-sm text-green-900 dark:text-green-100">Best Department</span>
                  </div>
                  <p className="text-xs text-green-700 dark:text-green-300">
                    Pediatrics leads with 96% adherence rate
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <span className="font-semibold text-sm text-yellow-900 dark:text-yellow-100">Needs Attention</span>
                  </div>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300">
                    Neurology department requires adherence improvement
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <Brain className="h-4 w-4 text-blue-600" />
                    <span className="font-semibold text-sm text-blue-900 dark:text-blue-100">AI Recommendation</span>
                  </div>
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    Consider expanding Pediatrics capacity by 15%
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}