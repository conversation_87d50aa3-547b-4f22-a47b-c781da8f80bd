'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { 
  Stethoscope, 
  Search, 
  Filter, 
  Plus, 
  Eye, 
  MessageSquare,
  Phone,
  Calendar,
  Activity,
  Users,
  Star,
  Award,
  TrendingUp,
  MoreHorizontal,
  Edit,
  Trash2,
  CheckCircle,
  Clock,
  Target,
  Sparkles,
  Zap,
  Heart,
  Brain
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { format } from 'date-fns';

// Mock doctor data
const mockDoctors = [
  {
    id: '1',
    name: 'Dr. Sarah Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    specialization: 'Cardiology',
    licenseNumber: 'MD-12345',
    experience: 15,
    patients: 156,
    adherenceRate: 94,
    rating: 4.9,
    reviews: 234,
    avatar: 'SW',
    status: 'active',
    joinDate: new Date('2020-03-15'),
    schedule: 'Full-time',
    department: 'Cardiology',
    qualifications: ['MD - Harvard Medical School', 'Fellowship - Mayo Clinic'],
    languages: ['English', 'Spanish'],
    consultationFee: 250
  },
  {
    id: '2',
    name: 'Dr. Michael Chen',
    email: '<EMAIL>',
    phone: '+****************',
    specialization: 'Endocrinology',
    licenseNumber: 'MD-23456',
    experience: 12,
    patients: 203,
    adherenceRate: 91,
    rating: 4.8,
    reviews: 189,
    avatar: 'MC',
    status: 'active',
    joinDate: new Date('2021-06-10'),
    schedule: 'Full-time',
    department: 'Endocrinology',
    qualifications: ['MD - Johns Hopkins', 'Residency - Cleveland Clinic'],
    languages: ['English', 'Mandarin'],
    consultationFee: 220
  },
  {
    id: '3',
    name: 'Dr. Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    specialization: 'Pulmonology',
    licenseNumber: 'MD-34567',
    experience: 8,
    patients: 134,
    adherenceRate: 89,
    rating: 4.7,
    reviews: 156,
    avatar: 'ER',
    status: 'active',
    joinDate: new Date('2022-01-20'),
    schedule: 'Part-time',
    department: 'Pulmonology',
    qualifications: ['MD - Stanford University', 'Fellowship - UCSF'],
    languages: ['English', 'Spanish', 'Portuguese'],
    consultationFee: 200
  },
  {
    id: '4',
    name: 'Dr. Robert Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    specialization: 'Neurology',
    licenseNumber: 'MD-45678',
    experience: 20,
    patients: 98,
    adherenceRate: 85,
    rating: 4.6,
    reviews: 298,
    avatar: 'RJ',
    status: 'on-leave',
    joinDate: new Date('2018-09-05'),
    schedule: 'Full-time',
    department: 'Neurology',
    qualifications: ['MD - Yale University', 'Fellowship - Mass General'],
    languages: ['English'],
    consultationFee: 300
  },
  {
    id: '5',
    name: 'Dr. Amanda Foster',
    email: '<EMAIL>',
    phone: '+****************',
    specialization: 'Pediatrics',
    licenseNumber: 'MD-56789',
    experience: 6,
    patients: 187,
    adherenceRate: 96,
    rating: 4.9,
    reviews: 145,
    avatar: 'AF',
    status: 'active',
    joinDate: new Date('2023-02-14'),
    schedule: 'Full-time',
    department: 'Pediatrics',
    qualifications: ['MD - University of Pennsylvania', 'Residency - Children\'s Hospital'],
    languages: ['English', 'French'],
    consultationFee: 180
  }
];

const departments = [
  'All Departments',
  'Cardiology',
  'Endocrinology', 
  'Pulmonology',
  'Neurology',
  'Pediatrics',
  'Orthopedics',
  'Dermatology'
];

export default function DoctorsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterDepartment, setFilterDepartment] = useState('All Departments');
  const [filterStatus, setFilterStatus] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [isNewDoctorOpen, setIsNewDoctorOpen] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<typeof mockDoctors[0] | null>(null);

  // Form state for new doctor
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    specialization: '',
    licenseNumber: '',
    department: '',
    schedule: 'Full-time',
    consultationFee: '',
    qualifications: '',
    languages: ''
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'on-leave': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      case 'inactive': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'on-leave': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'inactive': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Filter and sort doctors
  const filteredDoctors = mockDoctors
    .filter(doctor => {
      const matchesSearch = doctor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          doctor.specialization.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesDepartment = filterDepartment === 'All Departments' || doctor.department === filterDepartment;
      const matchesStatus = filterStatus === 'all' || doctor.status === filterStatus;
      
      return matchesSearch && matchesDepartment && matchesStatus;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'experience':
          return b.experience - a.experience;
        case 'patients':
          return b.patients - a.patients;
        case 'rating':
          return b.rating - a.rating;
        default:
          return 0;
      }
    });

  const handleCreateDoctor = () => {
    // Handle doctor creation logic here
    setIsNewDoctorOpen(false);
    setFormData({
      name: '',
      email: '',
      phone: '',
      specialization: '',
      licenseNumber: '',
      department: '',
      schedule: 'Full-time',
      consultationFee: '',
      qualifications: '',
      languages: ''
    });
  };

  const getStatusCounts = () => {
    return {
      total: mockDoctors.length,
      active: mockDoctors.filter(d => d.status === 'active').length,
      onLeave: mockDoctors.filter(d => d.status === 'on-leave').length,
      inactive: mockDoctors.filter(d => d.status === 'inactive').length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/hospital' },
            { label: 'Doctors', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Stethoscope className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Doctor Management</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Manage your medical staff and their performance
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Activity className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Performance Report</span>
              <span className="sm:hidden">Report</span>
            </Button>
            <Dialog open={isNewDoctorOpen} onOpenChange={setIsNewDoctorOpen}>
              <DialogTrigger asChild>
                <Button className="btn-primary w-full sm:w-auto">
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Add Doctor</span>
                  <span className="sm:hidden">Add</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="modern-card max-w-2xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle className="flex items-center space-x-2">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
                      <Stethoscope className="h-5 w-5 text-white" />
                    </div>
                    <span>Add New Doctor</span>
                  </DialogTitle>
                  <DialogDescription>
                    Add a new doctor to your medical staff
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        placeholder="Dr. John Smith"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Phone</Label>
                      <Input
                        id="phone"
                        placeholder="+****************"
                        value={formData.phone}
                        onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="licenseNumber">License Number</Label>
                      <Input
                        id="licenseNumber"
                        placeholder="MD-12345"
                        value={formData.licenseNumber}
                        onChange={(e) => setFormData(prev => ({ ...prev, licenseNumber: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="specialization">Specialization</Label>
                      <Input
                        id="specialization"
                        placeholder="Cardiology"
                        value={formData.specialization}
                        onChange={(e) => setFormData(prev => ({ ...prev, specialization: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="department">Department</Label>
                      <Select value={formData.department} onValueChange={(value) => setFormData(prev => ({ ...prev, department: value }))}>
                        <SelectTrigger className="bg-muted/30 border-border/50">
                          <SelectValue placeholder="Select department" />
                        </SelectTrigger>
                        <SelectContent>
                          {departments.slice(1).map((dept) => (
                            <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="schedule">Schedule</Label>
                      <Select value={formData.schedule} onValueChange={(value) => setFormData(prev => ({ ...prev, schedule: value }))}>
                        <SelectTrigger className="bg-muted/30 border-border/50">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Full-time">Full-time</SelectItem>
                          <SelectItem value="Part-time">Part-time</SelectItem>
                          <SelectItem value="Contract">Contract</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="consultationFee">Consultation Fee ($)</Label>
                      <Input
                        id="consultationFee"
                        type="number"
                        placeholder="250"
                        value={formData.consultationFee}
                        onChange={(e) => setFormData(prev => ({ ...prev, consultationFee: e.target.value }))}
                        className="bg-muted/30 border-border/50"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="qualifications">Qualifications</Label>
                    <Textarea
                      id="qualifications"
                      placeholder="MD - Harvard Medical School, Fellowship - Mayo Clinic"
                      value={formData.qualifications}
                      onChange={(e) => setFormData(prev => ({ ...prev, qualifications: e.target.value }))}
                      className="bg-muted/30 border-border/50 min-h-[80px]"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="languages">Languages</Label>
                    <Input
                      id="languages"
                      placeholder="English, Spanish, French"
                      value={formData.languages}
                      onChange={(e) => setFormData(prev => ({ ...prev, languages: e.target.value }))}
                      className="bg-muted/30 border-border/50"
                    />
                  </div>

                  <div className="flex justify-end space-x-3">
                    <Button variant="outline" onClick={() => setIsNewDoctorOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleCreateDoctor} className="btn-primary">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Doctor
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Stethoscope className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{statusCounts.total}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Doctors</p>
                <div className="flex items-center mt-1">
                  <Users className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">Medical staff</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{statusCounts.active}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">Currently working</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-yellow-600">{statusCounts.onLeave}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">On Leave</p>
                <div className="flex items-center mt-1">
                  <Calendar className="h-3 w-3 text-yellow-500 mr-1" />
                  <span className="text-xs text-yellow-600 font-medium">Temporary absence</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Star className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">4.8</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Avg Rating</p>
                <div className="flex items-center mt-1">
                  <Award className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">Patient satisfaction</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search doctors by name or specialization..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Department */}
              <Select value={filterDepartment} onValueChange={setFilterDepartment}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by department" />
                </SelectTrigger>
                <SelectContent>
                  {departments.map((dept) => (
                    <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="on-leave">On Leave</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="experience">Experience</SelectItem>
                  <SelectItem value="patients">Patient Count</SelectItem>
                  <SelectItem value="rating">Rating</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Doctors List */}
        <div className="grid gap-4 sm:gap-6">
          {filteredDoctors.length === 0 ? (
            <Card className="modern-card">
              <CardContent className="text-center py-12">
                <div className="relative">
                  <Stethoscope className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                  <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No doctors found</h3>
                <p className="text-muted-foreground mb-6">
                  {searchTerm || filterDepartment !== 'All Departments' || filterStatus !== 'all'
                    ? 'Try adjusting your search or filter criteria.'
                    : 'Add your first doctor to get started.'
                  }
                </p>
                <Button className="btn-primary" onClick={() => setIsNewDoctorOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Doctor
                </Button>
              </CardContent>
            </Card>
          ) : (
            filteredDoctors.map((doctor) => (
              <Card key={doctor.id} className={`modern-card p-6 bg-gradient-to-r ${getStatusColor(doctor.status)} border hover:scale-[1.01] transition-all duration-300`}>
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <Avatar className="h-16 w-16 ring-2 ring-white/50 shadow-lg">
                      <AvatarImage src={`/avatars/${doctor.avatar}.jpg`} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold text-lg">
                        {doctor.avatar}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-bold text-xl">{doctor.name}</h3>
                        <Badge variant="outline" className={`modern-badge ${getStatusBadgeColor(doctor.status)}`}>
                          {doctor.status.replace('-', ' ')}
                        </Badge>
                      </div>
                      <p className="opacity-80 font-medium">{doctor.specialization}</p>
                      <p className="text-sm opacity-70">{doctor.department} • {doctor.schedule}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <Phone className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <MessageSquare className="h-4 w-4" />
                    </Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="h-10 w-10 p-0 hover:bg-white/20"
                          onClick={() => setSelectedDoctor(doctor)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="modern-card max-w-3xl">
                        <DialogHeader>
                          <DialogTitle className="flex items-center space-x-3">
                            <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                              <AvatarImage src={`/avatars/${doctor.avatar}.jpg`} />
                              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                                {doctor.avatar}
                              </AvatarFallback>
                            </Avatar>
                            <div>
                              <span className="text-xl">{doctor.name}</span>
                              <p className="text-sm text-muted-foreground font-normal">{doctor.specialization}</p>
                            </div>
                          </DialogTitle>
                          <DialogDescription>
                            Complete doctor profile and performance metrics
                          </DialogDescription>
                        </DialogHeader>
                        <div className="space-y-6">
                          <div className="grid grid-cols-2 gap-4">
                            <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Contact Information</h4>
                              <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                                <p>📧 {doctor.email}</p>
                                <p>📞 {doctor.phone}</p>
                                <p>🆔 {doctor.licenseNumber}</p>
                              </div>
                            </div>
                            <div className="modern-card p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                              <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Performance</h4>
                              <div className="space-y-1 text-sm text-green-700 dark:text-green-300">
                                <p>👥 {doctor.patients} patients</p>
                                <p>📊 {doctor.adherenceRate}% adherence</p>
                                <p>⭐ {doctor.rating}/5 ({doctor.reviews} reviews)</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="modern-card p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                            <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Professional Details</h4>
                            <div className="grid grid-cols-2 gap-4 text-sm text-purple-700 dark:text-purple-300">
                              <div>
                                <p><strong>Experience:</strong> {doctor.experience} years</p>
                                <p><strong>Joined:</strong> {format(doctor.joinDate, 'MMM yyyy')}</p>
                                <p><strong>Fee:</strong> ${doctor.consultationFee}</p>
                              </div>
                              <div>
                                <p><strong>Languages:</strong> {doctor.languages.join(', ')}</p>
                                <p><strong>Schedule:</strong> {doctor.schedule}</p>
                              </div>
                            </div>
                          </div>
                          
                          <div className="modern-card p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                            <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Qualifications</h4>
                            <div className="space-y-1">
                              {doctor.qualifications.map((qual, index) => (
                                <Badge key={index} variant="outline" className="mr-2 mb-1 modern-badge">
                                  {qual}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                    <Button variant="ghost" size="sm" className="h-10 w-10 p-0 hover:bg-white/20">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-4">
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold">{doctor.patients}</div>
                    <div className="text-xs opacity-70">Patients</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold text-green-600">{doctor.adherenceRate}%</div>
                    <div className="text-xs opacity-70">Adherence</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold text-yellow-600">{doctor.rating}⭐</div>
                    <div className="text-xs opacity-70">Rating</div>
                  </div>
                  <div className="text-center p-3 bg-white/30 dark:bg-black/20 rounded-lg">
                    <div className="text-lg font-bold text-blue-600">{doctor.experience}y</div>
                    <div className="text-xs opacity-70">Experience</div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2 text-sm opacity-80">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {format(doctor.joinDate, 'MMM yyyy')}</span>
                  </div>
                  <div className="flex space-x-2">
                    <Button variant="outline" size="sm" className="hover:bg-white/20">
                      <Calendar className="h-4 w-4 mr-1" />
                      Schedule
                    </Button>
                    <Button variant="outline" size="sm" className="hover:bg-white/20">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  </div>
                </div>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}