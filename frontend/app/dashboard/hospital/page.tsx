'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { StatsCard } from '@/components/ui/stats-card';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Building2, 
  Users, 
  Activity, 
  TrendingUp, 
  Stethoscope,
  AlertTriangle,
  DollarSign,
  Calendar,
  FileText,
  Plus,
  Eye,
  MoreHorizontal,
  MapPin,
  Phone,
  Mail,
  Sparkles,
  Target,
  Zap,
  Heart
} from 'lucide-react';
import Link from 'next/link';

// Mock data for hospital dashboard
const mockDepartments = [
  {
    id: '1',
    name: 'Cardiology',
    doctors: 12,
    patients: 156,
    adherenceRate: 92,
    revenue: 245000,
    status: 'excellent'
  },
  {
    id: '2',
    name: 'Endocrinology',
    doctors: 8,
    patients: 203,
    adherenceRate: 88,
    revenue: 189000,
    status: 'good'
  },
  {
    id: '3',
    name: 'Pulmonology',
    doctors: 6,
    patients: 134,
    adherenceRate: 85,
    revenue: 167000,
    status: 'good'
  },
  {
    id: '4',
    name: 'Neurology',
    doctors: 10,
    patients: 98,
    adherenceRate: 78,
    revenue: 198000,
    status: 'needs-attention'
  }
];

const mockDoctors = [
  {
    id: '1',
    name: 'Dr. Sarah Wilson',
    specialization: 'Cardiology',
    patients: 45,
    adherenceRate: 94,
    rating: 4.9,
    avatar: 'SW',
    status: 'active'
  },
  {
    id: '2',
    name: 'Dr. Michael Chen',
    specialization: 'Endocrinology',
    patients: 52,
    adherenceRate: 91,
    rating: 4.8,
    avatar: 'MC',
    status: 'active'
  },
  {
    id: '3',
    name: 'Dr. Emily Rodriguez',
    specialization: 'Pulmonology',
    patients: 38,
    adherenceRate: 89,
    rating: 4.7,
    avatar: 'ER',
    status: 'active'
  },
  {
    id: '4',
    name: 'Dr. Robert Johnson',
    specialization: 'Neurology',
    patients: 41,
    adherenceRate: 85,
    rating: 4.6,
    avatar: 'RJ',
    status: 'busy'
  }
];

const mockAlerts = [
  {
    id: '1',
    type: 'system',
    title: 'Low Adherence Alert',
    message: 'Neurology department adherence dropped below 80%',
    severity: 'high',
    time: '1 hour ago'
  },
  {
    id: '2',
    type: 'financial',
    title: 'Revenue Target',
    message: 'Q1 revenue target achieved 2 weeks early',
    severity: 'low',
    time: '3 hours ago'
  },
  {
    id: '3',
    type: 'operational',
    title: 'Staff Schedule',
    message: 'Dr. Wilson requested schedule change for next week',
    severity: 'medium',
    time: '5 hours ago'
  }
];

const recentActivities = [
  {
    id: '1',
    action: 'New doctor onboarded',
    details: 'Dr. Amanda Foster joined Cardiology',
    time: '2 hours ago',
    type: 'staff'
  },
  {
    id: '2',
    action: 'Department performance review',
    details: 'Endocrinology Q1 review completed',
    time: '4 hours ago',
    type: 'review'
  },
  {
    id: '3',
    action: 'System maintenance',
    details: 'Scheduled maintenance completed successfully',
    time: '6 hours ago',
    type: 'system'
  }
];

export default function HospitalDashboard() {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'good': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      case 'needs-attention': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      case 'medium': return 'from-yellow-50 to-orange-50 border-yellow-200 text-yellow-900 dark:from-yellow-900/20 dark:to-orange-900/20 dark:border-yellow-800 dark:text-yellow-100';
      case 'low': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const totalPatients = mockDepartments.reduce((sum, dept) => sum + dept.patients, 0);
  const totalDoctors = mockDepartments.reduce((sum, dept) => sum + dept.doctors, 0);
  const avgAdherence = Math.round(mockDepartments.reduce((sum, dept) => sum + dept.adherenceRate, 0) / mockDepartments.length);
  const totalRevenue = mockDepartments.reduce((sum, dept) => sum + dept.revenue, 0);

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/hospital', current: true },
          ]}
        />

        {/* Welcome Section */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Building2 className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">City General Hospital 🏥</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Managing {totalDoctors} doctors and {totalPatients} patients across {mockDepartments.length} departments
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Link href="/dashboard/hospital/reports">
              <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
                <FileText className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Generate Report</span>
                <span className="sm:hidden">Report</span>
              </Button>
            </Link>
            <Link href="/dashboard/hospital/doctors">
              <Button className="btn-primary w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Add Doctor</span>
                <span className="sm:hidden">Add</span>
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{totalPatients.toLocaleString()}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Patients</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">+8.2% this month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Stethoscope className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{totalDoctors}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Active Doctors</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">+2 this week</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl shadow-lg group-hover:shadow-purple-500/25 transition-all duration-300">
                <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-purple-600">{avgAdherence}%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Avg Adherence</p>
                <div className="flex items-center mt-1">
                  <Zap className="h-3 w-3 text-purple-500 mr-1" />
                  <span className="text-xs text-purple-600 font-medium">+1.5% this week</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-lg group-hover:shadow-orange-500/25 transition-all duration-300">
                <DollarSign className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-orange-600">${(totalRevenue / 1000).toFixed(0)}K</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Monthly Revenue</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-orange-500 mr-1" />
                  <span className="text-xs text-orange-600 font-medium">+12.3% vs last month</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Department Overview */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                      <Building2 className="h-5 w-5 text-white" />
                    </div>
                    <span>Department Performance</span>
                  </CardTitle>
                  <Link href="/dashboard/hospital/analytics">
                    <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                      View Analytics
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockDepartments.map((department) => (
                    <div key={department.id} className={`modern-card p-4 bg-gradient-to-r ${getStatusColor(department.status)} border hover:scale-[1.01] transition-all duration-300`}>
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                            <Building2 className="h-5 w-5" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-sm sm:text-base">
                              {department.name}
                            </h3>
                            <p className="text-xs sm:text-sm opacity-80">
                              {department.doctors} doctors • {department.patients} patients
                            </p>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs modern-badge">
                          {department.status.replace('-', ' ')}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                        <div>
                          <p className="text-xs opacity-70">Adherence Rate</p>
                          <div className="flex items-center space-x-2 mt-1">
                            <Progress value={department.adherenceRate} className="h-2 flex-1" />
                            <span className="text-sm font-medium">{department.adherenceRate}%</span>
                          </div>
                        </div>
                        <div>
                          <p className="text-xs opacity-70">Monthly Revenue</p>
                          <p className="text-sm font-semibold mt-1">
                            ${(department.revenue / 1000).toFixed(0)}K
                          </p>
                        </div>
                        <div className="hidden sm:block">
                          <p className="text-xs opacity-70">Efficiency</p>
                          <p className="text-sm font-semibold mt-1">
                            {Math.round(department.patients / department.doctors)} pts/doc
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Top Doctors */}
            <Card className="modern-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                    <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                      <Stethoscope className="h-5 w-5 text-white" />
                    </div>
                    <span>Top Performing Doctors</span>
                  </CardTitle>
                  <Link href="/dashboard/hospital/doctors">
                    <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                      View All
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {mockDoctors.map((doctor) => (
                    <div key={doctor.id} className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800 hover:scale-[1.01] transition-all duration-300">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 min-w-0 flex-1">
                          <Avatar className="h-10 w-10 sm:h-12 sm:w-12 ring-2 ring-blue-200 dark:ring-blue-800 shadow-lg">
                            <AvatarImage src={`/avatars/${doctor.avatar}.jpg`} />
                            <AvatarFallback className="bg-gradient-to-br from-green-500 to-emerald-500 text-white font-semibold">
                              {doctor.avatar}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center space-x-2 mb-1">
                              <h3 className="font-semibold text-blue-900 dark:text-blue-100 truncate text-sm sm:text-base">
                                {doctor.name}
                              </h3>
                              <Badge variant={doctor.status === 'active' ? 'default' : 'secondary'} className="text-xs modern-badge">
                                {doctor.status}
                              </Badge>
                            </div>
                            <p className="text-xs sm:text-sm text-blue-700 dark:text-blue-300">{doctor.specialization}</p>
                            <div className="flex items-center space-x-4 mt-2">
                              <div className="flex items-center space-x-1">
                                <span className="text-xs text-blue-600 dark:text-blue-400">Patients:</span>
                                <span className="text-xs font-medium text-blue-900 dark:text-blue-100">{doctor.patients}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <span className="text-xs text-blue-600 dark:text-blue-400">Adherence:</span>
                                <span className="text-xs font-medium text-green-600">{doctor.adherenceRate}%</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <span className="text-xs text-blue-600 dark:text-blue-400">Rating:</span>
                                <span className="text-xs font-medium text-blue-900 dark:text-blue-100">{doctor.rating}⭐</span>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-800">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-blue-100 dark:hover:bg-blue-800">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* System Alerts */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg">
                    <AlertTriangle className="h-4 w-4 text-white" />
                  </div>
                  <span>System Alerts</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {mockAlerts.map((alert) => (
                  <div key={alert.id} className={`modern-card p-3 bg-gradient-to-r ${getSeverityColor(alert.severity)} border`}>
                    <div className="flex items-start justify-between">
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm">{alert.title}</p>
                        <p className="text-xs mt-1">{alert.message}</p>
                        <p className="text-xs opacity-70 mt-1">{alert.time}</p>
                      </div>
                      <Button variant="ghost" size="sm" className="h-6 w-6 p-0 ml-2 hover:bg-white/20">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  View All Alerts
                </Button>
              </CardContent>
            </Card>

            {/* Recent Activities */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Activity className="h-4 w-4 text-white" />
                  </div>
                  <span>Recent Activities</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="modern-card p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-start space-x-3">
                      <div className="p-1.5 bg-blue-100 dark:bg-blue-800 rounded-full mt-0.5">
                        <div className="h-2 w-2 bg-blue-600 dark:bg-blue-300 rounded-full" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <p className="font-semibold text-sm text-blue-900 dark:text-blue-100">{activity.action}</p>
                        <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">{activity.details}</p>
                        <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">{activity.time}</p>
                      </div>
                    </div>
                  </div>
                ))}
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <Activity className="h-4 w-4 mr-2" />
                  View All Activities
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Link href="/dashboard/hospital/doctors">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Stethoscope className="h-4 w-4 mr-2" />
                    Manage Doctors
                  </Button>
                </Link>
                <Link href="/dashboard/hospital/patients">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <Users className="h-4 w-4 mr-2" />
                    Patient Records
                  </Button>
                </Link>
                <Link href="/dashboard/hospital/analytics">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <TrendingUp className="h-4 w-4 mr-2" />
                    View Analytics
                  </Button>
                </Link>
                <Link href="/dashboard/hospital/reports">
                  <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                    <FileText className="h-4 w-4 mr-2" />
                    Generate Reports
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}