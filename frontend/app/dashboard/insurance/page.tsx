'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Landmark,
  Users,
  Activity,
  TrendingUp,
  Search,
  Filter,
  Eye,
  MoreHorizontal,
  Building2,
  CheckCircle,
  Clock,
  Target,
  Sparkles,
  Stethoscope,
  Pill,
  Heart,
  FileText,
  Download,
  Calendar,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { useState, useMemo } from 'react';
import { format } from 'date-fns';
import { useInsuranceData } from '@/hooks/use-insurance-data';

// Insurance Dashboard Component



export default function InsuranceDashboard() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [selectedPatient, setSelectedPatient] = useState<any>(null);

  // Use insurance data hooks
  const {
    dashboardSummary,
    policyHolders,
    hospitalPerformance,
    isLoading,
    hasError,
    error,
    refreshAll,
  } = useInsuranceData({
    autoFetchDashboard: true,
    autoFetchPolicyHolders: true,
    autoFetchHospitalPerformance: true,
    policyHoldersQuery: { limit: 50 }, // Get more for dashboard display
  });

  // Calculate statistics from real data
  const totalPatients = dashboardSummary?.total_policy_holders || 0;
  const avgAdherence = dashboardSummary?.average_adherence_rate || 0;
  const highRiskPatients = dashboardSummary?.high_risk_patients || 0;
  const lowAdherencePatients = dashboardSummary?.low_adherence_patients || 0;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'from-green-50 to-emerald-50 border-green-200 text-green-900 dark:from-green-900/20 dark:to-emerald-900/20 dark:border-green-800 dark:text-green-100';
      case 'good': return 'from-blue-50 to-indigo-50 border-blue-200 text-blue-900 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800 dark:text-blue-100';
      case 'needs-attention': return 'from-red-50 to-pink-50 border-red-200 text-red-900 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800 dark:text-red-100';
      default: return 'from-gray-50 to-slate-50 border-gray-200 text-gray-900 dark:from-gray-900/20 dark:to-slate-900/20 dark:border-gray-800 dark:text-gray-100';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      case 'critical': return 'text-red-800';
      default: return 'text-gray-600';
    }
  };

  // Transform policy holder data to match UI expectations
  const transformedPolicyHolders = useMemo(() => {
    if (!policyHolders || policyHolders.length === 0) return [];

    return policyHolders.map(holder => {
      // Calculate adherence rate from API data (placeholder logic)
      const adherenceRate = Math.floor(Math.random() * 40) + 60; // 60-100% range
      const riskLevel = adherenceRate >= 90 ? 'low' : adherenceRate >= 80 ? 'medium' : 'high';
      const status = adherenceRate >= 90 ? 'excellent' : adherenceRate >= 80 ? 'good' : 'needs-attention';

      return {
        id: holder.patient_id,
        name: holder.patient_name,
        email: holder.patient_email,
        age: holder.date_of_birth ? new Date().getFullYear() - new Date(holder.date_of_birth).getFullYear() : 45,
        gender: Math.random() > 0.5 ? 'Male' : 'Female',
        condition: 'Various Conditions', // This would come from medical records
        adherenceRate,
        lastVisit: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        nextAppointment: new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000),
        status,
        avatar: holder.patient_name.split(' ').map(n => n[0]).join('').toUpperCase(),
        riskLevel,
        doctor: holder.assigned_doctor?.name || 'Dr. Unknown',
        hospital: 'Various Hospitals', // This would come from hospital data
        medications: [`Medicine ${Math.floor(Math.random() * 3) + 1}`],
        insuranceId: `INS-${holder.patient_id.slice(-6)}`,
        policyType: holder.policy_type,
        policyExpiry: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        activeMedicationsCount: holder.active_medications_count,
        enrollmentDate: holder.enrollment_date,
        lastAdherenceUpdate: holder.last_adherence_update,
      };
    });
  }, [policyHolders]);

  // Filter patients
  const filteredPatients = transformedPolicyHolders
    .filter(patient => {
      const matchesSearch = patient.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          patient.condition.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          patient.doctor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          patient.hospital.toLowerCase().includes(searchTerm.toLowerCase());

      if (filterStatus === 'excellent') return matchesSearch && patient.status === 'excellent';
      if (filterStatus === 'good') return matchesSearch && patient.status === 'good';
      if (filterStatus === 'needs-attention') return matchesSearch && patient.status === 'needs-attention';

      return matchesSearch;
    });

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/insurance', current: true },
          ]}
        />

        {/* Welcome Section */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Landmark className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">HealthGuard Insurance 🏛️</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                {isLoading ? (
                  'Loading patient data...'
                ) : (
                  `Monitoring ${totalPatients} patients across ${hospitalPerformance?.length || 0} healthcare facilities`
                )}
              </p>
            </div>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" className="w-full sm:w-auto hover:bg-accent/50 interactive-element">
              <Download className="h-4 w-4 mr-2" />
              <span className="hidden sm:inline">Export Report</span>
              <span className="sm:hidden">Export</span>
            </Button>
          </div>
        </div>

        {/* Error State */}
        {hasError && (
          <div className="modern-card p-6 border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-red-100 dark:bg-red-900/50 rounded-lg">
                <AlertCircle className="h-5 w-5 text-red-600" />
              </div>
              <div>
                <h3 className="font-semibold text-red-900 dark:text-red-100">Error Loading Data</h3>
                <p className="text-sm text-red-700 dark:text-red-200 mt-1">{error}</p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={refreshAll}
                  className="mt-2 border-red-300 text-red-700 hover:bg-red-100 dark:border-red-700 dark:text-red-200 dark:hover:bg-red-900/50"
                >
                  Try Again
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-6">
          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
                <Users className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-foreground">{totalPatients}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Patients</p>
                <div className="flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 text-blue-500 mr-1" />
                  <span className="text-xs text-blue-600 font-medium">Active members</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl shadow-lg group-hover:shadow-green-500/25 transition-all duration-300">
                <Activity className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-green-600">{avgAdherence}%</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Avg Adherence</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-green-500 mr-1" />
                  <span className="text-xs text-green-600 font-medium">+2% this month</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl shadow-lg group-hover:shadow-red-500/25 transition-all duration-300">
                <Heart className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-red-600">{highRiskPatients}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">High Risk Patients</p>
                <div className="flex items-center mt-1">
                  <Clock className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-xs text-red-600 font-medium">Needs monitoring</span>
                </div>
              </div>
            </div>
          </div>

          <div className="modern-card p-4 sm:p-6 group hover:scale-105 transition-all duration-300">
            <div className="flex items-center space-x-3">
              <div className="p-3 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl shadow-lg group-hover:shadow-yellow-500/25 transition-all duration-300">
                <Pill className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
              </div>
              <div className="min-w-0 flex-1">
                <p className="text-2xl sm:text-3xl font-bold text-yellow-600">{lowAdherencePatients}</p>
                <p className="text-xs sm:text-sm text-muted-foreground">Low Adherence</p>
                <div className="flex items-center mt-1">
                  <Target className="h-3 w-3 text-yellow-500 mr-1" />
                  <span className="text-xs text-yellow-600 font-medium">Below 80%</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Search */}
        <Card className="modern-card">
          <CardContent className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search patients by name, condition, doctor or hospital..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 bg-muted/30 border-border/50 focus:bg-background transition-colors"
                  />
                </div>
              </div>
              
              {/* Filter by Status */}
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-full sm:w-48 bg-muted/30 border-border/50">
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Patients</SelectItem>
                  <SelectItem value="excellent">Excellent</SelectItem>
                  <SelectItem value="good">Good</SelectItem>
                  <SelectItem value="needs-attention">Needs Attention</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Content */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Patient List */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Users className="h-5 w-5 text-white" />
                  </div>
                  <span>Patient Adherence</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {filteredPatients.length === 0 ? (
                    <div className="text-center py-12">
                      <div className="relative">
                        <Users className="mx-auto h-16 w-16 text-muted-foreground/30 mb-4" />
                        <div className="absolute top-0 left-1/2 -translate-x-1/2 h-16 w-16 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl" />
                      </div>
                      <h3 className="text-lg font-semibold text-foreground mb-2">No patients found</h3>
                      <p className="text-muted-foreground mb-6">
                        {searchTerm || filterStatus !== 'all'
                          ? 'Try adjusting your search or filter criteria.'
                          : 'No patients have been enrolled yet.'
                        }
                      </p>
                    </div>
                  ) : (
                    filteredPatients.map((patient) => (
                      <div key={patient.id} className={`modern-card p-4 bg-gradient-to-r ${getStatusColor(patient.status)} border hover:scale-[1.01] transition-all duration-300`}>
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <Avatar className="h-10 w-10 sm:h-12 sm:w-12 ring-2 ring-white/50 shadow-lg">
                              <AvatarImage src={`/avatars/${patient.avatar}.jpg`} />
                              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                                {patient.avatar}
                              </AvatarFallback>
                            </Avatar>
                            <div className="min-w-0 flex-1">
                              <div className="flex items-center space-x-2 mb-1">
                                <h3 className="font-semibold truncate text-sm sm:text-base">
                                  {patient.name}
                                </h3>
                                <Badge variant="outline" className="text-xs modern-badge">
                                  {patient.status.replace('-', ' ')}
                                </Badge>
                              </div>
                              <p className="text-xs sm:text-sm opacity-80 truncate">{patient.condition}</p>
                              <div className="flex items-center space-x-4 mt-2">
                                <div className="flex items-center space-x-1">
                                  <span className="text-xs opacity-70">Adherence:</span>
                                  <span className="text-xs font-medium">{patient.adherenceRate}%</span>
                                </div>
                                <div className="flex items-center space-x-1">
                                  <span className="text-xs opacity-70">Risk:</span>
                                  <span className={`text-xs font-medium ${getRiskColor(patient.riskLevel)}`}>
                                    {patient.riskLevel}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2 ml-4">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="sm" 
                                  className="h-8 w-8 p-0 hover:bg-white/20"
                                  onClick={() => setSelectedPatient(patient)}
                                >
                                  <Eye className="h-4 w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="modern-card max-w-3xl">
                                <DialogHeader>
                                  <DialogTitle className="flex items-center space-x-3">
                                    <Avatar className="h-12 w-12 ring-2 ring-primary/20">
                                      <AvatarImage src={`/avatars/${patient.avatar}.jpg`} />
                                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 text-white font-semibold">
                                        {patient.avatar}
                                      </AvatarFallback>
                                    </Avatar>
                                    <div>
                                      <span className="text-xl">{patient.name}</span>
                                      <p className="text-sm text-muted-foreground font-normal">{patient.condition}</p>
                                    </div>
                                  </DialogTitle>
                                  <DialogDescription>
                                    Complete patient information and adherence data
                                  </DialogDescription>
                                </DialogHeader>
                                <div className="space-y-6">
                                  <div className="grid grid-cols-2 gap-4">
                                    <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                                      <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Personal Information</h4>
                                      <div className="space-y-1 text-sm text-blue-700 dark:text-blue-300">
                                        <p><strong>Age:</strong> {patient.age} years ({patient.gender})</p>
                                        <p><strong>Insurance ID:</strong> {patient.insuranceId}</p>
                                        <p><strong>Policy Type:</strong> {patient.policyType}</p>
                                        <p><strong>Policy Expiry:</strong> {format(patient.policyExpiry, 'MMM d, yyyy')}</p>
                                      </div>
                                    </div>
                                    <div className="modern-card p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                                      <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Healthcare Providers</h4>
                                      <div className="space-y-1 text-sm text-green-700 dark:text-green-300">
                                        <p><strong>Doctor:</strong> {patient.doctor}</p>
                                        <p><strong>Hospital:</strong> {patient.hospital}</p>
                                        <p><strong>Last Visit:</strong> {format(patient.lastVisit, 'MMM d, yyyy')}</p>
                                        <p><strong>Next Appointment:</strong> {format(patient.nextAppointment, 'MMM d, yyyy')}</p>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div className="modern-card p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                                    <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Current Medications</h4>
                                    <div className="flex flex-wrap gap-2">
                                      {patient.medications.map((med, index) => (
                                        <Badge key={index} variant="outline" className="modern-badge">
                                          <Pill className="h-3 w-3 mr-1" />
                                          {med}
                                        </Badge>
                                      ))}
                                    </div>
                                  </div>
                                  
                                  <div className="modern-card p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                                    <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Adherence Statistics</h4>
                                    <div className="space-y-3">
                                      <div>
                                        <div className="flex justify-between text-sm mb-1">
                                          <span className="text-yellow-700 dark:text-yellow-300">Current Adherence</span>
                                          <span className="font-medium text-yellow-900 dark:text-yellow-100">{patient.adherenceRate}%</span>
                                        </div>
                                        <Progress value={patient.adherenceRate} className="h-2" />
                                      </div>
                                      <div className="grid grid-cols-2 gap-4 mt-2">
                                        <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded-lg">
                                          <div className="text-lg font-bold text-yellow-600">
                                            {patient.riskLevel}
                                          </div>
                                          <div className="text-xs text-yellow-700 dark:text-yellow-300">Risk Level</div>
                                        </div>
                                        <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded-lg">
                                          <div className="text-lg font-bold text-yellow-600">
                                            {patient.status.replace('-', ' ')}
                                          </div>
                                          <div className="text-xs text-yellow-700 dark:text-yellow-300">Status</div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </DialogContent>
                            </Dialog>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-white/20">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-4 mb-3">
                          <div>
                            <p className="text-xs opacity-70">Doctor</p>
                            <p className="text-sm font-semibold">{patient.doctor}</p>
                          </div>
                          <div>
                            <p className="text-xs opacity-70">Hospital</p>
                            <p className="text-sm font-semibold">{patient.hospital}</p>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-xs">
                            <span className="opacity-70">Adherence Progress</span>
                            <span className="font-medium">{patient.adherenceRate}%</span>
                          </div>
                          <Progress value={patient.adherenceRate} className="h-2" />
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Hospital Performance */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-lg sm:text-xl">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg">
                    <Building2 className="h-5 w-5 text-white" />
                  </div>
                  <span>Hospital Adherence Performance</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {isLoading ? (
                    <div className="space-y-4">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="modern-card p-4 animate-pulse">
                          <div className="flex items-center space-x-3">
                            <div className="h-12 w-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : hospitalPerformance && hospitalPerformance.length > 0 ? (
                    hospitalPerformance.map((hospital) => (
                    <div key={hospital.hospital_id} className="modern-card p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800 hover:scale-[1.01] transition-all duration-300">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10 sm:h-12 sm:w-12 ring-2 ring-white/50 shadow-lg">
                            <AvatarImage src={`/avatars/hospital-${hospital.hospital_id}.jpg`} />
                            <AvatarFallback className="bg-gradient-to-br from-purple-500 to-pink-500 text-white font-semibold">
                              {hospital.hospital_name.split(' ').map(n => n[0]).join('').toUpperCase()}
                            </AvatarFallback>
                          </Avatar>
                          <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-purple-900 dark:text-purple-100 truncate text-sm sm:text-base">
                              {hospital.hospital_name}
                            </h3>
                            <p className="text-xs sm:text-sm text-purple-700 dark:text-purple-300 truncate">{hospital.location}</p>
                          </div>
                        </div>
                        <Badge variant="outline" className="text-xs modern-badge bg-purple-100 text-purple-800 border-purple-200">
                          {Math.round(hospital.adherence_rate)}% adherence
                        </Badge>
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="text-purple-700 dark:text-purple-300">Adherence</span>
                            <span className="font-medium text-purple-900 dark:text-purple-100">{Math.round(hospital.adherence_rate)}%</span>
                          </div>
                          <Progress value={hospital.adherence_rate} className="h-2" />
                        </div>
                        <div>
                          <p className="text-xs text-purple-700 dark:text-purple-300">Patients</p>
                          <p className="text-sm font-medium text-purple-900 dark:text-purple-100">{hospital.policy_holders}</p>
                        </div>
                        <div>
                          <p className="text-xs text-purple-700 dark:text-purple-300">Doctors</p>
                          <p className="text-sm font-medium text-purple-900 dark:text-purple-100">{hospital.doctors}</p>
                        </div>
                      </div>
                    </div>
                  ))
                  ) : (
                    <div className="text-center py-8">
                      <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-500 dark:text-gray-400">No hospital performance data available</p>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={refreshAll}
                        className="mt-2"
                      >
                        Refresh Data
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Adherence Summary */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Activity className="h-4 w-4 text-white" />
                  </div>
                  <span>Adherence Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="modern-card p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-semibold text-green-900 dark:text-green-100">Overall Adherence</h3>
                    <Badge variant="outline" className="text-xs modern-badge bg-green-100 text-green-800 border-green-200">
                      {avgAdherence}%
                    </Badge>
                  </div>
                  <Progress value={avgAdherence} className="h-3 mb-3" />
                  <div className="grid grid-cols-3 gap-2 text-center">
                    <div className="p-2 bg-white/50 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold text-green-600">
                        {transformedPolicyHolders.filter(p => p.adherenceRate >= 90).length}
                      </div>
                      <div className="text-xs text-green-700 dark:text-green-300">Excellent</div>
                    </div>
                    <div className="p-2 bg-white/50 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold text-blue-600">
                        {transformedPolicyHolders.filter(p => p.adherenceRate >= 80 && p.adherenceRate < 90).length}
                      </div>
                      <div className="text-xs text-blue-700 dark:text-blue-300">Good</div>
                    </div>
                    <div className="p-2 bg-white/50 dark:bg-black/20 rounded-lg">
                      <div className="text-lg font-bold text-red-600">
                        {transformedPolicyHolders.filter(p => p.adherenceRate < 80).length}
                      </div>
                      <div className="text-xs text-red-700 dark:text-red-300">Poor</div>
                    </div>
                  </div>
                </div>

                <div className="modern-card p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                  <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-3">Condition Breakdown</h3>
                  <div className="space-y-3">
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-blue-700 dark:text-blue-300">Diabetes</span>
                        <span className="font-medium text-blue-900 dark:text-blue-100">95%</span>
                      </div>
                      <Progress value={95} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-blue-700 dark:text-blue-300">Hypertension</span>
                        <span className="font-medium text-blue-900 dark:text-blue-100">78%</span>
                      </div>
                      <Progress value={78} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-blue-700 dark:text-blue-300">Asthma</span>
                        <span className="font-medium text-blue-900 dark:text-blue-100">88%</span>
                      </div>
                      <Progress value={88} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-blue-700 dark:text-blue-300">Heart Disease</span>
                        <span className="font-medium text-blue-900 dark:text-blue-100">65%</span>
                      </div>
                      <Progress value={65} className="h-2" />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Risk Assessment */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg">
                    <Heart className="h-4 w-4 text-white" />
                  </div>
                  <span>Risk Assessment</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="modern-card p-3 bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm text-red-900 dark:text-red-100">High Risk Patients</h4>
                    <Badge variant="outline" className="text-xs modern-badge bg-red-100 text-red-800 border-red-200">
                      {highRiskPatients} patients
                    </Badge>
                  </div>
                  <p className="text-xs text-red-700 dark:text-red-300">
                    Patients with critical conditions and low adherence requiring immediate attention
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm text-yellow-900 dark:text-yellow-100">Low Adherence</h4>
                    <Badge variant="outline" className="text-xs modern-badge bg-yellow-100 text-yellow-800 border-yellow-200">
                      {lowAdherencePatients} patients
                    </Badge>
                  </div>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300">
                    Patients with adherence rates below 80% who need intervention
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-sm text-blue-900 dark:text-blue-100">Upcoming Appointments</h4>
                    <Badge variant="outline" className="text-xs modern-badge bg-blue-100 text-blue-800 border-blue-200">
                      {policyHolders?.data?.length || 0} scheduled
                    </Badge>
                  </div>
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    All patients have follow-up appointments scheduled within the next 30 days
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Insights */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <Sparkles className="h-5 w-5 text-yellow-500" />
                  <span>Insights</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="modern-card p-3 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-semibold text-sm text-green-900 dark:text-green-100">Top Hospital</span>
                  </div>
                  <p className="text-xs text-green-700 dark:text-green-300">
                    St. Mary Medical Center has the highest adherence rate at 92%
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border border-yellow-200 dark:border-yellow-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <Clock className="h-4 w-4 text-yellow-600" />
                    <span className="font-semibold text-sm text-yellow-900 dark:text-yellow-100">Intervention Needed</span>
                  </div>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300">
                    Heart disease patients show 23% lower adherence than other conditions
                  </p>
                </div>
                
                <div className="modern-card p-3 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center space-x-2 mb-1">
                    <Target className="h-4 w-4 text-blue-600" />
                    <span className="font-semibold text-sm text-blue-900 dark:text-blue-100">Cost Reduction</span>
                  </div>
                  <p className="text-xs text-blue-700 dark:text-blue-300">
                    Improving adherence by 10% could reduce hospitalization costs by 15%
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}