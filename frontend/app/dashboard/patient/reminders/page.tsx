'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useMedicineStore, useAppStore } from '@/lib/store';
import { 
  Bell, 
  Clock, 
  Phone, 
  MessageSquare, 
  Smartphone,
  Volume2,
  Settings,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  AlertCircle,
  Sparkles,
  Zap,
  Shield,
  Target
} from 'lucide-react';
import { useState } from 'react';
import { format, addHours } from 'date-fns';

interface ReminderSettings {
  pushNotifications: boolean;
  smsReminders: boolean;
  voiceCalls: boolean;
  emailReminders: boolean;
  reminderTiming: string;
  snoozeInterval: string;
  escalationEnabled: boolean;
  emergencyContact: string;
}

export default function RemindersPage() {
  const { reminders, addReminder } = useMedicineStore();
  const { addNotification } = useAppStore();
  
  const [settings, setSettings] = useState<ReminderSettings>({
    pushNotifications: true,
    smsReminders: true,
    voiceCalls: false,
    emailReminders: true,
    reminderTiming: '15',
    snoozeInterval: '15',
    escalationEnabled: true,
    emergencyContact: '+****************',
  });

  // Mock upcoming reminders
  const upcomingReminders = [
    {
      id: '1',
      medicine: 'Metformin',
      time: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
      type: 'notification',
      status: 'pending'
    },
    {
      id: '2',
      medicine: 'Lisinopril',
      time: new Date(Date.now() + 6 * 60 * 60 * 1000), // 6 hours from now
      type: 'sms',
      status: 'pending'
    },
    {
      id: '3',
      medicine: 'Omeprazole',
      time: addHours(new Date(), 18), // Tomorrow morning
      type: 'notification',
      status: 'pending'
    },
  ];

  const handleSettingChange = (key: keyof ReminderSettings, value: boolean | string) => {
    setSettings(prev => ({ ...prev, [key]: value }));
    addNotification({
      type: 'success',
      title: 'Settings Updated',
      message: 'Your reminder preferences have been saved.',
    });
  };

  const handleTestReminder = (type: string) => {
    addNotification({
      type: 'info',
      title: 'Test Reminder Sent',
      message: `A test ${type} reminder has been sent to verify your settings.`,
    });
  };

  const getReminderTypeIcon = (type: string) => {
    switch (type) {
      case 'notification':
        return <Smartphone className="h-4 w-4" />;
      case 'sms':
        return <MessageSquare className="h-4 w-4" />;
      case 'call':
        return <Phone className="h-4 w-4" />;
      case 'email':
        return <Bell className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getReminderTypeColor = (type: string) => {
    switch (type) {
      case 'notification':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'sms':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'call':
        return 'text-purple-600 bg-purple-50 border-purple-200';
      case 'email':
        return 'text-orange-600 bg-orange-50 border-orange-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 sm:space-y-8">
        {/* Breadcrumb */}
        <Breadcrumb
          items={[
            { label: 'Dashboard', href: '/dashboard/patient' },
            { label: 'Reminders', current: true },
          ]}
        />

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div className="flex items-center space-x-3">
            <div className="h-12 w-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg animate-pulse-glow">
              <Bell className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold text-gradient">Reminder Settings</h1>
              <p className="text-muted-foreground mt-1 text-sm sm:text-base">
                Configure how and when you want to be reminded about your medications
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6 sm:gap-8">
          {/* Main Settings */}
          <div className="xl:col-span-2 space-y-6 sm:space-y-8">
            {/* Notification Methods */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Bell className="h-5 w-5 text-white" />
                  </div>
                  <span>Notification Methods</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Push Notifications */}
                <div className="modern-card p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg shadow-lg">
                        <Smartphone className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <Label className="text-base font-semibold text-blue-900 dark:text-blue-100">Push Notifications</Label>
                        <p className="text-sm text-blue-700 dark:text-blue-300">Instant alerts on your device</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTestReminder('push notification')}
                        disabled={!settings.pushNotifications}
                        className="hover:bg-blue-100 dark:hover:bg-blue-800"
                      >
                        Test
                      </Button>
                      <Switch
                        checked={settings.pushNotifications}
                        onCheckedChange={(checked) => handleSettingChange('pushNotifications', checked)}
                      />
                    </div>
                  </div>
                </div>

                {/* SMS Reminders */}
                <div className="modern-card p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg shadow-lg">
                        <MessageSquare className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <Label className="text-base font-semibold text-green-900 dark:text-green-100">SMS Reminders</Label>
                        <p className="text-sm text-green-700 dark:text-green-300">Text messages to your phone</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTestReminder('SMS')}
                        disabled={!settings.smsReminders}
                        className="hover:bg-green-100 dark:hover:bg-green-800"
                      >
                        Test
                      </Button>
                      <Switch
                        checked={settings.smsReminders}
                        onCheckedChange={(checked) => handleSettingChange('smsReminders', checked)}
                      />
                    </div>
                  </div>
                </div>

                {/* Voice Calls */}
                <div className="modern-card p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg shadow-lg">
                        <Phone className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <Label className="text-base font-semibold text-purple-900 dark:text-purple-100">Voice Calls</Label>
                        <p className="text-sm text-purple-700 dark:text-purple-300">AI-powered reminder calls</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTestReminder('voice call')}
                        disabled={!settings.voiceCalls}
                        className="hover:bg-purple-100 dark:hover:bg-purple-800"
                      >
                        Test
                      </Button>
                      <Switch
                        checked={settings.voiceCalls}
                        onCheckedChange={(checked) => handleSettingChange('voiceCalls', checked)}
                      />
                    </div>
                  </div>
                </div>

                {/* Email Reminders */}
                <div className="modern-card p-4 bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 border border-orange-200 dark:border-orange-800">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-lg shadow-lg">
                        <Bell className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <Label className="text-base font-semibold text-orange-900 dark:text-orange-100">Email Reminders</Label>
                        <p className="text-sm text-orange-700 dark:text-orange-300">Daily summary emails</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTestReminder('email')}
                        disabled={!settings.emailReminders}
                        className="hover:bg-orange-100 dark:hover:bg-orange-800"
                      >
                        Test
                      </Button>
                      <Switch
                        checked={settings.emailReminders}
                        onCheckedChange={(checked) => handleSettingChange('emailReminders', checked)}
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Timing Settings */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg">
                    <Clock className="h-5 w-5 text-white" />
                  </div>
                  <span>Timing Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="reminderTiming">Reminder Timing</Label>
                    <Select 
                      value={settings.reminderTiming} 
                      onValueChange={(value) => handleSettingChange('reminderTiming', value)}
                    >
                      <SelectTrigger className="bg-muted/30 border-border/50">
                        <SelectValue placeholder="Select timing" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 minutes before</SelectItem>
                        <SelectItem value="15">15 minutes before</SelectItem>
                        <SelectItem value="30">30 minutes before</SelectItem>
                        <SelectItem value="60">1 hour before</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">How early to send reminders</p>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="snoozeInterval">Snooze Interval</Label>
                    <Select 
                      value={settings.snoozeInterval} 
                      onValueChange={(value) => handleSettingChange('snoozeInterval', value)}
                    >
                      <SelectTrigger className="bg-muted/30 border-border/50">
                        <SelectValue placeholder="Select interval" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5 minutes</SelectItem>
                        <SelectItem value="15">15 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="60">1 hour</SelectItem>
                      </SelectContent>
                    </Select>
                    <p className="text-xs text-muted-foreground">Snooze duration</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Emergency Settings */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg">
                    <AlertCircle className="h-5 w-5 text-white" />
                  </div>
                  <span>Emergency Settings</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="modern-card p-4 bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 border border-red-200 dark:border-red-800">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gradient-to-br from-red-500 to-pink-500 rounded-lg shadow-lg">
                        <Shield className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <Label className="text-base font-semibold text-red-900 dark:text-red-100">Escalation Alerts</Label>
                        <p className="text-sm text-red-700 dark:text-red-300">
                          Alert emergency contact if doses are missed repeatedly
                        </p>
                      </div>
                    </div>
                    <Switch
                      checked={settings.escalationEnabled}
                      onCheckedChange={(checked) => handleSettingChange('escalationEnabled', checked)}
                    />
                  </div>
                </div>

                {settings.escalationEnabled && (
                  <div className="space-y-2">
                    <Label htmlFor="emergencyContact">Emergency Contact</Label>
                    <Input
                      id="emergencyContact"
                      value={settings.emergencyContact}
                      onChange={(e) => handleSettingChange('emergencyContact', e.target.value)}
                      placeholder="Enter phone number"
                      className="bg-muted/30 border-border/50 focus:bg-background transition-colors"
                    />
                    <p className="text-xs text-muted-foreground">
                      This contact will be notified if you miss multiple doses
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Reminders */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Clock className="h-4 w-4 text-white" />
                  </div>
                  <span>Upcoming Reminders</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {upcomingReminders.map((reminder) => (
                  <div key={reminder.id} className="modern-card p-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold text-sm text-blue-900 dark:text-blue-100">{reminder.medicine}</h4>
                      <Badge variant="outline" className={`text-xs modern-badge ${getReminderTypeColor(reminder.type)}`}>
                        {getReminderTypeIcon(reminder.type)}
                        <span className="ml-1 capitalize">{reminder.type}</span>
                      </Badge>
                    </div>
                    <p className="text-xs text-blue-700 dark:text-blue-300">
                      {format(reminder.time, 'MMM d, h:mm a')}
                    </p>
                  </div>
                ))}
                <Button variant="outline" className="w-full text-sm hover:bg-accent/50 interactive-element">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Custom Reminder
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="text-base sm:text-lg">Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                  <Volume2 className="h-4 w-4 mr-2" />
                  Test All Reminders
                </Button>
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                  <Settings className="h-4 w-4 mr-2" />
                  Advanced Settings
                </Button>
                <Button variant="outline" className="w-full justify-start text-sm hover:bg-accent/50 interactive-element">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Reminder History
                </Button>
              </CardContent>
            </Card>

            {/* Tips */}
            <Card className="modern-card">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-base sm:text-lg">
                  <Sparkles className="h-5 w-5 text-yellow-500" />
                  <span>Tips</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div className="modern-card p-3 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-800">
                  <p className="font-semibold text-blue-900 dark:text-blue-100">Enable multiple methods</p>
                  <p className="text-blue-700 dark:text-blue-300 text-xs mt-1">
                    Use both push notifications and SMS for better reliability
                  </p>
                </div>
                <div className="modern-card p-3 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border border-green-200 dark:border-green-800">
                  <p className="font-semibold text-green-900 dark:text-green-100">Set appropriate timing</p>
                  <p className="text-green-700 dark:text-green-300 text-xs mt-1">
                    15-30 minutes before gives you time to prepare
                  </p>
                </div>
                <div className="modern-card p-3 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border border-purple-200 dark:border-purple-800">
                  <p className="font-semibold text-purple-900 dark:text-purple-100">Test your settings</p>
                  <p className="text-purple-700 dark:text-purple-300 text-xs mt-1">
                    Use the test buttons to verify everything works
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}