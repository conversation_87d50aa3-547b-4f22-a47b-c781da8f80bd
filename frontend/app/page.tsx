'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/lib/store';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Activity, 
  Shield, 
  Clock, 
  Trophy, 
  Pill, 
  Users, 
  Building2, 
  ChevronRight,
  Star,
  CheckCircle,
  ArrowRight,
  Zap,
  BarChart3,
  Heart,
  Brain,
  Smartphone,
  Globe,
  Sparkles,
  TrendingUp,
  Menu,
  X,
  Landmark,
  FileCheck,
  ClipboardCheck
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { useTheme } from 'next-themes';
import Image from 'next/image';

const features = [
  {
    icon: Brain,
    title: 'AI-Powered Extraction',
    description: 'Advanced OCR technology automatically extracts medication details from prescriptions with 99.7% accuracy',
    gradient: 'from-purple-500 to-pink-500',
    bgGradient: 'from-purple-50 to-pink-50',
    borderGradient: 'from-purple-200 to-pink-200',
  },
  {
    icon: Smartphone,
    title: 'Smart Reminders',
    description: 'Intelligent notification system with voice calls, SMS, and push notifications that adapt to your routine',
    gradient: 'from-blue-500 to-cyan-500',
    bgGradient: 'from-blue-50 to-cyan-50',
    borderGradient: 'from-blue-200 to-cyan-200',
  },
  {
    icon: BarChart3,
    title: 'Real-time Analytics',
    description: 'Comprehensive dashboards with actionable insights for patients, doctors, and healthcare organizations',
    gradient: 'from-green-500 to-emerald-500',
    bgGradient: 'from-green-50 to-emerald-50',
    borderGradient: 'from-green-200 to-emerald-200',
  },
  {
    icon: Heart,
    title: 'Gamified Experience',
    description: 'Boost adherence with streaks, achievements, and rewards that make taking medication engaging',
    gradient: 'from-red-500 to-orange-500',
    bgGradient: 'from-red-50 to-orange-50',
    borderGradient: 'from-red-200 to-orange-200',
  },
];

const stats = [
  { value: '99.7%', label: 'Adherence Rate', sublabel: 'Average improvement' },
  { value: '2.3M+', label: 'Patients Served', sublabel: 'Across 50+ countries' },
  { value: '15K+', label: 'Healthcare Providers', sublabel: 'Trust our platform' },
  { value: '4.9/5', label: 'User Rating', sublabel: 'From 50K+ reviews' },
];

const testimonials = [
  {
    quote: "MedCare transformed how we monitor patient adherence. Our readmission rates dropped by 40%.",
    author: "Dr. Sarah Chen",
    role: "Chief Medical Officer",
    company: "Stanford Health Care",
    avatar: "SC",
    gradient: "from-blue-500 to-purple-500"
  },
  {
    quote: "The AI prescription scanning is incredibly accurate. It saves our staff 3 hours per day.",
    author: "Michael Rodriguez",
    role: "Pharmacy Director", 
    company: "Mayo Clinic",
    avatar: "MR",
    gradient: "from-green-500 to-teal-500"
  },
  {
    quote: "My patients love the gamification features. Adherence rates improved by 60% in 3 months.",
    author: "Dr. Emily Watson",
    role: "Family Medicine",
    company: "Johns Hopkins",
    avatar: "EW",
    gradient: "from-pink-500 to-rose-500"
  }
];

const roles = [
  {
    icon: Users,
    title: 'For Patients',
    description: 'Never miss a dose with intelligent reminders and progress tracking',
    href: '/auth/login?role=patient',
    features: ['Smart reminders', 'Progress tracking', 'Achievement system', 'Health insights'],
    gradient: 'from-blue-500 to-purple-500',
    bgGradient: 'from-blue-50 to-purple-50',
  },
  {
    icon: Shield,
    title: 'For Doctors',
    description: 'Monitor patient adherence and improve treatment outcomes',
    href: '/auth/login?role=doctor',
    features: ['Patient monitoring', 'Adherence analytics', 'Prescription management', 'Clinical insights'],
    gradient: 'from-green-500 to-emerald-500',
    bgGradient: 'from-green-50 to-emerald-50',
  },
  {
    icon: Building2,
    title: 'For Healthcare Organizations',
    description: 'Comprehensive platform for managing multiple providers and patients',
    href: '/auth/login?role=hospital',
    features: ['Multi-provider dashboard', 'Population health', 'ROI analytics', 'Integration APIs'],
    gradient: 'from-orange-500 to-red-500',
    bgGradient: 'from-orange-50 to-red-50',
  },
  {
    icon: Landmark,
    title: 'For Insurance Providers',
    description: 'Track policy holders\' medicine adherence for better decision making',
    href: '/auth/login?role=insurance',
    features: ['Adherence tracking', 'Provider analytics', 'Risk assessment', 'Patient insights'],
    gradient: 'from-purple-500 to-indigo-500',
    bgGradient: 'from-purple-50 to-indigo-50',
  }
];

export default function Home() {
  const router = useRouter();
  const { isAuthenticated, user } = useAuthStore();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { theme } = useTheme();

  useEffect(() => {
    if (isAuthenticated && user) {
      router.push(`/dashboard/${user.role}`);
    }
  }, [isAuthenticated, user, router]);

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="sticky top-0 z-50 border-b bg-white/80 backdrop-blur-xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center shadow-lg">
                <Pill className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  MedCare
                </h1>
                <p className="text-xs text-gray-500 -mt-1 hidden sm:block">AI-Powered Healthcare</p>
              </div>
            </div>
            
            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-4">
              <Link href="/pricing">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                  Pricing
                </Button>
              </Link>
              <Link href="/auth/login">
                <Button variant="ghost" className="text-gray-600 hover:text-gray-900">
                  Sign In
                </Button>
              </Link>
              <Link href="/auth/register">
                <Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200">
                  Get Started Free
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="p-2"
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
              </Button>
            </div>
          </div>

          {/* Mobile Menu */}
          {mobileMenuOpen && (
            <div className="md:hidden border-t bg-white/95 backdrop-blur-xl">
              <div className="px-4 py-6 space-y-4">
                <Link href="/pricing" onClick={() => setMobileMenuOpen(false)}>
                  <Button variant="ghost" className="w-full justify-start text-gray-600 hover:text-gray-900">
                    Pricing
                  </Button>
                </Link>
                <Link href="/auth/login" onClick={() => setMobileMenuOpen(false)}>
                  <Button variant="ghost" className="w-full justify-start text-gray-600 hover:text-gray-900">
                    Sign In
                  </Button>
                </Link>
                <Link href="/auth/register" onClick={() => setMobileMenuOpen(false)}>
                  <Button className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg">
                    Get Started Free
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
          )}
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50" />
        <div className="absolute inset-0 bg-grid-pattern opacity-5" />
        
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-12 sm:pt-20 pb-20 sm:pb-32">
          <div className="text-center max-w-4xl mx-auto">
            <Badge variant="outline" className="mb-4 sm:mb-6 px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium border-blue-200 text-blue-700 bg-blue-50">
              <Zap className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              Trusted by 15,000+ Healthcare Providers
            </Badge>
            
            <h1 className="text-3xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight mb-6 sm:mb-8 leading-tight">
              <span className="bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent">
                Never Miss Your
              </span>
              <br />
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Medicine Again
              </span>
            </h1>
            
            <p className="text-lg sm:text-xl md:text-2xl text-gray-600 mb-8 sm:mb-12 leading-relaxed max-w-3xl mx-auto px-4 sm:px-0">
              AI-powered medicine adherence platform that helps patients, doctors, and hospitals 
              achieve <span className="font-semibold text-blue-600">99.7% adherence rates</span> with 
              intelligent reminders and gamification.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center items-center mb-12 sm:mb-16 px-4 sm:px-0">
              <Link href="/auth/register" className="w-full sm:w-auto">
                <Button size="lg" className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                  Start Free Trial
                  <ChevronRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </Link>
              <Link href="#demo" className="w-full sm:w-auto">
                <Button size="lg" variant="outline" className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg border-2 hover:bg-gray-50 transition-all duration-200">
                  Watch Demo
                  <Globe className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                </Button>
              </Link>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-8 max-w-4xl mx-auto px-4 sm:px-0">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-1 sm:mb-2">
                    {stat.value}
                  </div>
                  <div className="text-xs sm:text-sm font-semibold text-gray-900 mb-1">{stat.label}</div>
                  <div className="text-xs text-gray-500 hidden sm:block">{stat.sublabel}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 sm:py-24 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-20">
            <Badge variant="outline" className="mb-3 sm:mb-4 px-3 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium border-purple-200 text-purple-700 bg-purple-50">
              <Star className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
              Award-Winning Technology
            </Badge>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4 sm:mb-6">
              Powered by Advanced AI
            </h2>
            <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto">
              Our cutting-edge technology stack delivers unparalleled accuracy and user experience
            </p>
          </div>
          
          <div className="grid gap-6 sm:gap-8 md:grid-cols-2">
            {features.map((feature, index) => (
              <Card key={index} className={`group relative overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 bg-gradient-to-br ${feature.bgGradient} backdrop-blur-sm`}>
                {/* Animated background gradient */}
                <div className={`absolute inset-0 bg-gradient-to-br ${feature.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                
                {/* Decorative elements */}
                <div className="absolute top-4 right-4 opacity-10 group-hover:opacity-20 transition-opacity duration-500">
                  <Sparkles className="h-6 w-6 sm:h-8 sm:w-8 text-gray-400" />
                </div>
                
                <CardHeader className="pb-3 sm:pb-4 relative z-10 p-4 sm:p-6">
                  <div className={`w-12 h-12 sm:w-16 sm:h-16 rounded-2xl sm:rounded-3xl bg-gradient-to-br ${feature.gradient} flex items-center justify-center mb-4 sm:mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg`}>
                    <feature.icon className="h-6 w-6 sm:h-8 sm:w-8 text-white" />
                  </div>
                  <CardTitle className="text-xl sm:text-2xl font-bold text-gray-900 group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-gray-900 group-hover:to-blue-600 group-hover:bg-clip-text transition-all duration-300">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="relative z-10 p-4 sm:p-6 pt-0">
                  <CardDescription className="text-gray-700 text-base sm:text-lg leading-relaxed">
                    {feature.description}
                  </CardDescription>
                  
                  {/* Progress indicator */}
                  <div className="mt-4 sm:mt-6 flex items-center space-x-2">
                    <div className={`h-1 w-8 sm:w-12 bg-gradient-to-r ${feature.gradient} rounded-full`} />
                    <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-gray-400 group-hover:text-blue-600 transition-colors duration-300" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-16 sm:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-20">
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4 sm:mb-6">
              Trusted by Healthcare Leaders
            </h2>
            <p className="text-lg sm:text-xl text-gray-600">
              See what top healthcare professionals are saying about MedCare
            </p>
          </div>
          
          <div className="grid gap-6 sm:gap-8 md:grid-cols-2 lg:grid-cols-3">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="group relative overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 bg-white">
                {/* Gradient border effect */}
                <div className={`absolute inset-0 bg-gradient-to-br ${testimonial.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500`} />
                
                <CardContent className="p-6 sm:p-8 relative z-10">
                  <div className="flex mb-4 sm:mb-6">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 text-base sm:text-lg mb-6 sm:mb-8 leading-relaxed font-medium">
                    "{testimonial.quote}"
                  </blockquote>
                  <div className="flex items-center">
                    <div className={`w-12 h-12 sm:w-14 sm:h-14 bg-gradient-to-br ${testimonial.gradient} rounded-xl sm:rounded-2xl flex items-center justify-center text-white font-bold mr-3 sm:mr-4 shadow-lg group-hover:scale-110 transition-transform duration-300 text-sm sm:text-base`}>
                      {testimonial.avatar}
                    </div>
                    <div>
                      <div className="font-bold text-gray-900 text-base sm:text-lg">{testimonial.author}</div>
                      <div className="text-xs sm:text-sm text-gray-600 font-medium">{testimonial.role}</div>
                      <div className="text-xs sm:text-sm text-blue-600 font-semibold">{testimonial.company}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Roles Section */}
      <section className="py-16 sm:py-24 bg-gradient-to-br from-blue-50 to-purple-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-20">
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 mb-4 sm:mb-6">
              Built for Every Healthcare Role
            </h2>
            <p className="text-lg sm:text-xl text-gray-600">
              Tailored experiences that deliver value across your entire organization
            </p>
          </div>
          
          <div className="grid gap-6 sm:gap-8 md:grid-cols-2 lg:grid-cols-4">
            {roles.map((role, index) => (
              <Card key={index} className={`group relative overflow-hidden border-0 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-3 bg-gradient-to-br ${role.bgGradient} backdrop-blur-sm`}>
                {/* Animated background */}
                <div className={`absolute inset-0 bg-gradient-to-br ${role.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`} />
                
                {/* Decorative corner element */}
                <div className="absolute top-0 right-0 w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-white/20 to-transparent rounded-bl-2xl sm:rounded-bl-3xl opacity-50" />
                
                <CardHeader className="text-center pb-4 sm:pb-6 relative z-10 p-4 sm:p-6">
                  <div className={`w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br ${role.gradient} rounded-2xl sm:rounded-3xl flex items-center justify-center mx-auto mb-4 sm:mb-6 group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-xl`}>
                    <role.icon className="h-8 w-8 sm:h-10 sm:w-10 text-white" />
                  </div>
                  <CardTitle className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">{role.title}</CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-6 sm:space-y-8 relative z-10 p-4 sm:p-6 pt-0">
                  <CardDescription className="text-gray-700 text-base sm:text-lg font-medium">
                    {role.description}
                  </CardDescription>
                  
                  <div className="space-y-3 sm:space-y-4">
                    {role.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center text-sm sm:text-base text-gray-700 bg-white/50 rounded-lg p-2.5 sm:p-3 group-hover:bg-white/70 transition-colors duration-300">
                        <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 mr-2 sm:mr-3 flex-shrink-0" />
                        <span className="font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="pt-2 sm:pt-4">
                    <Link href={role.href}>
                      <Button className={`w-full bg-gradient-to-r ${role.gradient} hover:shadow-xl text-white shadow-lg transition-all duration-300 transform group-hover:scale-105 py-2.5 sm:py-3 text-base sm:text-lg font-semibold`}>
                        Get Started
                        <ChevronRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 sm:py-24 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden">
        {/* Background pattern */}
        <div className="absolute inset-0 bg-grid-pattern opacity-10" />
        
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8 relative z-10">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-white mb-4 sm:mb-6">
            Ready to Transform Healthcare?
          </h2>
          <p className="text-lg sm:text-xl text-blue-100 mb-8 sm:mb-12 leading-relaxed">
            Join thousands of healthcare providers who trust MedCare to improve patient outcomes
          </p>
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center max-w-md sm:max-w-none mx-auto">
            <Link href="/auth/register" className="w-full sm:w-auto">
              <Button size="lg" className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg bg-white text-blue-600 hover:bg-gray-50 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105 font-semibold">
                Start Free Trial
                <ArrowRight className="ml-2 h-4 w-4 sm:h-5 sm:w-5" />
              </Button>
            </Link>
            <Link href="/contact" className="w-full sm:w-auto">
              <Button 
                size="lg" 
                className="w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 text-base sm:text-lg border-2 border-white bg-transparent text-white hover:bg-white hover:text-blue-600 transition-all duration-300 font-semibold"
              >
                Contact Sales
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 sm:py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-4 mb-8 sm:mb-12">
            <div className="sm:col-span-2 lg:col-span-1">
              <div className="flex items-center space-x-3 mb-4 sm:mb-6">
                <div className="h-8 w-8 sm:h-10 sm:w-10 bg-gradient-to-br from-blue-600 to-purple-600 rounded-xl sm:rounded-2xl flex items-center justify-center">
                  <Pill className="h-4 w-4 sm:h-6 sm:w-6 text-white" />
                </div>
                <div>
                  <span className="text-xl sm:text-2xl font-bold">MedCare</span>
                  <p className="text-xs sm:text-sm text-gray-400 -mt-1">AI-Powered Healthcare</p>
                </div>
              </div>
              <p className="text-gray-400 text-base sm:text-lg leading-relaxed max-w-md">
                Transforming healthcare through intelligent medicine adherence solutions.
              </p>
            </div>
            
            <div>
              <h3 className="font-semibold text-base sm:text-lg mb-3 sm:mb-4">Product</h3>
              <ul className="space-y-2 sm:space-y-3 text-gray-400 text-sm sm:text-base">
                <li><a href="#" className="hover:text-white transition-colors">Features</a></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
                <li><a href="#" className="hover:text-white transition-colors">API</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Integrations</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-base sm:text-lg mb-3 sm:mb-4">Company</h3>
              <ul className="space-y-2 sm:space-y-3 text-gray-400 text-sm sm:text-base">
                <li><a href="#" className="hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Careers</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Privacy</a></li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-semibold text-base sm:text-lg mb-3 sm:mb-4">Resources</h3>
              <ul className="space-y-2 sm:space-y-3 text-gray-400 text-sm sm:text-base">
                <li><a href="#" className="hover:text-white transition-colors">Blog</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Documentation</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Help Center</a></li>
                <li><a href="#" className="hover:text-white transition-colors">Community</a></li>
              </ul>
            </div>
          </div>
          
          <div className="border-t border-gray-800 pt-6 sm:pt-8 flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
            <p className="text-gray-400 text-sm sm:text-base text-center sm:text-left">
              © 2025 MedCare. All rights reserved.
            </p>
            <div className="flex space-x-4 sm:space-x-6 text-gray-400 text-sm sm:text-base">
              <a href="#" className="hover:text-white transition-colors">Terms</a>
              <a href="#" className="hover:text-white transition-colors">Privacy</a>
              <a href="#" className="hover:text-white transition-colors">Security</a>
            </div>
          </div>
        </div>
      </footer>

      {/* Bolt Badge */}
      <div className="fixed bottom-4 right-4 z-50">
        <Link href="https://bolt.new" target="_blank" rel="noopener noreferrer">
          <div className="w-20 h-20 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110">
            {theme === 'dark' ? (
              <Image 
                src="/bolt_logo_dark.png" 
                alt="Powered by Bolt.new" 
                width={64} 
                height={64} 
                className="w-full h-full"
              />
            ) : (
              <Image 
                src="/bolt_logo_light.png" 
                alt="Powered by Bolt.new" 
                width={64} 
                height={64} 
                className="w-full h-full"
              />
            )}
          </div>
        </Link>
      </div>
    </div>
  );
}