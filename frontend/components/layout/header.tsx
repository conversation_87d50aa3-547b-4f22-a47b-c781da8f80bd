'use client';

import { <PERSON>, <PERSON>, <PERSON>, Sun, Settings } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useAppStore, useAuthStore } from '@/lib/store';
import { useTheme } from 'next-themes';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

export function Header() {
  const { notifications, markNotificationRead } = useAppStore();
  const { theme, setTheme } = useTheme();
  const { user } = useAuthStore();
  const router = useRouter();
  
  const unreadCount = notifications.filter(n => !n.read).length;

  const handleSettingsClick = () => {
    if (user) {
      if (user.role === 'admin') {
        router.push('/dashboard/admin/system');
      } else {
        router.push(`/dashboard/${user.role}/settings`);
      }
    }
  };

  return (
    <header className="modern-card border-b border-border/50 px-4 sm:px-6 py-3 sm:py-4 bg-card/80 backdrop-blur-xl">
      <div className="flex items-center justify-between">
        {/* Search - Hidden on mobile, shown on tablet+ */}
        <div className="flex-1 max-w-lg hidden sm:block">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search medicines, prescriptions..."
              className="pl-10 bg-muted/30 border-border/50 text-sm sm:text-base focus:bg-background transition-colors"
            />
          </div>
        </div>

        {/* Mobile Search Button */}
        <div className="sm:hidden">
          <Button variant="ghost" size="sm" className="h-9 w-9 p-0 hover:bg-accent/50">
            <Search className="h-4 w-4" />
          </Button>
        </div>

        {/* Actions */}
        <div className="flex items-center space-x-2 sm:space-x-4">
          {/* Theme Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
            className="h-9 w-9 p-0 hover:bg-accent/50 relative overflow-hidden"
          >
            <Sun className="h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
            <Moon className="absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
          </Button>

          {/* Settings */}
          <Button
            variant="ghost"
            size="sm"
            className="h-9 w-9 p-0 hover:bg-accent/50"
            onClick={handleSettingsClick}
          >
            <Settings className="h-4 w-4" />
          </Button>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="relative h-9 w-9 p-0 hover:bg-accent/50">
                <Bell className="h-4 w-4" />
                {unreadCount > 0 && (
                  <Badge 
                    variant="destructive" 
                    className={cn(
                      "absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center",
                      "bg-gradient-to-r from-red-500 to-pink-500 animate-pulse-glow"
                    )}
                  >
                    {unreadCount > 9 ? '9+' : unreadCount}
                  </Badge>
                )}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-72 sm:w-80 modern-card">
              <div className="px-4 py-3 border-b border-border/50">
                <h4 className="font-semibold text-sm sm:text-base flex items-center">
                  <Bell className="h-4 w-4 mr-2" />
                  Notifications
                  {unreadCount > 0 && (
                    <Badge variant="secondary" className="ml-auto">
                      {unreadCount} new
                    </Badge>
                  )}
                </h4>
              </div>
              <div className="max-h-80 overflow-y-auto">
                {notifications.length === 0 ? (
                  <div className="px-4 py-8 text-center text-muted-foreground">
                    <Bell className="mx-auto h-8 w-8 text-muted-foreground/50 mb-2" />
                    <p className="text-sm">No notifications yet</p>
                    <p className="text-xs mt-1">We'll notify you when something important happens</p>
                  </div>
                ) : (
                  notifications.slice(0, 10).map((notification) => (
                    <DropdownMenuItem
                      key={notification.id}
                      className="px-4 py-3 cursor-pointer focus:bg-accent/50 border-b border-border/30 last:border-0"
                      onClick={() => markNotificationRead(notification.id)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <h5 className="text-sm font-medium text-foreground truncate pr-2">
                            {notification.title}
                          </h5>
                          <div className="flex items-center space-x-2">
                            {!notification.read && (
                              <div className="h-2 w-2 bg-primary rounded-full animate-pulse" />
                            )}
                            <Badge 
                              variant={
                                notification.type === 'success' ? 'default' :
                                notification.type === 'error' ? 'destructive' :
                                notification.type === 'warning' ? 'secondary' : 'outline'
                              }
                              className="text-xs"
                            >
                              {notification.type}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                          {notification.message}
                        </p>
                        <p className="text-xs text-muted-foreground/70 mt-1">
                          {notification.timestamp.toLocaleTimeString([], { 
                            hour: '2-digit', 
                            minute: '2-digit' 
                          })}
                        </p>
                      </div>
                    </DropdownMenuItem>
                  ))
                )}
              </div>
              {notifications.length > 0 && (
                <div className="px-4 py-2 border-t border-border/50">
                  <Button variant="ghost" className="w-full text-xs hover:bg-accent/50">
                    View all notifications
                  </Button>
                </div>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}