'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Clock, CheckCircle, AlertCircle, Info, Pill, Calendar, Target, Zap } from 'lucide-react';
import type { Medicine } from '@/types';
import { format, differenceInDays } from 'date-fns';
import { cn } from '@/lib/utils';

interface MedicineCardProps {
  medicine: Medicine;
  nextDose?: Date;
  adherenceRate?: number;
  onTakeMedicine?: (medicineId: string) => void;
  onSnooze?: (medicineId: string) => void;
}

export function MedicineCard({
  medicine,
  nextDose,
  adherenceRate = 0,
  onTakeMedicine,
  onSnooze,
}: MedicineCardProps) {
  const [showDetails, setShowDetails] = useState(false);
  
  const daysRemaining = differenceInDays(medicine.endDate, new Date());
  const totalDuration = differenceInDays(medicine.endDate, medicine.startDate);
  const progress = Math.max(0, Math.min(100, ((totalDuration - daysRemaining) / totalDuration) * 100));
  
  const isOverdue = nextDose && nextDose < new Date();
  const isUpcoming = nextDose && nextDose > new Date() && 
    (nextDose.getTime() - new Date().getTime()) < 30 * 60 * 1000; // 30 minutes

  const getStatusColor = () => {
    if (daysRemaining <= 0) return 'from-red-500 to-pink-500';
    if (daysRemaining <= 3) return 'from-yellow-500 to-orange-500';
    return 'from-green-500 to-emerald-500';
  };

  const getAdherenceColor = () => {
    if (adherenceRate >= 90) return 'from-green-500 to-emerald-500';
    if (adherenceRate >= 75) return 'from-yellow-500 to-orange-500';
    return 'from-red-500 to-pink-500';
  };

  return (
    <Card className="modern-card group hover:scale-[1.02] transition-all duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg group-hover:shadow-blue-500/25 transition-all duration-300">
              <Pill className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg text-foreground">{medicine.name}</CardTitle>
              <p className="text-sm text-muted-foreground">
                {medicine.dosage} • {medicine.frequency}
              </p>
            </div>
          </div>
          <Badge 
            variant={daysRemaining > 3 ? 'default' : 'destructive'}
            className={cn(
              "modern-badge",
              daysRemaining > 3 
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200' 
                : 'bg-gradient-to-r from-red-50 to-pink-50 text-red-700 border-red-200'
            )}
          >
            {daysRemaining > 0 ? `${daysRemaining} days left` : 'Expired'}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-3">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground flex items-center">
              <Target className="h-3 w-3 mr-1" />
              Treatment Progress
            </span>
            <span className="font-medium text-foreground">{Math.round(progress)}%</span>
          </div>
          <div className="relative">
            <Progress value={progress} className="h-3" />
            <div className={cn(
              "absolute inset-0 bg-gradient-to-r opacity-20 rounded-full",
              getStatusColor()
            )} />
          </div>
        </div>

        {/* Next Dose */}
        {nextDose && (
          <div className={cn(
            "flex items-center justify-between p-4 rounded-xl border transition-all duration-300",
            isOverdue 
              ? 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200 dark:from-red-900/20 dark:to-pink-900/20 dark:border-red-800' 
              : 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 dark:from-blue-900/20 dark:to-indigo-900/20 dark:border-blue-800'
          )}>
            <div className="flex items-center space-x-3">
              <div className={cn(
                "p-2 rounded-lg",
                isOverdue ? 'bg-red-100 dark:bg-red-800' : 'bg-blue-100 dark:bg-blue-800'
              )}>
                <Clock className={cn(
                  "h-4 w-4",
                  isOverdue ? 'text-red-600 dark:text-red-300' : 'text-blue-600 dark:text-blue-300'
                )} />
              </div>
              <div>
                <p className="text-sm font-medium text-foreground">
                  Next dose: {format(nextDose, 'h:mm a')}
                </p>
                <p className="text-xs text-muted-foreground">
                  {format(nextDose, 'MMM d, yyyy')}
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {isOverdue && (
                <Badge variant="destructive" className="text-xs animate-pulse">
                  Overdue
                </Badge>
              )}
              {isUpcoming && (
                <Badge variant="outline" className="text-xs bg-yellow-50 text-yellow-700 border-yellow-200">
                  Soon
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* Adherence Rate */}
        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
          <div className="flex items-center space-x-2">
            <Zap className="h-4 w-4 text-muted-foreground" />
            <span className="text-sm text-muted-foreground">Adherence Rate</span>
          </div>
          <Badge 
            variant={adherenceRate >= 80 ? 'default' : 'destructive'}
            className={cn(
              "modern-badge",
              adherenceRate >= 80 
                ? 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-700 border-green-200' 
                : 'bg-gradient-to-r from-red-50 to-pink-50 text-red-700 border-red-200'
            )}
          >
            {adherenceRate}%
          </Badge>
        </div>

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-2">
          {onTakeMedicine && (
            <Button
              onClick={() => onTakeMedicine(medicine.id)}
              className={cn(
                "flex-1 transition-all duration-300",
                isOverdue 
                  ? 'bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600 shadow-lg shadow-red-500/25' 
                  : 'btn-primary'
              )}
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark as Taken
            </Button>
          )}
          {onSnooze && nextDose && (
            <Button
              onClick={() => onSnooze(medicine.id)}
              variant="outline"
              size="sm"
              className="hover:bg-accent/50 interactive-element"
            >
              Snooze
            </Button>
          )}
          <Dialog open={showDetails} onOpenChange={setShowDetails}>
            <DialogTrigger asChild>
              <Button variant="outline" size="sm" className="hover:bg-accent/50 interactive-element">
                <Info className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DialogContent className="modern-card">
              <DialogHeader>
                <DialogTitle className="flex items-center space-x-2">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg">
                    <Pill className="h-5 w-5 text-white" />
                  </div>
                  <span>{medicine.name}</span>
                </DialogTitle>
                <DialogDescription>
                  Complete medication information and details
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-xl border border-blue-200 dark:border-blue-800">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">Dosage</h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">{medicine.dosage}</p>
                  </div>
                  <div className="p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-xl border border-green-200 dark:border-green-800">
                    <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">Frequency</h4>
                    <p className="text-sm text-green-700 dark:text-green-300">{medicine.frequency}</p>
                  </div>
                </div>
                
                <div className="p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-xl border border-purple-200 dark:border-purple-800">
                  <h4 className="font-semibold text-purple-900 dark:text-purple-100 mb-2">Instructions</h4>
                  <p className="text-sm text-purple-700 dark:text-purple-300">{medicine.instructions}</p>
                </div>
                
                {medicine.sideEffects && (
                  <div className="p-4 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-xl border border-yellow-200 dark:border-yellow-800">
                    <h4 className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Side Effects</h4>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">{medicine.sideEffects}</p>
                  </div>
                )}
                
                <div className="p-4 bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-900/20 dark:to-slate-900/20 rounded-xl border border-gray-200 dark:border-gray-800">
                  <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-2 flex items-center">
                    <Calendar className="h-4 w-4 mr-2" />
                    Duration
                  </h4>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    {format(medicine.startDate, 'MMM d, yyyy')} - {format(medicine.endDate, 'MMM d, yyyy')}
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    {totalDuration} days total • {daysRemaining > 0 ? `${daysRemaining} days remaining` : 'Completed'}
                  </p>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );
}