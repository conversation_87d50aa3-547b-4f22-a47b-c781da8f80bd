'use client';

import { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { FileText, Upload, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { PrescriptionService } from '@/lib/api/prescriptions';
import { useMedicineStore } from '@/lib/store';
import { toast } from 'sonner';

interface UploadedFile {
  file: File;
  status: 'uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  extractedText?: string;
  error?: string;
}

export function PrescriptionUpload() {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const { addPrescription } = useMedicineStore();

  const processFile = async (file: File) => {
    // Add file to state
    setUploadedFiles(prev => [...prev, {
      file,
      status: 'uploading',
      progress: 0,
    }]);

    try {
      // Simulate upload progress
      for (let progress = 0; progress <= 100; progress += 10) {
        await new Promise(resolve => setTimeout(resolve, 100));
        setUploadedFiles(prev => prev.map(f => 
          f.file === file ? { ...f, progress } : f
        ));
      }

      // Update status to processing
      setUploadedFiles(prev => prev.map(f => 
        f.file === file ? { ...f, status: 'processing', progress: 100 } : f
      ));

      // Upload and process prescription with real AWS Textract
      const uploadedPrescription = await PrescriptionService.uploadPrescription({
        file,
        patientId: undefined, // Will be handled by backend based on current user
      });

      // Update with extracted text and prescription data
      setUploadedFiles(prev => prev.map(f =>
        f.file === file ? {
          ...f,
          status: 'completed',
          extractedText: uploadedPrescription.extractedText || 'No text extracted'
        } : f
      ));

      // Add prescription to store
      addPrescription({
        id: uploadedPrescription.id,
        patientId: uploadedPrescription.patientId,
        doctorId: uploadedPrescription.doctorId,
        uploadedAt: new Date(uploadedPrescription.uploadedAt),
        filename: uploadedPrescription.filename,
        fileUrl: uploadedPrescription.fileUrl,
        status: uploadedPrescription.status,
        medicines: uploadedPrescription.medicines || [],
        extractedText: uploadedPrescription.extractedText || '',
      });

      toast.success('Prescription processed successfully with AWS Textract!');
      
    } catch (error: any) {
      console.error('Prescription upload error:', error);
      setUploadedFiles(prev => prev.map(f =>
        f.file === file ? {
          ...f,
          status: 'error',
          error: error.message || 'Failed to process prescription'
        } : f
      ));
      toast.error(error.message || 'Failed to process prescription');
    }
  };

  const onDrop = useCallback((acceptedFiles: File[]) => {
    acceptedFiles.forEach(processFile);
  }, []);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.jpeg', '.jpg', '.png'],
      'application/pdf': ['.pdf'],
    },
    maxSize: 10 * 1024 * 1024, // 10MB
  });

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="h-5 w-5" />
            <span>Upload Prescription</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
              isDragActive
                ? 'border-blue-400 bg-blue-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
          >
            <input {...getInputProps()} />
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            {isDragActive ? (
              <p className="text-blue-600">Drop the files here...</p>
            ) : (
              <div>
                <p className="text-gray-600 mb-2">
                  Drag & drop prescription files here, or click to select
                </p>
                <p className="text-sm text-gray-500">
                  Supports JPG, PNG, PDF (max 10MB)
                </p>
                <Button variant="outline" className="mt-4">
                  Choose Files
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Processing Files</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {uploadedFiles.map((uploadedFile, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-5 w-5 text-gray-500" />
                    <span className="font-medium">{uploadedFile.file.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {uploadedFile.status === 'uploading' && (
                      <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                    )}
                    {uploadedFile.status === 'processing' && (
                      <Loader2 className="h-4 w-4 animate-spin text-orange-600" />
                    )}
                    {uploadedFile.status === 'completed' && (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    )}
                    {uploadedFile.status === 'error' && (
                      <AlertCircle className="h-4 w-4 text-red-600" />
                    )}
                    <Badge variant={
                      uploadedFile.status === 'completed' ? 'default' :
                      uploadedFile.status === 'error' ? 'destructive' :
                      'secondary'
                    }>
                      {uploadedFile.status}
                    </Badge>
                  </div>
                </div>
                
                {(uploadedFile.status === 'uploading' || uploadedFile.status === 'processing') && (
                  <Progress value={uploadedFile.progress} className="mb-2" />
                )}
                
                {uploadedFile.status === 'completed' && uploadedFile.extractedText && (
                  <div className="bg-gray-50 rounded p-3 mt-3">
                    <h4 className="font-medium text-sm mb-1">Extracted Information:</h4>
                    <p className="text-sm text-gray-600">{uploadedFile.extractedText}</p>
                  </div>
                )}
                
                {uploadedFile.status === 'error' && uploadedFile.error && (
                  <div className="bg-red-50 rounded p-3 mt-3">
                    <p className="text-sm text-red-600">{uploadedFile.error}</p>
                  </div>
                )}
              </div>
            ))}
          </CardContent>
        </Card>
      )}
    </div>
  );
}