'use client';

import React, { useState } from 'react';
import { useWebSocketNotifications, useWebSocketReminders, useWebSocketAdherence, useWebSocketAchievements } from '@/hooks/use-websocket';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Bell, Clock, TrendingUp, Trophy, X, CheckCircle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

// Real-time Notification Feed Component
export function NotificationFeed() {
  const { notifications, markAsRead, clearNotifications } = useWebSocketNotifications();
  const { reminders } = useWebSocketReminders();
  const { adherenceUpdates } = useWebSocketAdherence();
  const { achievements } = useWebSocketAchievements();

  const [activeTab, setActiveTab] = useState('all');

  const allItems = [
    ...notifications.map(n => ({ ...n, category: 'notification', icon: Bell })),
    ...reminders.map(r => ({ ...r, category: 'reminder', icon: Clock })),
    ...adherenceUpdates.map(a => ({ ...a, category: 'adherence', icon: TrendingUp })),
    ...achievements.map(a => ({ ...a, category: 'achievement', icon: Trophy })),
  ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());

  const getFilteredItems = () => {
    switch (activeTab) {
      case 'notifications':
        return notifications.map(n => ({ ...n, category: 'notification', icon: Bell }));
      case 'reminders':
        return reminders.map(r => ({ ...r, category: 'reminder', icon: Clock }));
      case 'adherence':
        return adherenceUpdates.map(a => ({ ...a, category: 'adherence', icon: TrendingUp }));
      case 'achievements':
        return achievements.map(a => ({ ...a, category: 'achievement', icon: Trophy }));
      default:
        return allItems;
    }
  };

  const filteredItems = getFilteredItems();

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'notification':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'reminder':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'adherence':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'achievement':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const renderItem = (item: any, index: number) => {
    const Icon = item.icon;
    const isNotification = item.category === 'notification';
    
    return (
      <div
        key={`${item.category}-${item.id || item.reminderId || item.achievementId || index}`}
        className={cn(
          "p-4 border-b border-border/50 hover:bg-accent/30 transition-colors",
          isNotification && !item.read && "bg-blue-50/50 dark:bg-blue-950/20 border-l-4 border-l-blue-500"
        )}
      >
        <div className="flex items-start space-x-3">
          <div className={cn(
            "p-2 rounded-full",
            getCategoryColor(item.category)
          )}>
            <Icon className="h-4 w-4" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium truncate">
                {item.title || item.medicineName || `${item.category} Update`}
              </h4>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className={cn("text-xs", getCategoryColor(item.category))}>
                  {item.category}
                </Badge>
                {isNotification && !item.read && (
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => markAsRead(item.id)}
                    className="h-6 w-6 p-0 hover:bg-accent"
                  >
                    <CheckCircle className="h-3 w-3" />
                  </Button>
                )}
              </div>
            </div>
            
            <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
              {item.message || item.description || `${item.category} event occurred`}
            </p>
            
            <div className="flex items-center justify-between mt-2">
              <span className="text-xs text-muted-foreground">
                {formatDistanceToNow(new Date(item.timestamp), { addSuffix: true })}
              </span>
              
              {item.points && (
                <Badge variant="secondary" className="text-xs">
                  +{item.points} points
                </Badge>
              )}
              
              {item.adherenceRate && (
                <Badge variant="secondary" className="text-xs">
                  {Math.round(item.adherenceRate)}% adherence
                </Badge>
              )}
              
              {item.type && item.category === 'reminder' && (
                <Badge variant="outline" className="text-xs">
                  {item.type}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (allItems.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>Real-time Updates</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Bell className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No real-time updates yet</p>
            <p className="text-sm text-muted-foreground mt-1">
              Live notifications will appear here when available
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Bell className="h-5 w-5" />
            <span>Real-time Updates</span>
            <Badge variant="secondary" className="ml-2">
              {allItems.length}
            </Badge>
          </CardTitle>
          
          {notifications.length > 0 && (
            <Button
              size="sm"
              variant="outline"
              onClick={clearNotifications}
              className="text-xs"
            >
              <X className="h-3 w-3 mr-1" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="px-6 pb-4">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="all" className="text-xs">
                All ({allItems.length})
              </TabsTrigger>
              <TabsTrigger value="notifications" className="text-xs">
                Alerts ({notifications.length})
              </TabsTrigger>
              <TabsTrigger value="reminders" className="text-xs">
                Reminders ({reminders.length})
              </TabsTrigger>
              <TabsTrigger value="adherence" className="text-xs">
                Adherence ({adherenceUpdates.length})
              </TabsTrigger>
              <TabsTrigger value="achievements" className="text-xs">
                Rewards ({achievements.length})
              </TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value={activeTab} className="mt-0">
            <ScrollArea className="h-96">
              {filteredItems.length > 0 ? (
                <div>
                  {filteredItems.map((item, index) => renderItem(item, index))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Bell className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    No {activeTab === 'all' ? '' : activeTab} updates yet
                  </p>
                </div>
              )}
            </ScrollArea>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

// Compact Real-time Status Widget
export function RealTimeStatusWidget() {
  const { notifications } = useWebSocketNotifications();
  const { reminders } = useWebSocketReminders();
  const { adherenceUpdates } = useWebSocketAdherence();
  const { achievements } = useWebSocketAchievements();

  const unreadNotifications = notifications.filter(n => !n.read).length;
  const recentReminders = reminders.filter(r => 
    new Date(r.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
  ).length;
  const recentAdherence = adherenceUpdates.filter(a => 
    new Date(a.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
  ).length;
  const recentAchievements = achievements.filter(a => 
    new Date(a.timestamp).getTime() > Date.now() - 24 * 60 * 60 * 1000
  ).length;

  return (
    <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
      <div className="text-center">
        <div className="flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-full mx-auto mb-2">
          <Bell className="h-6 w-6 text-blue-600 dark:text-blue-400" />
        </div>
        <p className="text-2xl font-bold">{unreadNotifications}</p>
        <p className="text-xs text-muted-foreground">New Alerts</p>
      </div>
      
      <div className="text-center">
        <div className="flex items-center justify-center w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-full mx-auto mb-2">
          <Clock className="h-6 w-6 text-orange-600 dark:text-orange-400" />
        </div>
        <p className="text-2xl font-bold">{recentReminders}</p>
        <p className="text-xs text-muted-foreground">Reminders (24h)</p>
      </div>
      
      <div className="text-center">
        <div className="flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-900 rounded-full mx-auto mb-2">
          <TrendingUp className="h-6 w-6 text-green-600 dark:text-green-400" />
        </div>
        <p className="text-2xl font-bold">{recentAdherence}</p>
        <p className="text-xs text-muted-foreground">Adherence Updates</p>
      </div>
      
      <div className="text-center">
        <div className="flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-full mx-auto mb-2">
          <Trophy className="h-6 w-6 text-purple-600 dark:text-purple-400" />
        </div>
        <p className="text-2xl font-bold">{recentAchievements}</p>
        <p className="text-xs text-muted-foreground">New Rewards</p>
      </div>
    </div>
  );
}
