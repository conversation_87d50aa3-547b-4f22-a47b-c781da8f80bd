// Simple Node.js script to test the API directly
const axios = require('axios');

async function testAPI() {
  try {
    console.log('🧪 Testing API endpoints...\n');

    // Test without auth first to see the error
    console.log('1. Testing GET /api/v1/prescriptions (no auth):');
    try {
      const response = await axios.get('http://localhost:3003/api/v1/prescriptions');
      console.log('✅ Success:', response.data);
    } catch (error) {
      console.log('❌ Expected auth error:', error.response?.status, error.response?.data?.message);
    }

    // Test with a sample auth token (you'll need to get this from browser localStorage)
    console.log('\n2. To test with auth, you need to:');
    console.log('   - Open browser dev tools on http://localhost:3002');
    console.log('   - Go to Application/Storage > Local Storage');
    console.log('   - Copy the "auth-token" value');
    console.log('   - Run: node debug-api.js "your-token-here"');

    const token = process.argv[2];
    if (token) {
      console.log('\n3. Testing with provided token:');
      try {
        const response = await axios.get('http://localhost:3001/api/v1/prescriptions', {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        console.log('✅ Authenticated request successful!');
        console.log('📊 Response structure:', {
          type: typeof response.data,
          isArray: Array.isArray(response.data),
          keys: Object.keys(response.data || {}),
          length: response.data?.length || 'N/A'
        });
        
        if (response.data && response.data.length > 0) {
          console.log('📋 First prescription sample:');
          const first = response.data[0];
          console.log({
            id: first.id,
            filename: first.filename,
            status: first.status,
            ai_extracted_medicines: first.ai_extracted_medicines,
            ai_extraction_success: first.ai_extraction_success,
            extracted_text: first.extracted_text ? `${first.extracted_text.substring(0, 100)}...` : null
          });
        }
      } catch (error) {
        console.log('❌ Auth request failed:', error.response?.status, error.response?.data);
      }
    }

  } catch (err) {
    console.error('❌ Test error:', err.message);
  }
}

testAPI();
