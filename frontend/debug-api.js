// Simple Node.js script to test the API directly
const axios = require('axios');

async function testAPI() {
  console.log('🧪 Testing API endpoints...\n');

  // Test without auth first to see the error
  console.log('1. Testing GET /api/v1/prescriptions (no auth):');
  try {
    const response = await axios.get('http://localhost:3001/api/v1/prescriptions');
    console.log('✅ Success:', response.data);
  } catch (error) {
    console.log('❌ Expected auth error:', error.response?.status, error.response?.data?.message);
  }

  // Try to login as patient first
  console.log('\n2. Testing patient login:');
  let token = process.argv[2];

  if (!token) {
    try {
      const loginResponse = await axios.post('http://localhost:3001/api/v1/auth/login', {
        email: '<EMAIL>',
        password: 'password123'
      });
      console.log('✅ Login successful');
      token = loginResponse.data.access_token;
      console.log('🔑 Token received:', token.substring(0, 20) + '...');
    } catch (loginError) {
      console.log('❌ Login failed:', loginError.response?.status, loginError.response?.data);
      console.log('\n   Alternative: Get token manually:');
      console.log('   - Open browser dev tools on http://localhost:3002');
      console.log('   - Go to Application/Storage > Local Storage');
      console.log('   - Copy the "auth-token" value');
      console.log('   - Run: node debug-api.js "your-token-here"');
      return; // Exit if login failed
    }
  } else {
    console.log('🔑 Using provided token:', token.substring(0, 20) + '...');
  }

  // Now test prescriptions with the token
  if (token) {
    console.log('\n3. Testing GET /api/v1/prescriptions (with auth):');
    try {
      const response = await axios.get('http://localhost:3001/api/v1/prescriptions', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });
      console.log('✅ Authenticated request successful!');
      console.log('📊 Response structure:', {
        type: typeof response.data,
        isArray: Array.isArray(response.data),
        keys: Object.keys(response.data || {}),
        length: response.data?.length || 'N/A'
      });

      if (response.data && response.data.length > 0) {
        console.log('📋 First prescription sample:');
        const first = response.data[0];
        console.log({
          id: first.id,
          filename: first.filename,
          status: first.status,
          ai_extracted_medicines: first.ai_extracted_medicines,
          ai_extraction_success: first.ai_extraction_success,
          extracted_text: first.extracted_text ? `${first.extracted_text.substring(0, 100)}...` : null
        });
      } else {
        console.log('📋 No prescriptions found for this patient');
      }
    } catch (error) {
      console.log('❌ Auth request failed:', error.response?.status, error.response?.data);
    }
  } else {
    console.log('❌ No token available for testing');
  }
}

testAPI().catch(console.error);
