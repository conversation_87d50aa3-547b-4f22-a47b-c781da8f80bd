import { useEffect, useMemo } from 'react';
import { useHospitalStore } from '@/lib/stores/hospital-store';
import type {
  HospitalPatient,
  HospitalDoctor,
  HospitalDashboardSummary,
  HospitalPatientsQuery,
  HospitalDoctorsQuery,
} from '@/lib/api/hospitals';

/**
 * Hook for managing hospital dashboard data
 */
export function useHospitalDashboard() {
  const {
    dashboardSummary,
    isLoadingDashboard,
    dashboardError,
    fetchDashboardSummary,
  } = useHospitalStore();

  const refreshDashboard = () => {
    fetchDashboardSummary();
  };

  return {
    dashboardSummary,
    isLoading: isLoadingDashboard,
    error: dashboardError,
    hasError: !!dashboardError,
    refreshDashboard,
    fetchDashboardSummary,
  };
}

/**
 * Hook for managing hospital patients data
 */
export function useHospitalPatients(query?: HospitalPatientsQuery) {
  const {
    patients,
    patientsTotal,
    patientsPage,
    patientsLimit,
    isLoadingPatients,
    patientsError,
    fetchPatients,
  } = useHospitalStore();

  const refreshPatients = () => {
    fetchPatients(query);
  };

  // Memoized patient statistics
  const patientStats = useMemo(() => {
    if (!patients.length) {
      return {
        totalPatients: 0,
        highRiskCount: 0,
        averageAdherence: 0,
        departmentCounts: {},
      };
    }

    const departmentCounts: Record<string, number> = {};
    
    patients.forEach((patient) => {
      const department = patient.assigned_doctor.specialization;
      departmentCounts[department] = (departmentCounts[department] || 0) + 1;
    });

    return {
      totalPatients: patients.length,
      highRiskCount: 0, // This would need to be calculated from adherence data
      averageAdherence: 0, // This would need to be calculated from adherence data
      departmentCounts,
    };
  }, [patients]);

  return {
    patients,
    total: patientsTotal,
    page: patientsPage,
    limit: patientsLimit,
    isLoading: isLoadingPatients,
    error: patientsError,
    hasError: !!patientsError,
    patientStats,
    refreshPatients,
    fetchPatients,
  };
}

/**
 * Hook for managing hospital doctors data
 */
export function useHospitalDoctors(query?: HospitalDoctorsQuery) {
  const {
    doctors,
    doctorsTotal,
    doctorsPage,
    doctorsLimit,
    isLoadingDoctors,
    doctorsError,
    fetchDoctors,
  } = useHospitalStore();

  const refreshDoctors = () => {
    fetchDoctors(query);
  };

  // Calculate doctor statistics
  const doctorStats = useMemo(() => {
    if (!doctors || doctors.length === 0) {
      return {
        totalDoctors: 0,
        activeDoctors: 0,
        averageRating: 0,
        specializationCounts: {},
      };
    }

    const specializationCounts: Record<string, number> = {};
    let totalRating = 0;
    let ratedDoctors = 0;
    let activeDoctors = 0;

    doctors.forEach((doctor) => {
      const specialization = doctor.specialization;
      specializationCounts[specialization] = (specializationCounts[specialization] || 0) + 1;

      if (doctor.status === 'active') {
        activeDoctors++;
      }

      if (doctor.rating && doctor.rating > 0) {
        totalRating += doctor.rating;
        ratedDoctors++;
      }
    });

    return {
      totalDoctors: doctors.length,
      activeDoctors,
      averageRating: ratedDoctors > 0 ? totalRating / ratedDoctors : 0,
      specializationCounts,
    };
  }, [doctors]);

  return {
    doctors,
    total: doctorsTotal,
    page: doctorsPage,
    limit: doctorsLimit,
    isLoading: isLoadingDoctors,
    error: doctorsError,
    hasError: !!doctorsError,
    doctorStats,
    refreshDoctors,
    fetchDoctors,
  };
}

/**
 * Hook for managing patient adherence reports
 */
export function usePatientAdherence(patientId?: string) {
  const {
    adherenceReports,
    isLoadingAdherence,
    adherenceError,
    fetchPatientAdherenceReport,
  } = useHospitalStore();

  const adherenceReport = patientId ? adherenceReports[patientId] : null;

  const fetchReport = (id: string, query?: any) => {
    fetchPatientAdherenceReport(id, query);
  };

  return {
    adherenceReport,
    adherenceReports,
    isLoading: isLoadingAdherence,
    error: adherenceError,
    hasError: !!adherenceError,
    fetchReport,
    fetchPatientAdherenceReport,
  };
}

/**
 * Hook for managing bulk adherence reports
 */
export function useBulkAdherence() {
  const {
    bulkAdherenceReport,
    isLoadingBulkAdherence,
    bulkAdherenceError,
    fetchBulkAdherenceReport,
  } = useHospitalStore();

  return {
    bulkAdherenceReport,
    isLoading: isLoadingBulkAdherence,
    error: bulkAdherenceError,
    hasError: !!bulkAdherenceError,
    fetchBulkAdherenceReport,
  };
}

/**
 * Hook for managing department analysis
 */
export function useDepartmentAnalysis(department?: string) {
  const {
    departmentAnalyses,
    isLoadingDepartmentAnalysis,
    departmentAnalysisError,
    fetchDepartmentAnalysis,
  } = useHospitalStore();

  const departmentAnalysis = department ? departmentAnalyses[department] : null;

  const fetchAnalysis = (dept: string, query?: any) => {
    fetchDepartmentAnalysis({ department: dept, ...query });
  };

  return {
    departmentAnalysis,
    departmentAnalyses,
    isLoading: isLoadingDepartmentAnalysis,
    error: departmentAnalysisError,
    hasError: !!departmentAnalysisError,
    fetchAnalysis,
    fetchDepartmentAnalysis,
  };
}

/**
 * Hook for managing patient summaries
 */
export function usePatientSummary(patientId?: string) {
  const {
    patientSummaries,
    isLoadingPatientSummary,
    patientSummaryError,
    fetchPatientSummary,
  } = useHospitalStore();

  const patientSummary = patientId ? patientSummaries[patientId] : null;

  const fetchSummary = (id: string) => {
    fetchPatientSummary(id);
  };

  return {
    patientSummary,
    patientSummaries,
    isLoading: isLoadingPatientSummary,
    error: patientSummaryError,
    hasError: !!patientSummaryError,
    fetchSummary,
    fetchPatientSummary,
  };
}

/**
 * Main hook that combines all hospital data management
 */
export function useHospitalData(options?: {
  autoFetchDashboard?: boolean;
  autoFetchPatients?: boolean;
  autoFetchDoctors?: boolean;
  patientsQuery?: HospitalPatientsQuery;
  doctorsQuery?: HospitalDoctorsQuery;
}) {
  const {
    autoFetchDashboard = false,
    autoFetchPatients = false,
    autoFetchDoctors = false,
    patientsQuery,
    doctorsQuery,
  } = options || {};

  const dashboard = useHospitalDashboard();
  const patients = useHospitalPatients(patientsQuery);
  const doctors = useHospitalDoctors(doctorsQuery);
  const adherence = usePatientAdherence();
  const bulkAdherence = useBulkAdherence();
  const departmentAnalysis = useDepartmentAnalysis();
  const patientSummary = usePatientSummary();

  const { clearErrors, reset } = useHospitalStore();

  // Auto-fetch data on mount if requested
  useEffect(() => {
    if (autoFetchDashboard && !dashboard.dashboardSummary && !dashboard.isLoading) {
      dashboard.fetchDashboardSummary();
    }
  }, [autoFetchDashboard, dashboard.dashboardSummary, dashboard.isLoading, dashboard.fetchDashboardSummary]);

  useEffect(() => {
    if (autoFetchPatients && !patients.patients.length && !patients.isLoading) {
      patients.fetchPatients(patientsQuery);
    }
  }, [autoFetchPatients, patients.patients.length, patients.isLoading, patients.fetchPatients, patientsQuery]);

  useEffect(() => {
    if (autoFetchDoctors && !doctors.doctors.length && !doctors.isLoading) {
      doctors.fetchDoctors(doctorsQuery);
    }
  }, [autoFetchDoctors, doctors.doctors.length, doctors.isLoading, doctors.fetchDoctors, doctorsQuery]);

  // Combined loading state
  const isLoading = dashboard.isLoading || patients.isLoading || doctors.isLoading;

  // Combined error state
  const hasError = dashboard.hasError || patients.hasError || doctors.hasError;
  const error = dashboard.error || patients.error || doctors.error;

  // Refresh all data
  const refreshAll = () => {
    dashboard.refreshDashboard();
    patients.refreshPatients();
    doctors.refreshDoctors();
  };

  return {
    // Dashboard
    dashboardSummary: dashboard.dashboardSummary,
    isDashboardLoading: dashboard.isLoading,
    dashboardError: dashboard.error,
    hasDashboardError: dashboard.hasError,
    refreshDashboard: dashboard.refreshDashboard,

    // Patients
    patients: patients.patients,
    patientsTotal: patients.total,
    patientsPage: patients.page,
    patientsLimit: patients.limit,
    isPatientsLoading: patients.isLoading,
    patientsError: patients.error,
    hasPatientsError: patients.hasError,
    patientStats: patients.patientStats,
    refreshPatients: patients.refreshPatients,

    // Doctors
    doctors: doctors.doctors,
    doctorsTotal: doctors.total,
    doctorsPage: doctors.page,
    doctorsLimit: doctors.limit,
    isDoctorsLoading: doctors.isLoading,
    doctorsError: doctors.error,
    hasDoctorsError: doctors.hasError,
    doctorStats: doctors.doctorStats,
    refreshDoctors: doctors.refreshDoctors,

    // Adherence
    adherenceReports: adherence.adherenceReports,
    isAdherenceLoading: adherence.isLoading,
    adherenceError: adherence.error,
    hasAdherenceError: adherence.hasError,
    fetchPatientAdherenceReport: adherence.fetchPatientAdherenceReport,

    // Bulk adherence
    bulkAdherenceReport: bulkAdherence.bulkAdherenceReport,
    isBulkAdherenceLoading: bulkAdherence.isLoading,
    bulkAdherenceError: bulkAdherence.error,
    hasBulkAdherenceError: bulkAdherence.hasError,
    fetchBulkAdherenceReport: bulkAdherence.fetchBulkAdherenceReport,

    // Department analysis
    departmentAnalyses: departmentAnalysis.departmentAnalyses,
    isDepartmentAnalysisLoading: departmentAnalysis.isLoading,
    departmentAnalysisError: departmentAnalysis.error,
    hasDepartmentAnalysisError: departmentAnalysis.hasError,
    fetchDepartmentAnalysis: departmentAnalysis.fetchDepartmentAnalysis,

    // Patient summaries
    patientSummaries: patientSummary.patientSummaries,
    isPatientSummaryLoading: patientSummary.isLoading,
    patientSummaryError: patientSummary.error,
    hasPatientSummaryError: patientSummary.hasError,
    fetchPatientSummary: patientSummary.fetchPatientSummary,

    // Combined states
    isLoading,
    hasError,
    error,

    // Actions
    refreshAll,
    clearErrors,
    reset,
  };
}
