import { apiClient } from '../api-client';
import type {
  ApiAdherenceRecord,
  CreateAdherenceRecordRequest,
  AdherenceStats,
  PaginatedResponse,
  AdherenceQueryParams,
} from '../../types/api';

export class AdherenceService {
  /**
   * Get adherence records for the current user
   */
  static async getAdherenceRecords(params?: AdherenceQueryParams): Promise<PaginatedResponse<ApiAdherenceRecord>> {
    return apiClient.get<PaginatedResponse<ApiAdherenceRecord>>('/adherence', { params });
  }

  /**
   * Get adherence records for a specific patient (for doctors/hospitals)
   */
  static async getPatientAdherenceRecords(
    patientId: string,
    params?: AdherenceQueryParams
  ): Promise<PaginatedResponse<ApiAdherenceRecord>> {
    return apiClient.get<PaginatedResponse<ApiAdherenceRecord>>(`/adherence/patient/${patientId}`, { params });
  }

  /**
   * Get a specific adherence record by ID
   */
  static async getAdherenceRecord(id: string): Promise<ApiAdherenceRecord> {
    return apiClient.get<ApiAdherenceRecord>(`/adherence/${id}`);
  }

  /**
   * Create a new adherence record
   */
  static async createAdherenceRecord(data: CreateAdherenceRecordRequest): Promise<ApiAdherenceRecord> {
    return apiClient.post<ApiAdherenceRecord>('/adherence', data, {
      showSuccessToast: true,
      successMessage: 'Adherence record created successfully',
    });
  }

  /**
   * Update an adherence record
   */
  static async updateAdherenceRecord(
    id: string,
    data: Partial<CreateAdherenceRecordRequest>
  ): Promise<ApiAdherenceRecord> {
    return apiClient.patch<ApiAdherenceRecord>(`/adherence/${id}`, data, {
      showSuccessToast: true,
      successMessage: 'Adherence record updated successfully',
    });
  }

  /**
   * Delete an adherence record
   */
  static async deleteAdherenceRecord(id: string): Promise<void> {
    await apiClient.delete(`/adherence/${id}`, {
      showSuccessToast: true,
      successMessage: 'Adherence record deleted successfully',
    });
  }

  /**
   * Get adherence statistics
   */
  static async getAdherenceStats(params?: {
    startDate?: string;
    endDate?: string;
    medicineId?: string;
    patientId?: string;
  }): Promise<AdherenceStats> {
    return apiClient.get<AdherenceStats>('/adherence/stats', { params });
  }

  /**
   * Get today's adherence summary
   */
  static async getTodaysAdherence(): Promise<{
    totalScheduled: number;
    taken: number;
    missed: number;
    skipped: number;
    pending: number;
    adherenceRate: number;
    medicines: Array<{
      id: string;
      name: string;
      dosage: string;
      scheduledTime: string;
      status: 'taken' | 'missed' | 'skipped' | 'pending';
      takenTime?: string;
    }>;
  }> {
    return apiClient.get('/adherence/today');
  }

  /**
   * Get weekly adherence summary
   */
  static async getWeeklyAdherence(): Promise<{
    weekStart: string;
    weekEnd: string;
    totalScheduled: number;
    taken: number;
    missed: number;
    skipped: number;
    adherenceRate: number;
    dailyStats: Array<{
      date: string;
      scheduled: number;
      taken: number;
      missed: number;
      skipped: number;
      adherenceRate: number;
    }>;
  }> {
    return apiClient.get('/adherence/weekly');
  }

  /**
   * Get monthly adherence summary
   */
  static async getMonthlyAdherence(month?: string, year?: number): Promise<{
    month: string;
    year: number;
    totalScheduled: number;
    taken: number;
    missed: number;
    skipped: number;
    adherenceRate: number;
    weeklyStats: Array<{
      weekStart: string;
      weekEnd: string;
      scheduled: number;
      taken: number;
      missed: number;
      skipped: number;
      adherenceRate: number;
    }>;
  }> {
    return apiClient.get('/adherence/monthly', {
      params: { month, year },
    });
  }

  /**
   * Get adherence trends
   */
  static async getAdherenceTrends(params?: {
    period: 'week' | 'month' | 'quarter' | 'year';
    medicineId?: string;
    patientId?: string;
  }): Promise<{
    period: string;
    trends: Array<{
      date: string;
      adherenceRate: number;
      taken: number;
      missed: number;
      skipped: number;
    }>;
    averageAdherence: number;
    improvement: number;
    insights: string[];
  }> {
    return apiClient.get('/adherence/trends', { params });
  }

  /**
   * Get adherence insights and recommendations
   */
  static async getAdherenceInsights(params?: {
    patientId?: string;
    medicineId?: string;
    days?: number;
  }): Promise<{
    overallScore: number;
    riskLevel: 'low' | 'medium' | 'high';
    insights: Array<{
      type: 'positive' | 'warning' | 'critical';
      title: string;
      description: string;
      recommendation?: string;
    }>;
    patterns: Array<{
      pattern: string;
      frequency: number;
      impact: 'low' | 'medium' | 'high';
    }>;
    recommendations: Array<{
      category: 'timing' | 'reminders' | 'support' | 'medication';
      title: string;
      description: string;
      priority: 'low' | 'medium' | 'high';
    }>;
  }> {
    return apiClient.get('/adherence/insights', { params });
  }

  /**
   * Record medicine intake
   */
  static async recordIntake(data: {
    medicineId: string;
    scheduledTime: string;
    takenTime?: string;
    notes?: string;
  }): Promise<ApiAdherenceRecord> {
    return apiClient.post<ApiAdherenceRecord>('/adherence/intake', data, {
      showSuccessToast: true,
      successMessage: 'Medicine intake recorded successfully',
    });
  }

  /**
   * Record missed dose
   */
  static async recordMissedDose(data: {
    medicineId: string;
    scheduledTime: string;
    reason?: string;
    notes?: string;
  }): Promise<ApiAdherenceRecord> {
    return apiClient.post<ApiAdherenceRecord>('/adherence/missed', data, {
      showSuccessToast: true,
      successMessage: 'Missed dose recorded',
    });
  }

  /**
   * Record skipped dose
   */
  static async recordSkippedDose(data: {
    medicineId: string;
    scheduledTime: string;
    reason?: string;
    notes?: string;
  }): Promise<ApiAdherenceRecord> {
    return apiClient.post<ApiAdherenceRecord>('/adherence/skipped', data, {
      showSuccessToast: true,
      successMessage: 'Skipped dose recorded',
    });
  }

  /**
   * Get adherence calendar data
   */
  static async getAdherenceCalendar(params?: {
    month: number;
    year: number;
    medicineId?: string;
  }): Promise<{
    month: number;
    year: number;
    days: Array<{
      date: string;
      scheduled: number;
      taken: number;
      missed: number;
      skipped: number;
      adherenceRate: number;
      status: 'excellent' | 'good' | 'fair' | 'poor';
    }>;
  }> {
    return apiClient.get('/adherence/calendar', { params });
  }

  /**
   * Export adherence data
   */
  static async exportAdherenceData(params?: {
    format: 'csv' | 'pdf' | 'json';
    startDate?: string;
    endDate?: string;
    medicineId?: string;
    includeInsights?: boolean;
  }): Promise<Blob> {
    const response = await fetch(`${apiClient['baseURL']}/adherence/export`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${localStorage.getItem('auth-token')}`,
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error('Failed to export adherence data');
    }

    return response.blob();
  }

  /**
   * Get adherence comparison with peers
   */
  static async getAdherenceComparison(params?: {
    ageGroup?: string;
    condition?: string;
    medicationType?: string;
  }): Promise<{
    userAdherence: number;
    peerAverage: number;
    percentile: number;
    comparison: 'above' | 'below' | 'average';
    insights: string[];
  }> {
    return apiClient.get('/adherence/comparison', { params });
  }

  /**
   * Set adherence goals
   */
  static async setAdherenceGoals(data: {
    targetAdherenceRate: number;
    goalPeriod: 'weekly' | 'monthly' | 'quarterly';
    medicineIds?: string[];
    reminderPreferences?: {
      enableGoalReminders: boolean;
      reminderFrequency: 'daily' | 'weekly';
    };
  }): Promise<{
    goalId: string;
    targetRate: number;
    currentRate: number;
    progress: number;
    estimatedCompletion: string;
  }> {
    return apiClient.post('/adherence/goals', data, {
      showSuccessToast: true,
      successMessage: 'Adherence goals set successfully',
    });
  }

  /**
   * Get adherence goals progress
   */
  static async getAdherenceGoalsProgress(): Promise<Array<{
    goalId: string;
    targetRate: number;
    currentRate: number;
    progress: number;
    status: 'on-track' | 'behind' | 'ahead' | 'completed';
    daysRemaining: number;
    estimatedCompletion: string;
  }>> {
    return apiClient.get('/adherence/goals/progress');
  }
}

// Export both the class and an instance
export const adherenceService = AdherenceService;
export default AdherenceService;
