import { apiClient, TokenManager } from '../api-client';
import type {
  LoginRequest,
  RegisterRequest,
  AuthResponse,
  RefreshTokenRequest,
  ApiUser,
} from '../../types/api';

export class AuthService {
  /**
   * Login user with email and password
   */
  static async login(credentials: LoginRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/login', credentials, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Login successful!',
    });

    // Store tokens
    TokenManager.setToken(response.accessToken);
    if (response.refreshToken) {
      TokenManager.setRefreshToken(response.refreshToken);
    }

    return response;
  }

  /**
   * Register new user
   */
  static async register(userData: RegisterRequest): Promise<AuthResponse> {
    const response = await apiClient.post<AuthResponse>('/auth/register', userData, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Registration successful!',
    });

    // Store tokens
    TokenManager.setToken(response.accessToken);
    if (response.refreshToken) {
      TokenManager.setRefreshToken(response.refreshToken);
    }

    return response;
  }

  /**
   * Logout user
   */
  static async logout(): Promise<void> {
    try {
      await apiClient.post('/auth/logout', {}, {
        showSuccessToast: true,
        successMessage: 'Logged out successfully',
      });
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', error);
    } finally {
      // Always clear tokens
      TokenManager.clearTokens();
    }
  }

  /**
   * Refresh access token
   */
  static async refreshToken(): Promise<AuthResponse> {
    const refreshToken = TokenManager.getRefreshToken();
    if (!refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await apiClient.post<AuthResponse>('/auth/refresh', {
      refreshToken,
    }, {
      requireAuth: false,
    });

    // Update stored tokens
    TokenManager.setToken(response.accessToken);
    if (response.refreshToken) {
      TokenManager.setRefreshToken(response.refreshToken);
    }

    return response;
  }

  /**
   * Get current user profile
   */
  static async getCurrentUser(): Promise<ApiUser> {
    return apiClient.get<ApiUser>('/auth/me');
  }

  /**
   * Update user profile
   */
  static async updateProfile(updates: Partial<ApiUser>): Promise<ApiUser> {
    return apiClient.patch<ApiUser>('/auth/profile', updates, {
      showSuccessToast: true,
      successMessage: 'Profile updated successfully',
    });
  }

  /**
   * Change password
   */
  static async changePassword(data: {
    currentPassword: string;
    newPassword: string;
  }): Promise<void> {
    await apiClient.post('/auth/change-password', data, {
      showSuccessToast: true,
      successMessage: 'Password changed successfully',
    });
  }

  /**
   * Request password reset
   */
  static async requestPasswordReset(email: string): Promise<void> {
    await apiClient.post('/auth/forgot-password', { email }, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Password reset email sent',
    });
  }

  /**
   * Reset password with token
   */
  static async resetPassword(data: {
    token: string;
    newPassword: string;
  }): Promise<void> {
    await apiClient.post('/auth/reset-password', data, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Password reset successfully',
    });
  }

  /**
   * Verify email address
   */
  static async verifyEmail(token: string): Promise<void> {
    await apiClient.post('/auth/verify-email', { token }, {
      requireAuth: false,
      showSuccessToast: true,
      successMessage: 'Email verified successfully',
    });
  }

  /**
   * Resend email verification
   */
  static async resendEmailVerification(): Promise<void> {
    await apiClient.post('/auth/resend-verification', {}, {
      showSuccessToast: true,
      successMessage: 'Verification email sent',
    });
  }

  /**
   * Check if user is authenticated
   */
  static isAuthenticated(): boolean {
    const token = TokenManager.getToken();
    if (!token) return false;
    
    return !TokenManager.isTokenExpired(token);
  }

  /**
   * Get user role from token
   */
  static getUserRole(): string | null {
    const token = TokenManager.getToken();
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.role || null;
    } catch {
      return null;
    }
  }

  /**
   * Get user ID from token
   */
  static getUserId(): string | null {
    const token = TokenManager.getToken();
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.sub || payload.userId || null;
    } catch {
      return null;
    }
  }

  /**
   * Check if user has specific role
   */
  static hasRole(role: string): boolean {
    const userRole = this.getUserRole();
    return userRole === role;
  }

  /**
   * Check if user has any of the specified roles
   */
  static hasAnyRole(roles: string[]): boolean {
    const userRole = this.getUserRole();
    return userRole ? roles.includes(userRole) : false;
  }
}

export default AuthService;
