import { apiClient } from '../api-client';
import type {
  ApiUser,
  UserProfile,
  PaginatedResponse,
  SearchParams,
  AdherenceQueryParams,
  CreatePrescriptionRequest,
  ApiPrescription,
  ApiMedicine,
  ApiAdherenceRecord,
  AdherenceStats,
} from '../../types/api';

// Doctor-specific types
export interface DoctorProfile extends UserProfile {
  specialization: string;
  licenseNumber: string;
  hospitalId?: string;
  hospital?: {
    id: string;
    name: string;
    email: string;
  };
}

export interface DoctorPatient extends ApiUser {
  dateOfBirth?: string;
  gender?: string;
  phone?: string;
  address?: string;
  emergencyContact?: string;
  condition?: string;
  adherenceRate: number;
  lastVisit?: string;
  nextAppointment?: string;
  status: 'excellent' | 'good' | 'needs-attention' | 'critical';
  riskLevel: 'low' | 'medium' | 'high';
  medications: string[];
  notes?: string;
}

export interface DoctorDashboardStats {
  totalPatients: number;
  activePatients: number;
  averageAdherence: number;
  criticalPatients: number;
  todayAppointments: number;
  pendingPrescriptions: number;
  monthlyTrend: number;
}

export interface DoctorAnalytics {
  patientAdherence: Array<{
    patientId: string;
    patientName: string;
    adherenceRate: number;
    trend: 'up' | 'down' | 'stable';
  }>;
  medicationEffectiveness: Array<{
    medicationName: string;
    adherenceRate: number;
    patientCount: number;
  }>;
  departmentComparison?: {
    myPerformance: number;
    departmentAverage: number;
    hospitalAverage: number;
  };
}

export class DoctorService {
  /**
   * Get doctor profile details
   */
  static async getDoctorProfile(doctorId?: string): Promise<DoctorProfile> {
    const endpoint = doctorId ? `/users/${doctorId}/doctor-details` : '/auth/profile';
    return apiClient.get<DoctorProfile>(endpoint);
  }

  /**
   * Update doctor profile
   */
  static async updateDoctorProfile(
    doctorId: string,
    data: Partial<DoctorProfile>
  ): Promise<DoctorProfile> {
    return apiClient.patch<DoctorProfile>(`/users/${doctorId}/doctor`, data, {
      showSuccessToast: true,
      successMessage: 'Profile updated successfully',
    });
  }

  /**
   * Get all patients assigned to the doctor
   */
  static async getDoctorPatients(params?: SearchParams): Promise<PaginatedResponse<DoctorPatient>> {
    // This will filter patients based on the authenticated doctor
    return apiClient.get<PaginatedResponse<DoctorPatient>>('/users', { 
      params: { ...params, role: 'patient' } 
    });
  }

  /**
   * Get specific patient details
   */
  static async getPatientDetails(patientId: string): Promise<DoctorPatient> {
    return apiClient.get<DoctorPatient>(`/users/${patientId}/patient-details`);
  }

  /**
   * Get patient's medicines
   */
  static async getPatientMedicines(
    patientId: string,
    params?: SearchParams
  ): Promise<PaginatedResponse<ApiMedicine>> {
    return apiClient.get<PaginatedResponse<ApiMedicine>>(`/medicines/patient/${patientId}`, { params });
  }

  /**
   * Get patient's adherence records
   */
  static async getPatientAdherence(
    patientId: string,
    params?: AdherenceQueryParams
  ): Promise<PaginatedResponse<ApiAdherenceRecord>> {
    return apiClient.get<PaginatedResponse<ApiAdherenceRecord>>('/adherence', {
      params: { ...params, patientId },
    });
  }

  /**
   * Get patient adherence analytics
   */
  static async getPatientAdherenceAnalytics(
    patientId: string,
    days: number = 30
  ): Promise<AdherenceStats> {
    return apiClient.get<AdherenceStats>(`/adherence/analytics/${patientId}`, {
      params: { days },
    });
  }

  /**
   * Get doctor's prescriptions
   */
  static async getDoctorPrescriptions(
    doctorId?: string,
    params?: SearchParams
  ): Promise<PaginatedResponse<ApiPrescription>> {
    if (doctorId) {
      return apiClient.get<PaginatedResponse<ApiPrescription>>(`/prescriptions/doctor/${doctorId}`, { params });
    }
    return apiClient.get<PaginatedResponse<ApiPrescription>>('/prescriptions', { params });
  }

  /**
   * Create new prescription
   */
  static async createPrescription(data: CreatePrescriptionRequest): Promise<ApiPrescription> {
    return apiClient.post<ApiPrescription>('/prescriptions', data, {
      showSuccessToast: true,
      successMessage: 'Prescription created successfully',
    });
  }

  /**
   * Upload prescription file
   */
  static async uploadPrescription(
    file: File,
    patientId: string,
    doctorId?: string
  ): Promise<ApiPrescription> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('patient_id', patientId);
    if (doctorId) {
      formData.append('doctor_id', doctorId);
    }

    return apiClient.post<ApiPrescription>('/prescriptions/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      showSuccessToast: true,
      successMessage: 'Prescription uploaded successfully',
    });
  }

  /**
   * Get doctor dashboard statistics
   */
  static async getDashboardStats(): Promise<DoctorDashboardStats> {
    // This would be a composite call or a dedicated endpoint
    // For now, we'll simulate it by making multiple calls
    const [patients, prescriptions] = await Promise.all([
      DoctorService.getDoctorPatients({ limit: 1000 }),
      DoctorService.getDoctorPrescriptions(undefined, { limit: 1000 }),
    ]);

    const totalPatients = patients.meta.total;
    const activePatients = patients.data.filter(p => p.status !== 'critical').length;
    const criticalPatients = patients.data.filter(p => p.status === 'critical').length;
    const averageAdherence = patients.data.reduce((sum, p) => sum + p.adherenceRate, 0) / totalPatients || 0;

    return {
      totalPatients,
      activePatients,
      averageAdherence: Math.round(averageAdherence),
      criticalPatients,
      todayAppointments: 0, // Would need appointment system
      pendingPrescriptions: prescriptions.data.filter(p => p.status === 'processing').length,
      monthlyTrend: 5, // Mock trend
    };
  }

  /**
   * Get doctor analytics
   */
  static async getDoctorAnalytics(): Promise<DoctorAnalytics> {
    const patients = await DoctorService.getDoctorPatients({ limit: 1000 });
    
    const patientAdherence = patients.data.map(patient => ({
      patientId: patient.id,
      patientName: patient.name,
      adherenceRate: patient.adherenceRate,
      trend: 'stable' as const, // Would calculate from historical data
    }));

    // Mock medication effectiveness data
    const medicationEffectiveness = [
      { medicationName: 'Metformin', adherenceRate: 85, patientCount: 12 },
      { medicationName: 'Lisinopril', adherenceRate: 78, patientCount: 8 },
      { medicationName: 'Atorvastatin', adherenceRate: 92, patientCount: 15 },
    ];

    return {
      patientAdherence,
      medicationEffectiveness,
      departmentComparison: {
        myPerformance: Math.round(patientAdherence.reduce((sum, p) => sum + p.adherenceRate, 0) / patientAdherence.length),
        departmentAverage: 82,
        hospitalAverage: 79,
      },
    };
  }

  /**
   * Create new patient (for doctors)
   */
  static async createPatient(data: {
    name: string;
    email: string;
    dateOfBirth?: string;
    phone?: string;
    address?: string;
    emergencyContact?: string;
  }): Promise<DoctorPatient> {
    return apiClient.post<DoctorPatient>('/users/patients', {
      ...data,
      role: 'patient',
    }, {
      showSuccessToast: true,
      successMessage: 'Patient created successfully',
    });
  }

  /**
   * Update patient information
   */
  static async updatePatient(
    patientId: string,
    data: Partial<DoctorPatient>
  ): Promise<DoctorPatient> {
    return apiClient.patch<DoctorPatient>(`/users/${patientId}/patient`, data, {
      showSuccessToast: true,
      successMessage: 'Patient updated successfully',
    });
  }
}

export default DoctorService;
