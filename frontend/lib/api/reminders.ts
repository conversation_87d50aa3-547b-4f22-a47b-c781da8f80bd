import { apiClient } from '../api-client';
import type {
  ApiReminder,
  CreateReminderRequest,
  UpdateReminderRequest,
  PaginatedResponse,
  ReminderQueryParams,
} from '../../types/api';

export class ReminderService {
  /**
   * Get all reminders for the current user
   */
  static async getReminders(params?: ReminderQueryParams): Promise<PaginatedResponse<ApiReminder>> {
    return apiClient.get<PaginatedResponse<ApiReminder>>('/reminders', { params });
  }

  /**
   * Get reminders for a specific patient (for doctors/hospitals)
   */
  static async getPatientReminders(
    patientId: string,
    params?: ReminderQueryParams
  ): Promise<PaginatedResponse<ApiReminder>> {
    return apiClient.get<PaginatedResponse<ApiReminder>>(`/reminders/patient/${patientId}`, { params });
  }

  /**
   * Get a specific reminder by ID
   */
  static async getReminder(id: string): Promise<ApiReminder> {
    return apiClient.get<ApiReminder>(`/reminders/${id}`);
  }

  /**
   * Create a new reminder
   */
  static async createReminder(data: CreateReminderRequest): Promise<ApiReminder> {
    return apiClient.post<ApiReminder>('/reminders', data, {
      showSuccessToast: true,
      successMessage: 'Reminder created successfully',
    });
  }

  /**
   * Update a reminder
   */
  static async updateReminder(
    id: string,
    data: UpdateReminderRequest
  ): Promise<ApiReminder> {
    return apiClient.patch<ApiReminder>(`/reminders/${id}`, data, {
      showSuccessToast: true,
      successMessage: 'Reminder updated successfully',
    });
  }

  /**
   * Delete a reminder
   */
  static async deleteReminder(id: string): Promise<void> {
    await apiClient.delete(`/reminders/${id}`, {
      showSuccessToast: true,
      successMessage: 'Reminder deleted successfully',
    });
  }

  /**
   * Get today's reminders
   */
  static async getTodaysReminders(): Promise<ApiReminder[]> {
    return apiClient.get<ApiReminder[]>('/reminders/today');
  }

  /**
   * Get upcoming reminders
   */
  static async getUpcomingReminders(hours: number = 24): Promise<ApiReminder[]> {
    return apiClient.get<ApiReminder[]>('/reminders/upcoming', {
      params: { hours },
    });
  }

  /**
   * Get overdue reminders
   */
  static async getOverdueReminders(): Promise<ApiReminder[]> {
    return apiClient.get<ApiReminder[]>('/reminders/overdue');
  }

  /**
   * Mark reminder as completed
   */
  static async completeReminder(
    id: string,
    data?: {
      completedAt?: string;
      notes?: string;
    }
  ): Promise<ApiReminder> {
    return apiClient.post<ApiReminder>(`/reminders/${id}/complete`, data, {
      showSuccessToast: true,
      successMessage: 'Reminder marked as completed',
    });
  }

  /**
   * Mark reminder as missed
   */
  static async missReminder(
    id: string,
    data?: {
      reason?: string;
      notes?: string;
    }
  ): Promise<ApiReminder> {
    return apiClient.post<ApiReminder>(`/reminders/${id}/miss`, data, {
      showSuccessToast: true,
      successMessage: 'Reminder marked as missed',
    });
  }

  /**
   * Snooze a reminder
   */
  static async snoozeReminder(
    id: string,
    data: {
      snoozeUntil: string;
      reason?: string;
    }
  ): Promise<ApiReminder> {
    return apiClient.post<ApiReminder>(`/reminders/${id}/snooze`, data, {
      showSuccessToast: true,
      successMessage: 'Reminder snoozed successfully',
    });
  }

  /**
   * Dismiss a reminder
   */
  static async dismissReminder(id: string): Promise<void> {
    await apiClient.post(`/reminders/${id}/dismiss`, {}, {
      showSuccessToast: true,
      successMessage: 'Reminder dismissed',
    });
  }

  /**
   * Get reminder statistics
   */
  static async getReminderStats(params?: {
    startDate?: string;
    endDate?: string;
    patientId?: string;
  }): Promise<{
    totalReminders: number;
    completedCount: number;
    missedCount: number;
    snoozedCount: number;
    pendingCount: number;
    completionRate: number;
    weeklyStats: Array<{
      date: string;
      completed: number;
      missed: number;
      snoozed: number;
    }>;
    reminderTypeStats: Array<{
      type: 'notification' | 'call' | 'sms';
      count: number;
      completionRate: number;
    }>;
  }> {
    return apiClient.get('/reminders/stats', { params });
  }

  /**
   * Test reminder delivery
   */
  static async testReminder(
    id: string,
    type: 'notification' | 'call' | 'sms'
  ): Promise<{
    success: boolean;
    message: string;
    deliveredAt: string;
  }> {
    return apiClient.post(`/reminders/${id}/test`, { type }, {
      showSuccessToast: true,
      successMessage: 'Test reminder sent successfully',
    });
  }

  /**
   * Bulk update reminders
   */
  static async bulkUpdateReminders(
    reminderIds: string[],
    updates: UpdateReminderRequest
  ): Promise<{
    updated: number;
    failed: number;
    errors: Array<{
      id: string;
      error: string;
    }>;
  }> {
    return apiClient.patch('/reminders/bulk', {
      reminderIds,
      updates,
    }, {
      showSuccessToast: true,
      successMessage: 'Reminders updated successfully',
    });
  }

  /**
   * Get reminder preferences
   */
  static async getReminderPreferences(): Promise<{
    defaultReminderType: 'notification' | 'call' | 'sms';
    enableSnooze: boolean;
    defaultSnoozeDuration: number;
    quietHours: {
      enabled: boolean;
      startTime: string;
      endTime: string;
    };
    reminderTones: {
      notification: string;
      call: string;
      sms: boolean;
    };
  }> {
    return apiClient.get('/reminders/preferences');
  }

  /**
   * Update reminder preferences
   */
  static async updateReminderPreferences(preferences: {
    defaultReminderType?: 'notification' | 'call' | 'sms';
    enableSnooze?: boolean;
    defaultSnoozeDuration?: number;
    quietHours?: {
      enabled: boolean;
      startTime: string;
      endTime: string;
    };
    reminderTones?: {
      notification: string;
      call: string;
      sms: boolean;
    };
  }): Promise<void> {
    await apiClient.patch('/reminders/preferences', preferences, {
      showSuccessToast: true,
      successMessage: 'Reminder preferences updated',
    });
  }

  /**
   * Get reminder history
   */
  static async getReminderHistory(params?: {
    startDate?: string;
    endDate?: string;
    medicineId?: string;
    status?: 'completed' | 'missed' | 'snoozed';
    page?: number;
    limit?: number;
  }): Promise<PaginatedResponse<ApiReminder & {
    medicine: {
      id: string;
      name: string;
      dosage: string;
    };
  }>> {
    return apiClient.get('/reminders/history', { params });
  }

  /**
   * Schedule recurring reminders
   */
  static async scheduleRecurringReminders(data: {
    medicineId: string;
    reminderType: 'notification' | 'call' | 'sms';
    schedule: {
      frequency: 'daily' | 'weekly' | 'monthly';
      times: string[];
      daysOfWeek?: number[];
      startDate: string;
      endDate?: string;
    };
  }): Promise<{
    created: number;
    reminders: ApiReminder[];
  }> {
    return apiClient.post('/reminders/recurring', data, {
      showSuccessToast: true,
      successMessage: 'Recurring reminders scheduled successfully',
    });
  }
}

export default ReminderService;
