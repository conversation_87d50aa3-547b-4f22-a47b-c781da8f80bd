import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient } from '@/lib/api-client';
import { authService } from '@/lib/api/auth';
import { medicinesService } from '@/lib/api/medicines';
import { gamificationService } from '@/lib/api/gamification';
import { remindersService } from '@/lib/api/reminders';
import { adherenceService } from '@/lib/api/adherence';
import type {
  User,
  Medicine,
  Prescription,
  Reminder,
  AdherenceRecord,
  GamificationStats,
  UserRole
} from '@/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  updateUser: (updates: Partial<User>) => void;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

interface MedicineState {
  medicines: Medicine[];
  prescriptions: Prescription[];
  reminders: Reminder[];
  adherenceRecords: AdherenceRecord[];
  isLoading: boolean;
  error: string | null;

  // API methods
  fetchMedicines: () => Promise<void>;
  fetchPrescriptions: () => Promise<void>;
  fetchReminders: () => Promise<void>;
  fetchAdherenceRecords: () => Promise<void>;

  // Local methods
  addMedicine: (medicine: Medicine) => void;
  updateMedicine: (id: string, updates: Partial<Medicine>) => void;
  deleteMedicine: (id: string) => void;
  addPrescription: (prescription: Prescription) => void;
  updatePrescription: (id: string, updates: Partial<Prescription>) => void;
  addReminder: (reminder: Reminder) => void;
  updateReminder: (id: string, updates: Partial<Reminder>) => void;
  recordAdherence: (record: AdherenceRecord) => void;

  // Computed methods
  getTodaysReminders: () => Reminder[];
  getUpcomingReminders: () => Reminder[];
  getAdherenceRate: (days: number) => number;
  clearError: () => void;
}

interface GamificationState {
  stats: GamificationStats;
  isLoading: boolean;
  error: string | null;

  // API methods
  fetchStats: () => Promise<void>;

  // Local methods
  updateStats: (updates: Partial<GamificationStats>) => void;
  addPoints: (points: number) => void;
  incrementStreak: () => void;
  resetStreak: () => void;
  updateProgress: (taken: boolean) => void;
  clearError: () => void;
}

interface AppState {
  theme: 'light' | 'dark';
  sidebarCollapsed: boolean;
  notifications: Array<{
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    timestamp: Date;
    read: boolean;
  }>;
  toggleTheme: () => void;
  toggleSidebar: () => void;
  addNotification: (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp'>) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          const response = await authService.login({ email, password });
          set({
            user: response.user,
            isAuthenticated: true,
            isLoading: false
          });

          // Redirect based on user role
          if (typeof window !== 'undefined') {
            const userRole = response.user.role;
            window.location.href = `/dashboard/${userRole}`;
          }
        } catch (error: any) {
          set({
            error: error.message || 'Login failed',
            isLoading: false
          });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        try {
          await authService.logout();
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
            error: null
          });
        } catch (error: any) {
          set({
            error: error.message || 'Logout failed',
            isLoading: false
          });
        }
      },

      checkAuth: async () => {
        set({ isLoading: true });
        try {
          const user = await authService.getCurrentUser();
          set({
            user,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      },

      updateUser: (updates) => set((state) => ({
        user: state.user ? { ...state.user, ...updates } : null,
      })),

      clearError: () => set({ error: null }),
    }),
    {
      name: 'auth-storage',
    }
  )
);

export const useMedicineStore = create<MedicineState>()(
  persist(
    (set, get) => ({
      medicines: [],
      prescriptions: [],
      reminders: [],
      adherenceRecords: [],
      isLoading: false,
      error: null,

      // API methods
      fetchMedicines: async () => {
        set({ isLoading: true, error: null });
        try {
          const medicines = await medicinesService.getAll();
          set({ medicines, isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch medicines', isLoading: false });
        }
      },

      fetchPrescriptions: async () => {
        set({ isLoading: true, error: null });
        try {
          // Note: This will be implemented when prescriptions service is ready
          set({ isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch prescriptions', isLoading: false });
        }
      },

      fetchReminders: async () => {
        set({ isLoading: true, error: null });
        try {
          const reminders = await remindersService.getAll();
          set({ reminders, isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch reminders', isLoading: false });
        }
      },

      fetchAdherenceRecords: async () => {
        set({ isLoading: true, error: null });
        try {
          const adherenceRecords = await adherenceService.getRecords();
          set({ adherenceRecords, isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch adherence records', isLoading: false });
        }
      },

      // Local methods
      addMedicine: (medicine) => set((state) => ({
        medicines: [...state.medicines, medicine],
      })),

      updateMedicine: (id, updates) => set((state) => ({
        medicines: state.medicines.map((m) =>
          m.id === id ? { ...m, ...updates } : m
        ),
      })),

      deleteMedicine: (id) => set((state) => ({
        medicines: state.medicines.filter((m) => m.id !== id),
      })),
      
      addPrescription: (prescription) => set((state) => ({
        prescriptions: [...state.prescriptions, prescription],
      })),
      
      updatePrescription: (id, updates) => set((state) => ({
        prescriptions: state.prescriptions.map((p) => 
          p.id === id ? { ...p, ...updates } : p
        ),
      })),
      
      addReminder: (reminder) => set((state) => ({
        reminders: [...state.reminders, reminder],
      })),
      
      updateReminder: (id, updates) => set((state) => ({
        reminders: state.reminders.map((r) => 
          r.id === id ? { ...r, ...updates } : r
        ),
      })),
      
      recordAdherence: (record) => set((state) => ({
        adherenceRecords: [...state.adherenceRecords, record],
      })),
      
      getTodaysReminders: () => {
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        return get().reminders.filter((reminder) => 
          reminder.scheduledTime >= today && reminder.scheduledTime < tomorrow
        );
      },
      
      getUpcomingReminders: () => {
        const now = new Date();
        return get().reminders
          .filter((reminder) => reminder.scheduledTime > now && reminder.status === 'pending')
          .sort((a, b) => a.scheduledTime.getTime() - b.scheduledTime.getTime())
          .slice(0, 5);
      },
      
      getAdherenceRate: (days) => {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);

        const records = get().adherenceRecords.filter((record) =>
          record.scheduledTime >= cutoffDate
        );

        if (records.length === 0) return 0;

        const takenCount = records.filter((record) => record.status === 'taken').length;
        return Math.round((takenCount / records.length) * 100);
      },

      clearError: () => set({ error: null }),
    }),
    {
      name: 'medicine-storage',
    }
  )
);

export const useGamificationStore = create<GamificationState>()(
  persist(
    (set, get) => ({
      stats: {
        currentStreak: 0,
        longestStreak: 0,
        totalPoints: 0,
        completionRate: 0,
        achievements: [],
        weeklyProgress: Array(7).fill(0),
        monthlyProgress: Array(30).fill(0),
      },
      isLoading: false,
      error: null,

      // API methods
      fetchStats: async () => {
        set({ isLoading: true, error: null });
        try {
          const stats = await gamificationService.getStats();
          set({ stats, isLoading: false });
        } catch (error: any) {
          set({ error: error.message || 'Failed to fetch gamification stats', isLoading: false });
        }
      },

      // Local methods
      updateStats: (updates) => set((state) => ({
        stats: { ...state.stats, ...updates },
      })),
      
      addPoints: (points) => set((state) => ({
        stats: { ...state.stats, totalPoints: state.stats.totalPoints + points },
      })),
      
      incrementStreak: () => set((state) => ({
        stats: {
          ...state.stats,
          currentStreak: state.stats.currentStreak + 1,
          longestStreak: Math.max(state.stats.longestStreak, state.stats.currentStreak + 1),
        },
      })),
      
      resetStreak: () => set((state) => ({
        stats: { ...state.stats, currentStreak: 0 },
      })),
      
      updateProgress: (taken) => set((state) => {
        const today = new Date().getDay();
        const newWeeklyProgress = [...state.stats.weeklyProgress];
        newWeeklyProgress[today] = taken ? 100 : 0;

        return {
          stats: {
            ...state.stats,
            weeklyProgress: newWeeklyProgress,
          },
        };
      }),

      clearError: () => set({ error: null }),
    }),
    {
      name: 'gamification-storage',
    }
  )
);

export const useAppStore = create<AppState>()(
  persist(
    (set) => ({
      theme: 'light',
      sidebarCollapsed: false,
      notifications: [],
      
      toggleTheme: () => set((state) => ({
        theme: state.theme === 'light' ? 'dark' : 'light',
      })),
      
      toggleSidebar: () => set((state) => ({
        sidebarCollapsed: !state.sidebarCollapsed,
      })),
      
      addNotification: (notification) => set((state) => ({
        notifications: [
          {
            ...notification,
            id: Math.random().toString(36).substr(2, 9),
            timestamp: new Date(),
            read: false,
          },
          ...state.notifications,
        ],
      })),
      
      markNotificationRead: (id) => set((state) => ({
        notifications: state.notifications.map((n) =>
          n.id === id ? { ...n, read: true } : n
        ),
      })),
      
      clearNotifications: () => set({ notifications: [] }),
    }),
    {
      name: 'app-storage',
      storage: {
        getItem: (name) => {
          const str = localStorage.getItem(name);
          if (!str) return null;
          
          try {
            const parsed = JSON.parse(str);
            // Convert timestamp strings back to Date objects
            if (parsed.state?.notifications) {
              parsed.state.notifications = parsed.state.notifications.map((notification: any) => ({
                ...notification,
                timestamp: new Date(notification.timestamp),
              }));
            }
            return parsed;
          } catch (error) {
            console.error('Error parsing stored data:', error);
            return null;
          }
        },
        setItem: (name, value) => {
          localStorage.setItem(name, JSON.stringify(value));
        },
        removeItem: (name) => {
          localStorage.removeItem(name);
        },
      },
    }
  )
);