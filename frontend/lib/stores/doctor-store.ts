import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Doctor<PERSON><PERSON>boardStats, DoctorAnalytics } from '../api/doctors';
import type { ApiPrescription, ApiMedicine, ApiAdherenceRecord, AdherenceStats } from '../../types/api';

interface DoctorState {
  // Profile data
  profile: Doctor<PERSON><PERSON><PERSON><PERSON> | null;
  
  // Patients data
  patients: DoctorPatient[];
  selectedPatient: DoctorPatient | null;
  
  // Dashboard data
  dashboardStats: DoctorDashboardStats | null;
  analytics: DoctorAnalytics | null;
  
  // Prescriptions data
  prescriptions: ApiPrescription[];
  
  // Patient-specific data (for selected patient)
  patientMedicines: ApiMedicine[];
  patientAdherence: ApiAdherenceRecord[];
  patientAdherenceStats: AdherenceStats | null;
  
  // Loading states
  isLoading: boolean;
  isLoadingProfile: boolean;
  isLoadingPatients: boolean;
  isLoadingDashboard: boolean;
  isLoadingAnalytics: boolean;
  isLoadingPrescriptions: boolean;
  isLoadingPatientData: boolean;
  
  // Error states
  error: string | null;
  profileError: string | null;
  patientsError: string | null;
  dashboardError: string | null;
  analyticsError: string | null;
  prescriptionsError: string | null;
  patientDataError: string | null;
  
  // Actions
  fetchProfile: () => Promise<void>;
  updateProfile: (data: Partial<DoctorProfile>) => Promise<void>;
  
  fetchPatients: () => Promise<void>;
  selectPatient: (patient: DoctorPatient | null) => void;
  fetchPatientDetails: (patientId: string) => Promise<void>;
  createPatient: (data: any) => Promise<void>;
  updatePatient: (patientId: string, data: Partial<DoctorPatient>) => Promise<void>;
  
  fetchDashboardStats: () => Promise<void>;
  fetchAnalytics: () => Promise<void>;
  
  fetchPrescriptions: () => Promise<void>;
  createPrescription: (data: any) => Promise<void>;
  uploadPrescription: (file: File, patientId: string) => Promise<void>;
  
  fetchPatientMedicines: (patientId: string) => Promise<void>;
  fetchPatientAdherence: (patientId: string) => Promise<void>;
  fetchPatientAdherenceStats: (patientId: string, days?: number) => Promise<void>;
  
  // Computed getters
  getCriticalPatients: () => DoctorPatient[];
  getRecentPatients: () => DoctorPatient[];
  getPendingPrescriptions: () => ApiPrescription[];
  
  // Utility actions
  clearError: () => void;
  clearAllErrors: () => void;
  refreshAll: () => Promise<void>;
}

export const useDoctorStore = create<DoctorState>()(
  persist(
    (set, get) => ({
      // Initial state
      profile: null,
      patients: [],
      selectedPatient: null,
      dashboardStats: null,
      analytics: null,
      prescriptions: [],
      patientMedicines: [],
      patientAdherence: [],
      patientAdherenceStats: null,
      
      // Loading states
      isLoading: false,
      isLoadingProfile: false,
      isLoadingPatients: false,
      isLoadingDashboard: false,
      isLoadingAnalytics: false,
      isLoadingPrescriptions: false,
      isLoadingPatientData: false,
      
      // Error states
      error: null,
      profileError: null,
      patientsError: null,
      dashboardError: null,
      analyticsError: null,
      prescriptionsError: null,
      patientDataError: null,
      
      // Profile actions
      fetchProfile: async () => {
        set({ isLoadingProfile: true, profileError: null });
        try {
          const profile = await DoctorService.getDoctorProfile();
          set({ profile, isLoadingProfile: false });
        } catch (error: any) {
          set({ 
            profileError: error.message || 'Failed to fetch profile', 
            isLoadingProfile: false 
          });
        }
      },
      
      updateProfile: async (data: Partial<DoctorProfile>) => {
        const { profile } = get();
        if (!profile) return;
        
        set({ isLoadingProfile: true, profileError: null });
        try {
          const updatedProfile = await DoctorService.updateDoctorProfile(profile.id, data);
          set({ profile: updatedProfile, isLoadingProfile: false });
        } catch (error: any) {
          set({ 
            profileError: error.message || 'Failed to update profile', 
            isLoadingProfile: false 
          });
        }
      },
      
      // Patients actions
      fetchPatients: async () => {
        set({ isLoadingPatients: true, patientsError: null });
        try {
          const response = await DoctorService.getDoctorPatients({ limit: 1000 });
          set({ patients: response.data, isLoadingPatients: false });
        } catch (error: any) {
          set({ 
            patientsError: error.message || 'Failed to fetch patients', 
            isLoadingPatients: false 
          });
        }
      },
      
      selectPatient: (patient: DoctorPatient | null) => {
        set({ selectedPatient: patient });
      },
      
      fetchPatientDetails: async (patientId: string) => {
        set({ isLoadingPatients: true, patientsError: null });
        try {
          const patient = await DoctorService.getPatientDetails(patientId);
          const { patients } = get();
          const updatedPatients = patients.map(p => p.id === patientId ? patient : p);
          set({ patients: updatedPatients, isLoadingPatients: false });
        } catch (error: any) {
          set({ 
            patientsError: error.message || 'Failed to fetch patient details', 
            isLoadingPatients: false 
          });
        }
      },
      
      createPatient: async (data: any) => {
        set({ isLoadingPatients: true, patientsError: null });
        try {
          const newPatient = await DoctorService.createPatient(data);
          const { patients } = get();
          set({ patients: [newPatient, ...patients], isLoadingPatients: false });
        } catch (error: any) {
          set({ 
            patientsError: error.message || 'Failed to create patient', 
            isLoadingPatients: false 
          });
        }
      },
      
      updatePatient: async (patientId: string, data: Partial<DoctorPatient>) => {
        set({ isLoadingPatients: true, patientsError: null });
        try {
          const updatedPatient = await DoctorService.updatePatient(patientId, data);
          const { patients } = get();
          const updatedPatients = patients.map(p => p.id === patientId ? updatedPatient : p);
          set({ patients: updatedPatients, isLoadingPatients: false });
        } catch (error: any) {
          set({ 
            patientsError: error.message || 'Failed to update patient', 
            isLoadingPatients: false 
          });
        }
      },
      
      // Dashboard actions
      fetchDashboardStats: async () => {
        set({ isLoadingDashboard: true, dashboardError: null });
        try {
          const stats = await DoctorService.getDashboardStats();
          set({ dashboardStats: stats, isLoadingDashboard: false });
        } catch (error: any) {
          set({ 
            dashboardError: error.message || 'Failed to fetch dashboard stats', 
            isLoadingDashboard: false 
          });
        }
      },
      
      fetchAnalytics: async () => {
        set({ isLoadingAnalytics: true, analyticsError: null });
        try {
          const analytics = await DoctorService.getDoctorAnalytics();
          set({ analytics, isLoadingAnalytics: false });
        } catch (error: any) {
          set({ 
            analyticsError: error.message || 'Failed to fetch analytics', 
            isLoadingAnalytics: false 
          });
        }
      },
      
      // Prescriptions actions
      fetchPrescriptions: async () => {
        set({ isLoadingPrescriptions: true, prescriptionsError: null });
        try {
          const response = await DoctorService.getDoctorPrescriptions();
          set({ prescriptions: response.data, isLoadingPrescriptions: false });
        } catch (error: any) {
          set({ 
            prescriptionsError: error.message || 'Failed to fetch prescriptions', 
            isLoadingPrescriptions: false 
          });
        }
      },
      
      createPrescription: async (data: any) => {
        set({ isLoadingPrescriptions: true, prescriptionsError: null });
        try {
          const newPrescription = await DoctorService.createPrescription(data);
          const { prescriptions } = get();
          set({ prescriptions: [newPrescription, ...prescriptions], isLoadingPrescriptions: false });
        } catch (error: any) {
          set({ 
            prescriptionsError: error.message || 'Failed to create prescription', 
            isLoadingPrescriptions: false 
          });
        }
      },
      
      uploadPrescription: async (file: File, patientId: string) => {
        set({ isLoadingPrescriptions: true, prescriptionsError: null });
        try {
          const newPrescription = await DoctorService.uploadPrescription(file, patientId);
          const { prescriptions } = get();
          set({ prescriptions: [newPrescription, ...prescriptions], isLoadingPrescriptions: false });
        } catch (error: any) {
          set({ 
            prescriptionsError: error.message || 'Failed to upload prescription', 
            isLoadingPrescriptions: false 
          });
        }
      },
      
      // Patient-specific data actions
      fetchPatientMedicines: async (patientId: string) => {
        set({ isLoadingPatientData: true, patientDataError: null });
        try {
          const response = await DoctorService.getPatientMedicines(patientId);
          set({ patientMedicines: response.data, isLoadingPatientData: false });
        } catch (error: any) {
          set({ 
            patientDataError: error.message || 'Failed to fetch patient medicines', 
            isLoadingPatientData: false 
          });
        }
      },
      
      fetchPatientAdherence: async (patientId: string) => {
        set({ isLoadingPatientData: true, patientDataError: null });
        try {
          const response = await DoctorService.getPatientAdherence(patientId);
          set({ patientAdherence: response.data, isLoadingPatientData: false });
        } catch (error: any) {
          set({ 
            patientDataError: error.message || 'Failed to fetch patient adherence', 
            isLoadingPatientData: false 
          });
        }
      },
      
      fetchPatientAdherenceStats: async (patientId: string, days: number = 30) => {
        set({ isLoadingPatientData: true, patientDataError: null });
        try {
          const stats = await DoctorService.getPatientAdherenceAnalytics(patientId, days);
          set({ patientAdherenceStats: stats, isLoadingPatientData: false });
        } catch (error: any) {
          set({ 
            patientDataError: error.message || 'Failed to fetch adherence stats', 
            isLoadingPatientData: false 
          });
        }
      },
      
      // Computed getters
      getCriticalPatients: () => {
        const { patients } = get();
        return patients.filter(p => p.status === 'critical' || p.riskLevel === 'high');
      },
      
      getRecentPatients: () => {
        const { patients } = get();
        return patients
          .filter(p => p.lastVisit)
          .sort((a, b) => new Date(b.lastVisit!).getTime() - new Date(a.lastVisit!).getTime())
          .slice(0, 5);
      },
      
      getPendingPrescriptions: () => {
        const { prescriptions } = get();
        return prescriptions.filter(p => p.status === 'processing');
      },
      
      // Utility actions
      clearError: () => set({ error: null }),
      
      clearAllErrors: () => set({
        error: null,
        profileError: null,
        patientsError: null,
        dashboardError: null,
        analyticsError: null,
        prescriptionsError: null,
        patientDataError: null,
      }),
      
      refreshAll: async () => {
        set({ isLoading: true });
        try {
          await Promise.all([
            get().fetchProfile(),
            get().fetchPatients(),
            get().fetchDashboardStats(),
            get().fetchPrescriptions(),
          ]);
        } finally {
          set({ isLoading: false });
        }
      },
    }),
    {
      name: 'doctor-storage',
      // Only persist non-sensitive data
      partialize: (state) => ({
        selectedPatient: state.selectedPatient,
      }),
    }
  )
);
