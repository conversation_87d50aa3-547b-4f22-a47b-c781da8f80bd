import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { hospitalService } from '../api/hospitals';
import type {
  HospitalPatient,
  HospitalAdherenceReport,
  HospitalBulkAdherenceReport,
  HospitalDepartmentAnalysis,
  HospitalDashboardSummary,
  HospitalPatientsQuery,
  HospitalAdherenceReportQuery,
  HospitalBulkAdherenceQuery,
  HospitalDepartmentAnalysisQuery,
} from '../api/hospitals';

interface HospitalState {
  // Dashboard data
  dashboardSummary: HospitalDashboardSummary | null;
  isLoadingDashboard: boolean;
  dashboardError: string | null;

  // Patients data
  patients: HospitalPatient[];
  patientsTotal: number;
  patientsPage: number;
  patientsLimit: number;
  isLoadingPatients: boolean;
  patientsError: string | null;

  // Patient adherence reports
  adherenceReports: Record<string, HospitalAdherenceReport>;
  isLoadingAdherence: boolean;
  adherenceError: string | null;

  // Bulk adherence reports
  bulkAdherenceReport: HospitalBulkAdherenceReport | null;
  isLoadingBulkAdherence: boolean;
  bulkAdherenceError: string | null;

  // Department analysis
  departmentAnalyses: Record<string, HospitalDepartmentAnalysis>;
  isLoadingDepartmentAnalysis: boolean;
  departmentAnalysisError: string | null;

  // Patient summaries
  patientSummaries: Record<string, any>;
  isLoadingPatientSummary: boolean;
  patientSummaryError: string | null;

  // Actions
  fetchDashboardSummary: () => Promise<void>;
  fetchPatients: (query?: HospitalPatientsQuery) => Promise<void>;
  fetchPatientAdherenceReport: (
    patientId: string,
    query?: HospitalAdherenceReportQuery
  ) => Promise<void>;
  fetchBulkAdherenceReport: (query: HospitalBulkAdherenceQuery) => Promise<void>;
  fetchDepartmentAnalysis: (query: HospitalDepartmentAnalysisQuery) => Promise<void>;
  fetchPatientSummary: (patientId: string) => Promise<void>;
  clearErrors: () => void;
  reset: () => void;
}

const initialState = {
  // Dashboard data
  dashboardSummary: null,
  isLoadingDashboard: false,
  dashboardError: null,

  // Patients data
  patients: [],
  patientsTotal: 0,
  patientsPage: 1,
  patientsLimit: 10,
  isLoadingPatients: false,
  patientsError: null,

  // Patient adherence reports
  adherenceReports: {},
  isLoadingAdherence: false,
  adherenceError: null,

  // Bulk adherence reports
  bulkAdherenceReport: null,
  isLoadingBulkAdherence: false,
  bulkAdherenceError: null,

  // Department analysis
  departmentAnalyses: {},
  isLoadingDepartmentAnalysis: false,
  departmentAnalysisError: null,

  // Patient summaries
  patientSummaries: {},
  isLoadingPatientSummary: false,
  patientSummaryError: null,
};

export const useHospitalStore = create<HospitalState>()(
  persist(
    (set, get) => ({
      ...initialState,

      // Fetch dashboard summary
      fetchDashboardSummary: async () => {
        set({ isLoadingDashboard: true, dashboardError: null });
        try {
          const dashboardSummary = await hospitalService.getDashboardSummary();
          set({ dashboardSummary, isLoadingDashboard: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch dashboard summary';
          set({ dashboardError: errorMessage, isLoadingDashboard: false });
          console.error('Error fetching dashboard summary:', error);
        }
      },

      // Fetch patients
      fetchPatients: async (query: HospitalPatientsQuery = {}) => {
        set({ isLoadingPatients: true, patientsError: null });
        try {
          const result = await hospitalService.getHospitalPatients(query);
          set({
            patients: result.data,
            patientsTotal: result.total,
            patientsPage: result.page,
            patientsLimit: result.limit,
            isLoadingPatients: false,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch patients';
          set({ patientsError: errorMessage, isLoadingPatients: false });
          console.error('Error fetching patients:', error);
        }
      },

      // Fetch patient adherence report
      fetchPatientAdherenceReport: async (
        patientId: string,
        query: HospitalAdherenceReportQuery = {}
      ) => {
        set({ isLoadingAdherence: true, adherenceError: null });
        try {
          const report = await hospitalService.getPatientAdherenceReport(patientId, query);
          set((state) => ({
            adherenceReports: {
              ...state.adherenceReports,
              [patientId]: report,
            },
            isLoadingAdherence: false,
          }));
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch adherence report';
          set({ adherenceError: errorMessage, isLoadingAdherence: false });
          console.error('Error fetching adherence report:', error);
        }
      },

      // Fetch bulk adherence report
      fetchBulkAdherenceReport: async (query: HospitalBulkAdherenceQuery) => {
        set({ isLoadingBulkAdherence: true, bulkAdherenceError: null });
        try {
          const bulkAdherenceReport = await hospitalService.getBulkAdherenceReport(query);
          set({ bulkAdherenceReport, isLoadingBulkAdherence: false });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch bulk adherence report';
          set({ bulkAdherenceError: errorMessage, isLoadingBulkAdherence: false });
          console.error('Error fetching bulk adherence report:', error);
        }
      },

      // Fetch department analysis
      fetchDepartmentAnalysis: async (query: HospitalDepartmentAnalysisQuery) => {
        set({ isLoadingDepartmentAnalysis: true, departmentAnalysisError: null });
        try {
          const analysis = await hospitalService.getDepartmentAnalysis(query);
          set((state) => ({
            departmentAnalyses: {
              ...state.departmentAnalyses,
              [query.department]: analysis,
            },
            isLoadingDepartmentAnalysis: false,
          }));
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch department analysis';
          set({ departmentAnalysisError: errorMessage, isLoadingDepartmentAnalysis: false });
          console.error('Error fetching department analysis:', error);
        }
      },

      // Fetch patient summary
      fetchPatientSummary: async (patientId: string) => {
        set({ isLoadingPatientSummary: true, patientSummaryError: null });
        try {
          const summary = await hospitalService.getPatientSummary(patientId);
          set((state) => ({
            patientSummaries: {
              ...state.patientSummaries,
              [patientId]: summary,
            },
            isLoadingPatientSummary: false,
          }));
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch patient summary';
          set({ patientSummaryError: errorMessage, isLoadingPatientSummary: false });
          console.error('Error fetching patient summary:', error);
        }
      },

      // Clear all errors
      clearErrors: () => {
        set({
          dashboardError: null,
          patientsError: null,
          adherenceError: null,
          bulkAdherenceError: null,
          departmentAnalysisError: null,
          patientSummaryError: null,
        });
      },

      // Reset store to initial state
      reset: () => {
        set(initialState);
      },
    }),
    {
      name: 'hospital-storage',
      partialize: (state) => ({
        // Only persist non-loading states and non-error states
        dashboardSummary: state.dashboardSummary,
        patients: state.patients,
        patientsTotal: state.patientsTotal,
        patientsPage: state.patientsPage,
        patientsLimit: state.patientsLimit,
        adherenceReports: state.adherenceReports,
        bulkAdherenceReport: state.bulkAdherenceReport,
        departmentAnalyses: state.departmentAnalyses,
        patientSummaries: state.patientSummaries,
      }),
    }
  )
);
