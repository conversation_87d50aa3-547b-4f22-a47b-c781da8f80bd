// Clean API test script
const axios = require('axios');

async function main() {
  console.log('🧪 Testing API endpoints...\n');

  // Test 1: No auth
  console.log('1. Testing GET /api/v1/prescriptions (no auth):');
  try {
    await axios.get('http://localhost:3001/api/v1/prescriptions');
    console.log('✅ Unexpected success - should have failed');
  } catch (error) {
    console.log('❌ Expected auth error:', error.response?.status, error.response?.data?.message);
  }

  // Test 2: Login
  console.log('\n2. Testing patient login:');
  let token;
  try {
    const loginResponse = await axios.post('http://localhost:3001/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'password123'
    });
    console.log('✅ Login successful');
    token = loginResponse.data.access_token;
    console.log('🔑 Token received:', token.substring(0, 20) + '...');
  } catch (loginError) {
    console.log('❌ Login failed:', loginError.response?.status, loginError.response?.data);
    return;
  }

  // Test 3: Authenticated request
  console.log('\n3. Testing GET /api/v1/prescriptions (with auth):');
  try {
    const response = await axios.get('http://localhost:3001/api/v1/prescriptions', {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    console.log('✅ Authenticated request successful!');
    console.log('📊 Response:', {
      type: typeof response.data,
      isArray: Array.isArray(response.data),
      length: response.data?.length || 'N/A'
    });
    
    if (response.data && response.data.length > 0) {
      console.log('📋 First prescription:', {
        id: response.data[0].id,
        filename: response.data[0].filename,
        status: response.data[0].status
      });
    } else {
      console.log('📋 No prescriptions found');
    }
  } catch (error) {
    console.log('❌ Auth request failed:', error.response?.status, error.response?.data);
  }
}

main().catch(console.error);
